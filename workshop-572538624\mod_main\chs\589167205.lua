local s = STRINGS

s.CHARACTER_TITLES.spy = "无所不在的坏人"
s.CHARACTER_NAMES.spy = "间谍"
s.CHARACTER_DESCRIPTIONS.spy = "*他有信赖的隐形装置\n*喜欢用他独特的刀\n*讨厌腐败食物"
s.CHARACTER_QUOTES.spy = "\"我从来没有真的在你这边。\""
--s.SKIN_QUOTES.spy_blu = "\"Gentlemen.\""
--s.SKIN_NAMES.spy_blu = "Blu"

s.NAMES.SPY = "间谍"

s.CHARACTERS.SPY = nil

--s.CHARACTERS.SPY.DESCRIBE.INVISWATCH = "This will be the last time you see me."
s.CHARACTERS.GENERIC.DESCRIBE.INVISWATCH = "多么科学的噩梦手表！"
s.CHARACTERS.WILLOW.DESCRIBE.INVISWATCH = nil --"It would look prettier on fire."
s.CHARACTERS.WOLFGANG.DESCRIBE.INVISWATCH = nil --"Is too tiny for wrist."
s.CHARACTERS.WENDY.DESCRIBE.INVISWATCH = nil --nil --"You can't hide forever."
s.CHARACTERS.WX78.DESCRIBE.INVISWATCH = nil --nil --"INFERIOR TECHNOLOGY DETECTED"
s.CHARACTERS.WICKERBOTTOM.DESCRIBE.INVISWATCH = nil --nil --"It seems to be a highly developed sci-fi espionage wristwatch."
s.CHARACTERS.WOODIE.DESCRIBE.INVISWATCH = nil --nil --"I'm not the biggest fan of watches, eh."
s.CHARACTERS.WAXWELL.DESCRIBE.INVISWATCH = nil --nil --"A cheater's contraption."

s.NAMES.INVISWATCH = "隐形手表"

--s.CHARACTERS.SPY.DESCRIBE.KNIFE = "Shock, blood loss, infection; Oh ho ho ho, I love stabbing!"
s.CHARACTERS.GENERIC.DESCRIBE.KNIFE = "刺杀微小的敌人！"
s.CHARACTERS.WILLOW.DESCRIBE.KNIFE = nil --"It's pokey!"
s.CHARACTERS.WOLFGANG.DESCRIBE.KNIFE = nil --"Is sharp and small!"
s.CHARACTERS.WENDY.DESCRIBE.KNIFE = nil --"Sharp and ready to kill."
s.CHARACTERS.WX78.DESCRIBE.KNIFE = nil --"THIS KNIFE IS SHARPER THAN EXPECTED"
s.CHARACTERS.WICKERBOTTOM.DESCRIBE.KNIFE = nil --"The hinges of this balisong appear to be cemented."
s.CHARACTERS.WOODIE.DESCRIBE.KNIFE = nil --"Why use a tiny knife when you have an axe?"
s.CHARACTERS.WAXWELL.DESCRIBE.KNIFE = nil --"Yup, a pocket knife."

--s.CHARACTERS.SPY.DESCRIBE.GOLDENKNIFE = "It's the only one of it's kind! And it's mine!"
s.CHARACTERS.GENERIC.DESCRIBE.GOLDENKNIFE = "穿刺从未如此花哨！"
s.CHARACTERS.WILLOW.DESCRIBE.GOLDENKNIFE = nil --"It won't light people on fire, but at least it's shiny."
s.CHARACTERS.WOLFGANG.DESCRIBE.GOLDENKNIFE = nil --"Fancy knife good for stab."
s.CHARACTERS.WENDY.DESCRIBE.GOLDENKNIFE = nil --"At least I can kill with style."
s.CHARACTERS.WX78.DESCRIBE.GOLDENKNIFE = nil --"GOLD IS MORE DURABLE?"
s.CHARACTERS.WICKERBOTTOM.DESCRIBE.GOLDENKNIFE = nil --"The most malleable of metals, but let's see how this punctures."
s.CHARACTERS.WOODIE.DESCRIBE.GOLDENKNIFE = nil --"It's pretty but it can't chop down trees."
s.CHARACTERS.WAXWELL.DESCRIBE.GOLDENKNIFE = nil --"It gets sharper with every jab."

--s.CHARACTERS.SPY.DESCRIBE.HAT_BOX = "Oh hohoho!"
s.CHARACTERS.GENERIC.DESCRIBE.HAT_BOX = "为了你所有穿的盒子！"
s.CHARACTERS.WILLOW.DESCRIBE.HAT_BOX = nil --"I bet it shimmers when burnt."
s.CHARACTERS.WOLFGANG.DESCRIBE.HAT_BOX = nil --"I'm so sneaky!"
s.CHARACTERS.WENDY.DESCRIBE.HAT_BOX = nil --"To disappear to those who are foolish enough to believe it."
s.CHARACTERS.WX78.DESCRIBE.HAT_BOX = nil --"HOW DEMEANING"
s.CHARACTERS.WICKERBOTTOM.DESCRIBE.HAT_BOX = nil --"Comical camouflage."
s.CHARACTERS.WOODIE.DESCRIBE.HAT_BOX = nil --"It's good for hiding from nature."
s.CHARACTERS.WAXWELL.DESCRIBE.HAT_BOX = nil --"There's room in there for me."

--s.CHARACTERS.SPY.DESCRIBE.SPYKUNAI = "Ah, stab wounds. My favorite type of wound!"
s.CHARACTERS.GENERIC.DESCRIBE.SPYKUNAI = "我必须学会如何使用这个忍者武器。"
s.CHARACTERS.WILLOW.DESCRIBE.SPYKUNAI = nil --"I don't know how to use this dumb thing!"
s.CHARACTERS.WOLFGANG.DESCRIBE.SPYKUNAI = nil --"Is a small weapon."
s.CHARACTERS.WENDY.DESCRIBE.SPYKUNAI = nil --"An old conniver's murdering tool."
s.CHARACTERS.WX78.DESCRIBE.SPYKUNAI = nil --"BEST USED AGAINST FLESHLINGS"
s.CHARACTERS.WICKERBOTTOM.DESCRIBE.SPYKUNAI = nil --"An ancient Japanese trowel. It can also be used as a multi-functional weapon."
s.CHARACTERS.WOODIE.DESCRIBE.SPYKUNAI = nil --"How do I use this, eh?"
s.CHARACTERS.WAXWELL.DESCRIBE.SPYKUNAI = nil --"Ah, the ancient gardening tool turned weapon."

--s.CHARACTERS.SPY.DESCRIBE.SPYCICLE = "Time for the killing stroke!"
s.CHARACTERS.GENERIC.DESCRIBE.SPYCICLE = "这当武器是非常危险的。"
s.CHARACTERS.WILLOW.DESCRIBE.SPYCICLE = nil --"Cold ice can leave a nasty burn, I suppose."
s.CHARACTERS.WOLFGANG.DESCRIBE.SPYCICLE = nil --"Is cold and sharp!"
s.CHARACTERS.WENDY.DESCRIBE.SPYCICLE = nil --"To put a cold dark end to whomever dares test it."
s.CHARACTERS.WX78.DESCRIBE.SPYCICLE = nil --"NOW THIS IS HOW YOU USE WATER"
s.CHARACTERS.WICKERBOTTOM.DESCRIBE.SPYCICLE = nil --"These are known for their horrific outcomes when falling onto a person. Be careful."
s.CHARACTERS.WOODIE.DESCRIBE.SPYCICLE = nil --"These things were everywhere back home."
s.CHARACTERS.WAXWELL.DESCRIBE.SPYCICLE = nil --"Now that's how you make use of an icicle."

s.NAMES.HAT_BOX = "盒子戏法"
_G.ChinesePlus.RenameRecipe("HAT_BOX", "原来的隐形装置。")

s.NAMES.KNIFE = "蝴蝶刀"
_G.ChinesePlus.RenameRecipe("KNIFE", "不玩阴的可以做这个。")

s.NAMES.GOLDENKNIFE = "澳元素刀"
_G.ChinesePlus.RenameRecipe("GOLDENKNIFE", "不是真的澳元素刀。")

s.NAMES.SPYKUNAI = "密谋者的苦无"
_G.ChinesePlus.RenameRecipe("SPYKUNAI", "从倒下的敌人吸取生命。")

s.NAMES.SPYCICLE = "寒冽冰锥"
_G.ChinesePlus.RenameRecipe("SPYCICLE", "冰重组为杀人凶器。")

s.CHARACTERS.GENERIC.DESCRIBE.SPY =
{
  GENERIC = "你好, %s!",
  ATTACKER = "那个 %s 看起来很狡猾...",
  MURDERER = "凶手!",
  REVIVER = "%s 在使用他的力量。",
  GHOST = "别那样看看我。, %s! 我正在努力。",
  FIRESTARTER = "%s 只是要求了烧烤。",
}

STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.SPY = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.SPY = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.SPY = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.SPY = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.SPY = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.SPY = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.SPY = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.SPY = nil

