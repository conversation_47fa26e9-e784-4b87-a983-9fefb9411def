-- The character select screen lines

STRINGS.CHARACTER_TITLES.houndplayer = "獵犬"
STRINGS.CHARACTER_NAMES.houndplayer = "獵犬"
STRINGS.CHARACTER_DESCRIPTIONS.houndplayer = "*怪物。\n*血量比其他獵犬。\n* 撕咬削弱。"
STRINGS.CHARACTER_QUOTES.houndplayer = "嗚~嗚~嗚~."

STRINGS.CHARACTER_TITLES.houndiceplayer = "冰獵犬"
STRINGS.CHARACTER_NAMES.houndiceplayer = "冰獵犬"
STRINGS.CHARACTER_DESCRIPTIONS.houndiceplayer = "*怪物。\n*喜歡嚴寒。\n* 強力撕咬。"
STRINGS.CHARACTER_QUOTES.houndiceplayer = "汪~汪~汪~."

STRINGS.CHARACTER_TITLES.houndredplayer = "火獵犬"
STRINGS.CHARACTER_NAMES.houndredplayer = "火獵犬"
STRINGS.CHARACTER_DESCRIPTIONS.houndredplayer = "*怪物。\n*可以耐受酷熱。\n* 強力撕咬。"
STRINGS.CHARACTER_QUOTES.houndredplayer = "嗷~嗷~嗷~"

STRINGS.CHARACTER_TITLES.hounddarkplayer = "噩夢獵犬"
STRINGS.CHARACTER_NAMES.hounddarkplayer = "噩夢獵犬"
STRINGS.CHARACTER_DESCRIPTIONS.hounddarkplayer = "*怪物（測試）。\n*有精神值。\n* 睡覺來保持精神。"
STRINGS.CHARACTER_QUOTES.hounddarkplayer = "嚎~嚎~嚎~"

STRINGS.CHARACTER_TITLES.whitespiderplayer = "深度居住者"
STRINGS.CHARACTER_NAMES.whitespiderplayer = "白蜘蛛"
STRINGS.CHARACTER_DESCRIPTIONS.whitespiderplayer = "*有點討厭。 \n* 特別夜視儀。\n* 令人毛骨悚然。"
STRINGS.CHARACTER_QUOTES.whitespiderplayer = "*凝視中*"

STRINGS.CHARACTER_TITLES.blackspiderplayer = "蜘蛛"
STRINGS.CHARACTER_NAMES.blackspiderplayer = "蜘蛛"
STRINGS.CHARACTER_DESCRIPTIONS.blackspiderplayer = "*可製造蛛網。\n*很萌。\n* 不是很強。。"
STRINGS.CHARACTER_QUOTES.blackspiderplayer = "咔擦~咔擦~咔擦~"

STRINGS.CHARACTER_TITLES.warriorp = "蜘蛛勇士"
STRINGS.CHARACTER_NAMES.warriorp = "蜘蛛勇士"
STRINGS.CHARACTER_DESCRIPTIONS.warriorp = "*可以製作蜘蛛腺體。\n*更好的健康恢復。\n* 可以向敵人衝刺。"
STRINGS.CHARACTER_QUOTES.warriorp = "咔擦~咔擦~咔擦~"

STRINGS.CHARACTER_TITLES.queenspiderplayer = "蜘蛛女王"
STRINGS.CHARACTER_NAMES.queenspiderplayer = "蜘蛛女王"
STRINGS.CHARACTER_DESCRIPTIONS.queenspiderplayer = "*可以產卵\n*可以生成的嘍羅。\n*嘍羅並不是真正友好..."
STRINGS.CHARACTER_QUOTES.queenspiderplayer = "農場主"

STRINGS.CHARACTER_TITLES.wqueenspiderplayer = "居住者女王"
STRINGS.CHARACTER_NAMES.wqueenspiderplayer = "白蜘蛛女王"
STRINGS.CHARACTER_DESCRIPTIONS.wqueenspiderplayer = "*可以製作蜘蛛腺體。\n*可以生成居民。\n* 特別夜視儀。"
STRINGS.CHARACTER_QUOTES.wqueenspiderplayer = "看起來很髒的農場主"

STRINGS.CHARACTER_TITLES.moosegooseplayer = "鹿角鵝"
STRINGS.CHARACTER_NAMES.moosegooseplayer = "鹿角鵝"
STRINGS.CHARACTER_DESCRIPTIONS.moosegooseplayer = "*大BOSS。\n*能下蛋。\n* 掉落羽毛。"
STRINGS.CHARACTER_QUOTES.moosegooseplayer = "HONK-鵝叫"

STRINGS.CHARACTER_TITLES.babygooseplayer = "莫斯林"
STRINGS.CHARACTER_NAMES.babygooseplayer = "莫斯林"
STRINGS.CHARACTER_DESCRIPTIONS.babygooseplayer = "*非常可愛。\n*有脾氣。 \n* 生氣了..."
STRINGS.CHARACTER_QUOTES.babygooseplayer = "meep - 咩"

STRINGS.CHARACTER_TITLES.dragonplayer = "龍蠅"
STRINGS.CHARACTER_NAMES.dragonplayer = "龍蠅"
STRINGS.CHARACTER_DESCRIPTIONS.dragonplayer = "*大BOSS。\n*脾氣暴躁。\n*晚上很累。"
STRINGS.CHARACTER_QUOTES.dragonplayer = "*嗡~~嗡~~嗡~~*"

STRINGS.CHARACTER_TITLES.maggotplayer = "熔岩蟲"
STRINGS.CHARACTER_NAMES.maggotplayer = "熔岩蟲"
STRINGS.CHARACTER_DESCRIPTIONS.maggotplayer = "*覆蓋著溫暖的熔岩！ \n* 討厭下雨... \n* 並不強大..."
STRINGS.CHARACTER_QUOTES.maggotplayer = "像只蛆？"

STRINGS.CHARACTER_TITLES.bearplayer = "比格爾"
STRINGS.CHARACTER_NAMES.bearplayer = "比格爾"
STRINGS.CHARACTER_DESCRIPTIONS.bearplayer = "*大BOSS。\n*可以砸向地面。\n*掉毛。"
STRINGS.CHARACTER_QUOTES.bearplayer = "怒吼"

STRINGS.CHARACTER_TITLES.deerplayer = "鉅鹿"
STRINGS.CHARACTER_NAMES.deerplayer = "鉅鹿"
STRINGS.CHARACTER_DESCRIPTIONS.deerplayer = "*大BOSS。\n*致命 \n*可怕..."
STRINGS.CHARACTER_QUOTES.deerplayer = "它怎麼叫？"

STRINGS.CHARACTER_TITLES.catplayer = "浣貓"
STRINGS.CHARACTER_NAMES.catplayer = "浣貓"
STRINGS.CHARACTER_DESCRIPTIONS.catplayer = "*精通狩獵\n*可以收穫農作物。 \n*很可愛。"
STRINGS.CHARACTER_QUOTES.catplayer = "喵~"

STRINGS.CHARACTER_TITLES.ghostplayer = "鬼魂"
STRINGS.CHARACTER_NAMES.ghostplayer = "鬼魂"
STRINGS.CHARACTER_DESCRIPTIONS.ghostplayer = "*可以建造。 \n*發光。\n*可以隱形。"
STRINGS.CHARACTER_QUOTES.ghostplayer = "OOOoooOOOoooo"

STRINGS.CHARACTER_TITLES.mermplayer = "魚人"
STRINGS.CHARACTER_NAMES.mermplayer = "魚人"
STRINGS.CHARACTER_DESCRIPTIONS.mermplayer = "*可以建造。 \n*喜歡下雨。 \n*怪物。"
STRINGS.CHARACTER_QUOTES.mermplayer = "汩汩!"

STRINGS.CHARACTER_TITLES.pigmanplayer = "豬人"
STRINGS.CHARACTER_NAMES.pigmanplayer = "豬人"
STRINGS.CHARACTER_DESCRIPTIONS.pigmanplayer = "*可以建造。 \n*I一頭豬。 \n*有一個可怕的祕密..."
STRINGS.CHARACTER_QUOTES.pigmanplayer = "哼!"

STRINGS.CHARACTER_TITLES.walrusplayer = "海象"
STRINGS.CHARACTER_NAMES.walrusplayer = "海象"
STRINGS.CHARACTER_DESCRIPTIONS.walrusplayer = "*可以建造。 \n*喜歡冬天，討厭炎熱。 \n* 掉落吹箭。"
STRINGS.CHARACTER_QUOTES.walrusplayer = "我..說.."

STRINGS.CHARACTER_TITLES.beefplayer = "皮弗婁牛"
STRINGS.CHARACTER_NAMES.beefplayer = "皮弗婁牛"
STRINGS.CHARACTER_DESCRIPTIONS.beefplayer = "*健壯。 \n*生小牛。 \n*能拉屎。"
STRINGS.CHARACTER_QUOTES.beefplayer = "?"

STRINGS.CHARACTER_TITLES.vargplayer = "座狼"
STRINGS.CHARACTER_NAMES.vargplayer = "座狼"
STRINGS.CHARACTER_DESCRIPTIONS.vargplayer = "*健壯。\n*召喚獵犬。 \n*掉落毛髮。"
STRINGS.CHARACTER_QUOTES.vargplayer = "嗚~嗚~嗚~"

STRINGS.CHARACTER_TITLES.smallbirdp = "小高腳鳥"
STRINGS.CHARACTER_NAMES.smallbirdp = "小高腳鳥"
STRINGS.CHARACTER_DESCRIPTIONS.smallbirdp = "*很可愛。\n*可以長大。 \n*並不強壯..."
STRINGS.CHARACTER_QUOTES.smallbirdp = "嘰~嘰~"

STRINGS.CHARACTER_TITLES.tallbirdplayer = "高腳鳥"
STRINGS.CHARACTER_NAMES.tallbirdplayer = "高腳鳥"
STRINGS.CHARACTER_DESCRIPTIONS.tallbirdplayer = "*很快。 \n*很生氣。 \n*自豪的父母。"
STRINGS.CHARACTER_QUOTES.tallbirdplayer = "嘰~嘰~嘰~"

STRINGS.CHARACTER_TITLES.clockwork1player = "發條騎士"
STRINGS.CHARACTER_NAMES.clockwork1player = "發條騎士"
STRINGS.CHARACTER_DESCRIPTIONS.clockwork1player = "*是發條！ \n*擁有夜視功能。\n* 可以進行基礎維修。"
STRINGS.CHARACTER_QUOTES.clockwork1player = "嘶~嘶~"

STRINGS.CHARACTER_TITLES.clockwork2player = "發條主教"
STRINGS.CHARACTER_NAMES.clockwork2player = "發條主教"
STRINGS.CHARACTER_DESCRIPTIONS.clockwork2player = "*具有遠端攻擊！ \n*擁有夜視功能。 \n* 可以進行基礎維修。"
STRINGS.CHARACTER_QUOTES.clockwork2player = "茲~"

STRINGS.CHARACTER_TITLES.clockwork3player = "機械戰車"
STRINGS.CHARACTER_NAMES.clockwork3player = "機械戰車"
STRINGS.CHARACTER_DESCRIPTIONS.clockwork3player = "*體積大！ \n*擁有夜視功能。 \n* 極具破壞性。"
STRINGS.CHARACTER_QUOTES.clockwork3player = "框遲框遲~"

STRINGS.CHARACTER_TITLES.treeplayer = "樹精守衛"
STRINGS.CHARACTER_NAMES.treeplayer = "樹精守衛"
STRINGS.CHARACTER_DESCRIPTIONS.treeplayer = "*體積大！ \n* 幫助自然。 \n*很慢..."
STRINGS.CHARACTER_QUOTES.treeplayer = "..."

----------------------------------------------------------------
-- The character's name as appears in-game
STRINGS.NAMES.HOUNDPLAYER = "獵犬"
STRINGS.NAMES.HOUNDICEPLAYER = "冰獵犬"
STRINGS.NAMES.HOUNDREDPLAYER = "火獵犬"
STRINGS.NAMES.HOUNDDARKPLAYER = "噩夢獵犬"
STRINGS.NAMES.BLACKSPIDERPLAYER = "蜘蛛"
STRINGS.NAMES.WHITESPIDERPLAYER = "白蜘蛛"
STRINGS.NAMES.QUEENSPIDERPLAYER = "蜘蛛女王"
STRINGS.NAMES.WQUEENSPIDERPLAYER = "白蜘蛛女王"
STRINGS.NAMES.MOOSEGOOSEPLAYER = "鹿角鵝"
STRINGS.NAMES.BABYGOOSEPLAYER = "莫斯林"
STRINGS.NAMES.DRAGONPLAYER = "龍蠅"
STRINGS.NAMES.MAGGOTPLAYER = "熔岩蟲"
STRINGS.NAMES.BEARPLAYER = "比格爾"
STRINGS.NAMES.DEERPLAYER = "鉅鹿"
STRINGS.NAMES.CATPLAYER = "浣貓"
STRINGS.NAMES.GHOSTPLAYER = "鬼魂"
STRINGS.NAMES.MERMPLAYER = "魚人"
STRINGS.NAMES.PIGMANPLAYER = "豬人"
STRINGS.NAMES.WALRUSPLAYER = "海象"
STRINGS.NAMES.BEEFPLAYER = "皮弗婁牛"
STRINGS.NAMES.VARGPLAYER = "座狼"
STRINGS.NAMES.TALLBIRDPLAYER = "高腳鳥"
STRINGS.NAMES.CLOCKWORK1PLAYER = "發條騎士"
STRINGS.NAMES.CLOCKWORK2PLAYER = "發條主教"
STRINGS.NAMES.CLOCKWORK3PLAYER = "機械戰車"
STRINGS.NAMES.TREEPLAYER = "樹精守衛"
---------
STRINGS.NAMES.MERMHOUSE_PLAYER = "魚族廢墟"
STRINGS.NAMES.PIGHOUSE_PLAYER = "豬舍"
STRINGS.NAMES.IGLOO_PLAYER = "海象巢穴"
--STRINGS.NAMES.SPIDERNEST_P = "蜘蛛巢穴"
----------
STRINGS.NAMES.MONSTER_WPN = "神祕力量"

----------------------------------------------------------------
-- Custom speech strings

STRINGS.CHARACTERS.PIGMANPLAYER = require(chinesefolder.."/PlayablePets/speech_pigmanplayer")

----------------------------------------------------------------
-- The default responses of examining the prefab

STRINGS.CHARACTERS.GENERIC.DESCRIBE.MERMHOUSE_PLAYER =
{
  GENERIC = "這房子口味比較重！",
  BUNRT = "現在是無用的，臭的。",
}

STRINGS.CHARACTERS.GENERIC.DESCRIBE.PIGHOUSE_PLAYER =
{
  GENERIC = "一個豬的房子。",
  BURNT = "今晚沒有人睡。",
}

STRINGS.CHARACTERS.GENERIC.DESCRIBE.IGLOO_PLAYER =
{
  GENERIC = "我希望建立了這個的人是友好的。",
  BURNT = "我希望建立了這個的人現在還是友好的。",
}

STRINGS.CHARACTERS.GENERIC.DESCRIBE.MONSTER_WPN =
{
  GENERIC = "我真的不應該有這個...",
}
---------------------------------------------------------------------------
