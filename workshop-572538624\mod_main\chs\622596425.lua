STRINGS.CHARACTERS.BUNNYP = require(chinesefolder.."/PlayablePets/speech_bunnyp")

-- The character select screen lines
----------------------------------------------------------
STRINGS.CHARACTER_TITLES.shadow2player = "尖喙恐惧化身"
STRINGS.CHARACTER_NAMES.shadow2player = "尖喙恐惧化身"
STRINGS.CHARACTER_DESCRIPTIONS.shadow2player = "*对正常人和怪物是看不见的。\n* 总是看着你..."
STRINGS.CHARACTER_QUOTES.shadow2player = "它们"

STRINGS.CHARACTER_TITLES.wormp = "深渊蠕虫"
STRINGS.CHARACTER_NAMES.wormp = "深渊蠕虫"
STRINGS.CHARACTER_DESCRIPTIONS.wormp = "*挖掘地下。\n* 有一种诱惑。"
STRINGS.CHARACTER_QUOTES.wormp = "*隆 隆*"

STRINGS.CHARACTER_TITLES.batp = "黑蝙蝠"
STRINGS.CHARACTER_NAMES.batp = "黑蝙蝠"
STRINGS.CHARACTER_DESCRIPTIONS.batp = "*飞行！\n* 拉屎。 \n* 不是很强..."
STRINGS.CHARACTER_QUOTES.batp = "会飞的老鼠"

STRINGS.CHARACTER_TITLES.cspiderp = "喷吐虫"
STRINGS.CHARACTER_NAMES.cspiderp = "喷吐虫"
STRINGS.CHARACTER_DESCRIPTIONS.cspiderp = "*吐唾沫。 \n*是一只蜘蛛。 \n*比一般的蜘蛛更强一点。"
STRINGS.CHARACTER_QUOTES.cspiderp = "呸~呸~呸~"

STRINGS.CHARACTER_TITLES.cspider2p = "洞穴蜘蛛"
STRINGS.CHARACTER_NAMES.cspider2p = "洞穴蜘蛛"
STRINGS.CHARACTER_DESCRIPTIONS.cspider2p = "*可以隐藏。 \n*掉落石头。 \n*一只庞大的蜘蛛.."
STRINGS.CHARACTER_QUOTES.cspider2p = "仍然讨厌捉迷藏"

STRINGS.CHARACTER_TITLES.monkeyp = "暴躁猴"
STRINGS.CHARACTER_NAMES.monkeyp = "暴躁猴"
STRINGS.CHARACTER_DESCRIPTIONS.monkeyp = "*便便武器 \n* 有手。 \n*只吃蔬菜 "
STRINGS.CHARACTER_QUOTES.monkeyp = "我的猴子的便桶在哪里？"

STRINGS.CHARACTER_TITLES.slurperp = "缀食者"
STRINGS.CHARACTER_NAMES.slurperp = "缀食者"
STRINGS.CHARACTER_DESCRIPTIONS.slurperp = "*是个贪吃的人。 \n* 给出光亮。 \n*便便灯泡。"
STRINGS.CHARACTER_QUOTES.slurperp = "吃"

STRINGS.CHARACTER_TITLES.bunnyp = "兔男"
STRINGS.CHARACTER_NAMES.bunnyp = "兔男"
STRINGS.CHARACTER_DESCRIPTIONS.bunnyp = "*是素食主义者。 \n*在黑暗中能看见。 \n*有一个可怕的秘密。"
STRINGS.CHARACTER_QUOTES.bunnyp = "吃肉就是谋杀！"

STRINGS.CHARACTER_TITLES.rocklobsterp = "岩石大龙虾"
STRINGS.CHARACTER_NAMES.rocklobsterp = "岩石大龙虾"
STRINGS.CHARACTER_DESCRIPTIONS.rocklobsterp = "*超笨重和缓慢。 \n* 可以隐藏。 \n*吃石头。"
STRINGS.CHARACTER_QUOTES.rocklobsterp = "..."

STRINGS.CHARACTER_TITLES.guardianp = "远古守护者"
STRINGS.CHARACTER_NAMES.guardianp = "远古守护者"
STRINGS.CHARACTER_DESCRIPTIONS.guardianp = "*大BOSS \n* 极具破坏性。 \n*很好的防护。"
STRINGS.CHARACTER_QUOTES.guardianp = "..."

-- The character's name as appears in-game
STRINGS.NAMES.MONKEYBARREL_P = "暴躁猴群"
STRINGS.NAMES.MONKEYHOUSE = "暴躁猴群"

STRINGS.NAMES.RABBITHOUSE_PLAYER = "兔子窝"
STRINGS.NAMES.CMONSTER_WPN = "神秘的力量"
-- The default responses of examining the prefab

STRINGS.CHARACTERS.GENERIC.DESCRIBE.RABBITHOUSE_PLAYER =
{
  GENERIC = "闻起来像胡萝卜。",
  BURNT = "闻起来像烧焦的胡萝卜...和兔子。",
}

STRINGS.CHARACTERS.GENERIC.DESCRIBE.CMONSTER_WPN =
{
  GENERIC = "我不应该有这个...",
}

