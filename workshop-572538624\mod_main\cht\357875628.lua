if STRINGS and STRINGS.CHARACTERS ~= nil then
  for k,v in pairs(STRINGS.CHARACTERS) do
    v.DESCRIBE.MECH_HAY_ITEM = nil
    v.DESCRIBE.MECH_HAY = nil
    v.DESCRIBE.MECH_WOOD_ITEM = nil
    v.DESCRIBE.MECH_WOOD = nil
    v.DESCRIBE.MECH_STONE_ITEM = nil
    v.DESCRIBE.MECH_STONE = nil
    v.DESCRIBE.MECH_RUINS_ITEM = nil
    v.DESCRIBE.MECH_RUINS = nil
    v.DESCRIBE.MECH_MOONROCK_ITEM = nil
    v.DESCRIBE.MECH_MOONROCK = nil
    v.DESCRIBE.LOCKED_MECH_STONE_ITEM = nil
    v.DESCRIBE.LOCKED_MECH_STONE = nil
    v.DESCRIBE.LOCKED_MECH_RUINS_ITEM = nil
    v.DESCRIBE.LOCKED_MECH_RUINS = nil
    v.DESCRIBE.LOCKED_MECH_MOONROCK_ITEM = nil
    v.DESCRIBE.LOCKED_MECH_MOONROCK = nil
  end
end

STRINGS.NAMES.MECH_HAY_ITEM = "乾草門"
_G.ChinesePlus.RenameRecipe("MECH_HAY_ITEM", "乾草做的門.")

STRINGS.NAMES.MECH_WOOD_ITEM = "木門"
_G.ChinesePlus.RenameRecipe("MECH_WOOD_ITEM", "木頭做的門.")

STRINGS.NAMES.MECH_STONE_ITEM = "石門"
_G.ChinesePlus.RenameRecipe("MECH_STONE_ITEM", "石頭做的門.")

STRINGS.NAMES.MECH_RUINS_ITEM = "銩礦門"
_G.ChinesePlus.RenameRecipe("MECH_RUINS_ITEM", "銩礦做的門.")

STRINGS.NAMES.MECH_MOONROCK_ITEM = "月岩門"
_G.ChinesePlus.RenameRecipe("MECH_MOONROCK_ITEM", "月岩做的門.")

STRINGS.NAMES.MECH_HAY = "乾草門"
STRINGS.NAMES.MECH_WOOD = "木門"
STRINGS.NAMES.MECH_STONE = "石門"
STRINGS.NAMES.MECH_RUINS = "銩礦門"
STRINGS.NAMES.MECH_MOONROCK = "月岩門"

STRINGS.NAMES.LOCKED_MECH_STONE_ITEM = "上鎖的石門"
_G.ChinesePlus.RenameRecipe("LOCKED_MECH_STONE_ITEM", "上鎖的石頭做的門")

STRINGS.NAMES.LOCKED_MECH_RUINS_ITEM = "上鎖的銩礦門"
_G.ChinesePlus.RenameRecipe("LOCKED_MECH_RUINS_ITEM", "上鎖的銩礦做的門")

STRINGS.NAMES.LOCKED_MECH_MOONROCK_ITEM = "上鎖的月岩門"
_G.ChinesePlus.RenameRecipe("LOCKED_MECH_MOONROCK_ITEM", "上鎖的月岩做的門")

STRINGS.NAMES.LOCKED_MECH_STONE = "上鎖的石門"
STRINGS.NAMES.LOCKED_MECH_RUINS = "上鎖的銩礦門"
STRINGS.NAMES.LOCKED_MECH_MOONROCK = "上鎖的月岩門"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.MECH_HAY_ITEM = "手工仔細編制."
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MECH_HAY = "看起來還是不怎麼牢靠."
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MECH_WOOD_ITEM = "繩索加木板!"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MECH_WOOD = "移動的板子!"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MECH_STONE_ITEM = "這能讓我快進快出."
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MECH_STONE = "不是很隱蔽."
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MECH_RUINS_ITEM = "比看起來結實."
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MECH_RUINS = "不知道還有什麼改進的餘地."
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MECH_MOONROCK_ITEM = "不可思議. 不是很重的岩石."
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MECH_MOONROCK = "如果這都不能阻擋它們那就沒什麼可以了."
STRINGS.CHARACTERS.GENERIC.DESCRIBE.LOCKED_MECH_STONE_ITEM = "這能讓我快進快出"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.LOCKED_MECH_STONE = "不是很隱蔽."
STRINGS.CHARACTERS.GENERIC.DESCRIBE.LOCKED_MECH_RUINS_ITEM = "比看起來結實."
STRINGS.CHARACTERS.GENERIC.DESCRIBE.LOCKED_MECH_RUINS = "不知道還有什麼改進的餘地."
STRINGS.CHARACTERS.GENERIC.DESCRIBE.LOCKED_MECH_MOONROCK_ITEM = "不可思議. 不是很重的岩石."
STRINGS.CHARACTERS.GENERIC.DESCRIBE.LOCKED_MECH_MOONROCK = "不可思議. 不是很重的岩石."

_G.ChinesePlus.RenameAction("CLOSE","關門")
_G.ChinesePlus.RenameAction("OPEN","開門")
_G.ChinesePlus.RenameAction("UNLOCK","開鎖")
_G.ChinesePlus.RenameAction("LOCK","上鎖")
