-- The character select screen lines

STRINGS.CHARACTER_TITLES.houndplayer = "猎犬"
STRINGS.CHARACTER_NAMES.houndplayer = "猎犬"
STRINGS.CHARACTER_DESCRIPTIONS.houndplayer = "*怪物。\n*血量比其他猎犬。\n* 撕咬削弱。"
STRINGS.CHARACTER_QUOTES.houndplayer = "呜~呜~呜~."

STRINGS.CHARACTER_TITLES.houndiceplayer = "冰猎犬"
STRINGS.CHARACTER_NAMES.houndiceplayer = "冰猎犬"
STRINGS.CHARACTER_DESCRIPTIONS.houndiceplayer = "*怪物。\n*喜欢严寒。\n* 强力撕咬。"
STRINGS.CHARACTER_QUOTES.houndiceplayer = "汪~汪~汪~."

STRINGS.CHARACTER_TITLES.houndredplayer = "火猎犬"
STRINGS.CHARACTER_NAMES.houndredplayer = "火猎犬"
STRINGS.CHARACTER_DESCRIPTIONS.houndredplayer = "*怪物。\n*可以耐受酷热。\n* 强力撕咬。"
STRINGS.CHARACTER_QUOTES.houndredplayer = "嗷~嗷~嗷~"

STRINGS.CHARACTER_TITLES.hounddarkplayer = "噩梦猎犬"
STRINGS.CHARACTER_NAMES.hounddarkplayer = "噩梦猎犬"
STRINGS.CHARACTER_DESCRIPTIONS.hounddarkplayer = "*怪物（测试）。\n*有精神值。\n* 睡觉来保持精神。"
STRINGS.CHARACTER_QUOTES.hounddarkplayer = "嚎~嚎~嚎~"

STRINGS.CHARACTER_TITLES.whitespiderplayer = "深度居住者"
STRINGS.CHARACTER_NAMES.whitespiderplayer = "白蜘蛛"
STRINGS.CHARACTER_DESCRIPTIONS.whitespiderplayer = "*有点讨厌。 \n* 特别夜视仪。\n* 令人毛骨悚然。"
STRINGS.CHARACTER_QUOTES.whitespiderplayer = "*凝视中*"

STRINGS.CHARACTER_TITLES.blackspiderplayer = "蜘蛛"
STRINGS.CHARACTER_NAMES.blackspiderplayer = "蜘蛛"
STRINGS.CHARACTER_DESCRIPTIONS.blackspiderplayer = "*可制造蛛网。\n*很萌。\n* 不是很强。。"
STRINGS.CHARACTER_QUOTES.blackspiderplayer = "咔擦~咔擦~咔擦~"

STRINGS.CHARACTER_TITLES.warriorp = "蜘蛛勇士"
STRINGS.CHARACTER_NAMES.warriorp = "蜘蛛勇士"
STRINGS.CHARACTER_DESCRIPTIONS.warriorp = "*可以制作蜘蛛腺体。\n*更好的健康恢复。\n* 可以向敌人冲刺。"
STRINGS.CHARACTER_QUOTES.warriorp = "咔擦~咔擦~咔擦~"

STRINGS.CHARACTER_TITLES.queenspiderplayer = "蜘蛛女王"
STRINGS.CHARACTER_NAMES.queenspiderplayer = "蜘蛛女王"
STRINGS.CHARACTER_DESCRIPTIONS.queenspiderplayer = "*可以产卵\n*可以生成的喽罗。\n*喽罗并不是真正友好..."
STRINGS.CHARACTER_QUOTES.queenspiderplayer = "农场主"

STRINGS.CHARACTER_TITLES.wqueenspiderplayer = "居住者女王"
STRINGS.CHARACTER_NAMES.wqueenspiderplayer = "白蜘蛛女王"
STRINGS.CHARACTER_DESCRIPTIONS.wqueenspiderplayer = "*可以制作蜘蛛腺体。\n*可以生成居民。\n* 特别夜视仪。"
STRINGS.CHARACTER_QUOTES.wqueenspiderplayer = "看起来很脏的农场主"

STRINGS.CHARACTER_TITLES.moosegooseplayer = "鹿角鹅"
STRINGS.CHARACTER_NAMES.moosegooseplayer = "鹿角鹅"
STRINGS.CHARACTER_DESCRIPTIONS.moosegooseplayer = "*大BOSS。\n*能下蛋。\n* 掉落羽毛。"
STRINGS.CHARACTER_QUOTES.moosegooseplayer = "HONK-鹅叫"

STRINGS.CHARACTER_TITLES.babygooseplayer = "莫斯林"
STRINGS.CHARACTER_NAMES.babygooseplayer = "莫斯林"
STRINGS.CHARACTER_DESCRIPTIONS.babygooseplayer = "*非常可爱。\n*有脾气。 \n* 生气了..."
STRINGS.CHARACTER_QUOTES.babygooseplayer = "meep - 咩"

STRINGS.CHARACTER_TITLES.dragonplayer = "龙蝇"
STRINGS.CHARACTER_NAMES.dragonplayer = "龙蝇"
STRINGS.CHARACTER_DESCRIPTIONS.dragonplayer = "*大BOSS。\n*脾气暴躁。\n*晚上很累。"
STRINGS.CHARACTER_QUOTES.dragonplayer = "*嗡~~嗡~~嗡~~*"

STRINGS.CHARACTER_TITLES.maggotplayer = "熔岩虫"
STRINGS.CHARACTER_NAMES.maggotplayer = "熔岩虫"
STRINGS.CHARACTER_DESCRIPTIONS.maggotplayer = "*覆盖着温暖的熔岩！ \n* 讨厌下雨... \n* 并不强大..."
STRINGS.CHARACTER_QUOTES.maggotplayer = "像只蛆？"

STRINGS.CHARACTER_TITLES.bearplayer = "比格尔"
STRINGS.CHARACTER_NAMES.bearplayer = "比格尔"
STRINGS.CHARACTER_DESCRIPTIONS.bearplayer = "*大BOSS。\n*可以砸向地面。\n*掉毛。"
STRINGS.CHARACTER_QUOTES.bearplayer = "怒吼"

STRINGS.CHARACTER_TITLES.deerplayer = "巨鹿"
STRINGS.CHARACTER_NAMES.deerplayer = "巨鹿"
STRINGS.CHARACTER_DESCRIPTIONS.deerplayer = "*大BOSS。\n*致命 \n*可怕..."
STRINGS.CHARACTER_QUOTES.deerplayer = "它怎么叫？"

STRINGS.CHARACTER_TITLES.catplayer = "浣猫"
STRINGS.CHARACTER_NAMES.catplayer = "浣猫"
STRINGS.CHARACTER_DESCRIPTIONS.catplayer = "*精通狩猎\n*可以收获农作物。 \n*很可爱。"
STRINGS.CHARACTER_QUOTES.catplayer = "喵~"

STRINGS.CHARACTER_TITLES.ghostplayer = "鬼魂"
STRINGS.CHARACTER_NAMES.ghostplayer = "鬼魂"
STRINGS.CHARACTER_DESCRIPTIONS.ghostplayer = "*可以建造。 \n*发光。\n*可以隐形。"
STRINGS.CHARACTER_QUOTES.ghostplayer = "OOOoooOOOoooo"

STRINGS.CHARACTER_TITLES.mermplayer = "鱼人"
STRINGS.CHARACTER_NAMES.mermplayer = "鱼人"
STRINGS.CHARACTER_DESCRIPTIONS.mermplayer = "*可以建造。 \n*喜欢下雨。 \n*怪物。"
STRINGS.CHARACTER_QUOTES.mermplayer = "汩汩!"

STRINGS.CHARACTER_TITLES.pigmanplayer = "猪人"
STRINGS.CHARACTER_NAMES.pigmanplayer = "猪人"
STRINGS.CHARACTER_DESCRIPTIONS.pigmanplayer = "*可以建造。 \n*I一头猪。 \n*有一个可怕的秘密..."
STRINGS.CHARACTER_QUOTES.pigmanplayer = "哼!"

STRINGS.CHARACTER_TITLES.walrusplayer = "海象"
STRINGS.CHARACTER_NAMES.walrusplayer = "海象"
STRINGS.CHARACTER_DESCRIPTIONS.walrusplayer = "*可以建造。 \n*喜欢冬天，讨厌炎热。 \n* 掉落吹箭。"
STRINGS.CHARACTER_QUOTES.walrusplayer = "我..说.."

STRINGS.CHARACTER_TITLES.beefplayer = "皮弗娄牛"
STRINGS.CHARACTER_NAMES.beefplayer = "皮弗娄牛"
STRINGS.CHARACTER_DESCRIPTIONS.beefplayer = "*健壮。 \n*生小牛。 \n*能拉屎。"
STRINGS.CHARACTER_QUOTES.beefplayer = "?"

STRINGS.CHARACTER_TITLES.vargplayer = "座狼"
STRINGS.CHARACTER_NAMES.vargplayer = "座狼"
STRINGS.CHARACTER_DESCRIPTIONS.vargplayer = "*健壮。\n*召唤猎犬。 \n*掉落毛发。"
STRINGS.CHARACTER_QUOTES.vargplayer = "呜~呜~呜~"

STRINGS.CHARACTER_TITLES.smallbirdp = "小高脚鸟"
STRINGS.CHARACTER_NAMES.smallbirdp = "小高脚鸟"
STRINGS.CHARACTER_DESCRIPTIONS.smallbirdp = "*很可爱。\n*可以长大。 \n*并不强壮..."
STRINGS.CHARACTER_QUOTES.smallbirdp = "叽~叽~"

STRINGS.CHARACTER_TITLES.tallbirdplayer = "高脚鸟"
STRINGS.CHARACTER_NAMES.tallbirdplayer = "高脚鸟"
STRINGS.CHARACTER_DESCRIPTIONS.tallbirdplayer = "*很快。 \n*很生气。 \n*自豪的父母。"
STRINGS.CHARACTER_QUOTES.tallbirdplayer = "叽~叽~叽~"

STRINGS.CHARACTER_TITLES.clockwork1player = "发条骑士"
STRINGS.CHARACTER_NAMES.clockwork1player = "发条骑士"
STRINGS.CHARACTER_DESCRIPTIONS.clockwork1player = "*是发条！ \n*拥有夜视功能。\n* 可以进行基础维修。"
STRINGS.CHARACTER_QUOTES.clockwork1player = "嘶~嘶~"

STRINGS.CHARACTER_TITLES.clockwork2player = "发条主教"
STRINGS.CHARACTER_NAMES.clockwork2player = "发条主教"
STRINGS.CHARACTER_DESCRIPTIONS.clockwork2player = "*具有远程攻击！ \n*拥有夜视功能。 \n* 可以进行基础维修。"
STRINGS.CHARACTER_QUOTES.clockwork2player = "兹~"

STRINGS.CHARACTER_TITLES.clockwork3player = "机械战车"
STRINGS.CHARACTER_NAMES.clockwork3player = "机械战车"
STRINGS.CHARACTER_DESCRIPTIONS.clockwork3player = "*体积大！ \n*拥有夜视功能。 \n* 极具破坏性。"
STRINGS.CHARACTER_QUOTES.clockwork3player = "框迟框迟~"

STRINGS.CHARACTER_TITLES.treeplayer = "树精守卫"
STRINGS.CHARACTER_NAMES.treeplayer = "树精守卫"
STRINGS.CHARACTER_DESCRIPTIONS.treeplayer = "*体积大！ \n* 帮助自然。 \n*很慢..."
STRINGS.CHARACTER_QUOTES.treeplayer = "..."

----------------------------------------------------------------
-- The character's name as appears in-game
STRINGS.NAMES.HOUNDPLAYER = "猎犬"
STRINGS.NAMES.HOUNDICEPLAYER = "冰猎犬"
STRINGS.NAMES.HOUNDREDPLAYER = "火猎犬"
STRINGS.NAMES.HOUNDDARKPLAYER = "噩梦猎犬"
STRINGS.NAMES.BLACKSPIDERPLAYER = "蜘蛛"
STRINGS.NAMES.WHITESPIDERPLAYER = "白蜘蛛"
STRINGS.NAMES.QUEENSPIDERPLAYER = "蜘蛛女王"
STRINGS.NAMES.WQUEENSPIDERPLAYER = "白蜘蛛女王"
STRINGS.NAMES.MOOSEGOOSEPLAYER = "鹿角鹅"
STRINGS.NAMES.BABYGOOSEPLAYER = "莫斯林"
STRINGS.NAMES.DRAGONPLAYER = "龙蝇"
STRINGS.NAMES.MAGGOTPLAYER = "熔岩虫"
STRINGS.NAMES.BEARPLAYER = "比格尔"
STRINGS.NAMES.DEERPLAYER = "巨鹿"
STRINGS.NAMES.CATPLAYER = "浣猫"
STRINGS.NAMES.GHOSTPLAYER = "鬼魂"
STRINGS.NAMES.MERMPLAYER = "鱼人"
STRINGS.NAMES.PIGMANPLAYER = "猪人"
STRINGS.NAMES.WALRUSPLAYER = "海象"
STRINGS.NAMES.BEEFPLAYER = "皮弗娄牛"
STRINGS.NAMES.VARGPLAYER = "座狼"
STRINGS.NAMES.TALLBIRDPLAYER = "高脚鸟"
STRINGS.NAMES.CLOCKWORK1PLAYER = "发条骑士"
STRINGS.NAMES.CLOCKWORK2PLAYER = "发条主教"
STRINGS.NAMES.CLOCKWORK3PLAYER = "机械战车"
STRINGS.NAMES.TREEPLAYER = "树精守卫"
---------
STRINGS.NAMES.MERMHOUSE_PLAYER = "鱼族废墟"
STRINGS.NAMES.PIGHOUSE_PLAYER = "猪舍"
STRINGS.NAMES.IGLOO_PLAYER = "海象巢穴"
--STRINGS.NAMES.SPIDERNEST_P = "蜘蛛巢穴"
----------
STRINGS.NAMES.MONSTER_WPN = "神秘力量"

----------------------------------------------------------------
-- Custom speech strings

STRINGS.CHARACTERS.PIGMANPLAYER = require(chinesefolder.."/PlayablePets/speech_pigmanplayer")

----------------------------------------------------------------
-- The default responses of examining the prefab

STRINGS.CHARACTERS.GENERIC.DESCRIBE.MERMHOUSE_PLAYER =
{
  GENERIC = "这房子口味比较重！",
  BUNRT = "现在是无用的，臭的。",
}

STRINGS.CHARACTERS.GENERIC.DESCRIBE.PIGHOUSE_PLAYER =
{
  GENERIC = "一个猪的房子。",
  BURNT = "今晚没有人睡。",
}

STRINGS.CHARACTERS.GENERIC.DESCRIBE.IGLOO_PLAYER =
{
  GENERIC = "我希望建立了这个的人是友好的。",
  BURNT = "我希望建立了这个的人现在还是友好的。",
}

STRINGS.CHARACTERS.GENERIC.DESCRIBE.MONSTER_WPN =
{
  GENERIC = "我真的不应该有这个...",
}
---------------------------------------------------------------------------
