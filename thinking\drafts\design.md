## 季节工坊（Season Workshop）设计文档 — MVP v0.1

### 背景与目标
- 主题：四季工坊（Seasonal Tech）。
- 目标：围绕“季节切换”带来玩法变化，提供1名新角色+少量季节装备+1个机制导向Boss+一个小型世界事件。
- 贴图约束：全部复用原版贴图，进行改色（色相/饱和度/明度），不新增复杂动画。
- 联机定位：多人可协作，但MVP以1名新角色可独立体验为基线；数值与状态通过服务端权威计算，客户端仅渲染特效与提示。

### 范围与不做清单
- 本期范围：
  - 新角色：季匠（Season Crafter）
  - 装备：季节之刃、气候披风
  - 建筑：季工坊台（Season Workbench）、季祭坛（Season Altar）
  - 世界事件：季风乱流（Seasonal Gust）+ 季节宝珠
  - Boss：季冠树守（Season Crown Warden，机制导向）
- 不做：
  - 新UI系统、复杂背包系统、全新怪物生态线（留到后续版本）
  - 大型地形生成改动

---

## 角色：季匠（Season Crafter）

### 基础属性
- 生命：150
- 饥饿：150（饥饿速率：标准 1.0x；秋季被动可降低）
- 理智：200
- 战斗：无直接伤害加成，依赖季节被动与季节装备配合

### 被动：季节刻印（Season Engraving）
- 机制：智能季节管理系统，支持自动跟随与手动切换
  - **自动模式**：默认跟随世界季节自动切换被动效果
  - **手动模式**：可使用季节符印在当前世界季节内切换到任意季节
  - **智能重置**：世界季节变化时自动清除手动设置，重新跟随世界季节
  - 切换瞬间播放轻量VFX（改色 staffcastfx）与SFX提示
  - 角色具有轻微的季节颜色调整，手动设置时颜色更明显

- Spring 春（雨季适应）
  - 防水：waterproofness +0.5（显著降低湿度积累）
  - 采集效率：采集/采矿/砍伐动作速度 +50%
  - 湿冷抵抗：在潮湿状态下理智损失 -50%
- Summer 夏（炎热耐受）
  - 过热隔热：insulation_heat +60
  - 走位灵活：移动速度 +20%
  - 火焰耐受：来自燃烧/灼烧的伤害 -50%
- Autumn 秋（丰收稳健）
  - 饥饿稳态：饥饿消耗 -50%
  - 收获小幅增益：对浆果丛/作物收获时50%概率+1产出（简单掉落表hook）
- Winter 冬（冰寒对策）
  - 寒冷隔热：insulation_cold +120
  - 冰缓打击：近战命中给予目标-20%移速，持续2秒（对Boss减免效果，仅-15%/1.5秒）

### 季节符印（Season Seal）
- 类型：消耗品工具（四种季节变体）
- 功能：允许季匠主动切换季节刻印到指定季节
- 制作配方：纸莎草 x1 + 对应季节碎片 x2（在季工坊台制作）
  - 春季符印：纸莎草 + 春季碎片 x2
  - 夏季符印：纸莎草 + 夏季碎片 x2
  - 秋季符印：纸莎草 + 秋季碎片 x2
  - 冬季符印：纸莎草 + 冬季碎片 x2
- 使用方式：右键点击使用，立即切换季节刻印
- 限制：一次性消耗品，仅季匠角色可使用
- 战术价值：解决Boss四季护盾破盾难题，提供灵活的季节控制

### 季节状态查看
- 角色检查：右键点击角色显示详细季节状态
- 快捷命令：控制台输入 `ShowSeasonStatus()` 查看状态
- 视觉提示：角色具有季节对应的轻微颜色调整
- 状态格式：`季节刻印：春季（手动） | 世界：夏季`

实现提示：使用玩家实例上的自定义组件 season_engraving（服务端计算），支持手动季节设置与世界季节变化监听；通过 tags/externalspeedmult/insulation设置；近战减速使用自定义debuff组件（带netvars）；季节符印为useableitem组件实现。

---

## 装备

### 季节之刃（Season Blade）
- 类型：近战武器（基于长矛build改色）
- 基础伤害：34（与长矛一致）
- 耐久：150次攻击
- 特性：随“季节刻印”调整附加效果（服务端OnHit检查持有者季节）
  - 春：对潮湿目标伤害 +25%
  - 夏：命中时附带轻微灼伤（等效3点/秒，持续2秒，对Boss改为2点/秒）
  - 秋：命中时15%概率返还1点耐久（节约匠）
  - 冬：命中时附带1层小幅减速（与角色被动叠加但设上限50%）
- 连击叠层与爆发（机制核心）：
  - 当持有者在同一目标上连续命中，将获得“季势”叠层，每次命中+1层（仅在8秒内连续命中才续期，超时清空；换目标清空）。
  - 达到3层时立即触发一次“小型季节爆发”（Small Seasonal Burst）：
    - 春：小范围电链，对半径3格内敌人造成50点伤害，并施加轻微湿身（对Boss伤害30点；电链至多跳2次）。
    - 夏：小范围热浪，对半径3格内敌人造成25点伤害+灼烧（5点/0.5秒，持续3秒；Boss仅伤害不附带灼烧）。
    - 秋：回复持有者2点饥饿与3点理智，并对目标造成25点打击（Boss取15点）。
    - 冬：对目标与周围2格内敌人施加20%减速2秒（Boss减为10%/1.5秒）并造成20点冰伤。
  - 触发后清空叠层，进入3秒冷却；冷却期继续命中可重新叠层但不会触发。
  - 数值可通过配置项进行整体上/下调（见配置项）。

- 合成：在季工坊台制作
  - 配方：树枝x2、犬牙x1、季芯x1（Season Core）
  - 科技：SEASONAL=1

- Boss破盾机制：
  - 当对季冠树守连击3次触发爆发时，若持有者季节刻印与Boss当前阶段匹配，则可破除Boss护盾
  - 配合季节符印使用，可灵活应对Boss的四季护盾变化
  - 破盾条件：持有者季节刻印 = Boss当前季节阶段
  - 战术应用：观察Boss阶段 → 使用对应季节符印 → 武器连击破盾

### 气候披风（Climate Cloak）
- 类型：身体装备（基于trunkvest/夏装改色）
- 耐久：不衰减；但每季切换时自动“调校”属性（仅修改数值）
  - 春：waterproofness +0.4
  - 夏：insulation_heat +90
  - 秋：饥饿 -5%（与角色被动相乘：0.95×0.5≈0.475）
  - 冬：insulation_cold +150
- 合成：
  - 主配方：牛毛x4、蜘蛛丝x4、季芯x1
  - 备选配方：牛毛x4、兔尾x4、季芯x1
  - 科技：SEASONAL=1

---

## 建筑与制作

### 季工坊台（Season Workbench）
- 用途：新的季节科技站；解锁“季节之刃/气候披风/季芯工具”等配方
- 贴图：基于炼金引擎（alchemist）改色；沿用原有动画
- 合成：木板x4、石砖x2、齿轮x1
- 功能：提供自定义科技树 SEASONAL=1（与原科技独立）

### 季祭坛（Season Altar）
- 用途：用于召唤Boss与选择首次弱点季节
- 贴图：基于月台/骨架部件改色组合（静态结构）
- 合成：石砖x6、月石x4、季芯x2
- 交互：
  - 放入4枚“季芯”（春夏秋冬各1）即可激活祭坛并生成Boss；
  - 最后放入的季芯类型决定Boss的初始弱点季节（特定季芯）或随机弱点（通用季芯）；
  - 若Boss被击败，祭坛进入冷却（默认5天，配置项）

### 季芯（Season Core）
- 类型：制作材料
- 获得：季风乱流事件中收集“季节碎片”x4合成
- 变体：
  - **通用季芯**（season_core）：四种不同季节碎片各1个合成，用于基础装备制作
  - **春季芯**（season_core_spring）：春季碎片x4合成，绿色外观
  - **夏季芯**（season_core_summer）：夏季碎片x4合成，橙色外观
  - **秋季芯**（season_core_autumn）：秋季碎片x4合成，褐色外观
  - **冬季芯**（season_core_winter）：冬季碎片x4合成，蓝色外观
- 用途：
  - 通用季芯：装备配方、Boss召唤（随机初始弱点）
  - 特定季芯：制作对应季节的炸符、Boss召唤时指定初始弱点

---

## 世界事件：季风乱流（Seasonal Gust）
- 触发：默认每季2次（间隔8~12天浮动），持续半天至1天（配置项）
- 现象：
  - 世界轻度变色（调色滤镜）；
  - 在玩家附近生成3~5个“季节宝珠”（对应当前季节）
- 交互：
  - 近距离可“吸收”宝珠获得“季节碎片”（season_shard_<season>）；
    - 春季：season_shard_spring（绿色）
    - 夏季：season_shard_summer（橙色）
    - 秋季：season_shard_autumn（褐色）
    - 冬季：season_shard_winter（蓝色）
  - 持有不同季节碎片可在工坊合成为“季芯”
（通用或特定季节）

- 实现：world组件 seasonal_gust，监听天相+计时器；宝珠用发光FX（复用 staffcastfx 改色），实体为简单pickup。

---

## Boss：季冠树守（Season Crown Warden）

### 概念与外观
- 外观：基于树精/熊獾抽象化改色（偏四季主题色），不改动作；附加简单季节光环VFX。

### 数值（默认，随配置缩放）
- 生命：8000（多人联机建议开局倍率=玩家数^0.5，配置项可选）
- 护甲：0.8（未破盾时额外伤害减免，见机制）
- 伤害：普通拍击 75；季节技能 30~50（带环境效果）
- 掉落：季芯x2、松露/蜂王浆/当季的成品食物随机各1、饰品蓝图若干（待后续扩充）

### 机制核心：四季护冠与弱点揭示
- Boss拥有“季节护冠”状态，分四个阶段（每损失25%生命进入下一阶段）。进入阶段时随机/轮换季节主题：春/夏/秋/冬。
- 护冠效果：未被“对应季节之力”击破前，Boss额外获得80%伤害减免与部分异常免疫。
- 破盾方式（任一满足）：
  1) 对应“季芯炸符”（Season Sigil）在Boss脚下部署/投掷生效（范围小圆，持续2秒）；
     - 春季护盾需要春季炸符（由春季芯制作）
     - 夏季护盾需要夏季炸符（由夏季芯制作）
     - 秋季护盾需要秋季炸符（由秋季芯制作）
     - 冬季护盾需要冬季炸符（由冬季芯制作）
  2) 季节之刃持有者季节刻印与Boss当前阶段匹配时，连续命中Boss3次（8秒内）自动触发小型破盾；
     - 关键改进：可使用季节符印主动切换季节刻印来匹配Boss阶段
     - 战术流程：观察Boss季节阶段 → 使用对应季节符印切换刻印 → 武器连击破盾
     - 解决了原本需要等待对应季节才能破盾的限制
- 破盾后：该阶段内Boss失去额外减免，受到正常伤害，并暴露“季伤弱点”（被动吃到季节附加效果更明显）。阶段结束/进入下一阶段时护冠重置。

### 季节阶段行为
- 春：雷鸣号令
  - 随机对玩家位置落雷预警圈（不必真实雷击，使用电击VFX+伤害）；被击中会短暂湿身+小眩晕
  - 地面生成2个“藤蔓缠绕点”（陷阱，踩中-30%移速3秒）
- 夏：炎热涌动
  - 周期性释放灼热波，点燃地面小范围（短时，不蔓延），玩家受轻微DoT
  - Boss获得短时移速+10%
- 秋：萧瑟旋风
  - 施放2个落叶旋风在场追逐最近玩家3秒，命中击退+小量伤害
  - 场上掉落1~2个“秋实”，捡取回复少量理智（鼓励走位）
- 冬：寒潮吐息
  - 锥形寒息，命中施加2秒缓速与少量体温下降
  - 地面生成“冰斑”，踩中滑步（易被追击）

### 召唤与复活
- 通过季祭坛放入四季芯后召唤；
- 击败后祭坛进入冷却（默认5天），地图标注更新；
- 中途离开一定范围会使Boss回归祭坛并缓慢回血（防止风筝过远）。


### 野外入侵（赛季挑战）
- 规则：每个季节Boss将“随机两次”在“随机玩家”附近入侵（不在基地密集建筑20格内，不在洞穴入口/传送台5格内）。
- 未击杀不计入两次：若当次入侵结束时Boss未被击杀（玩家脱战或超时离开），则这次不计入季节的“两次”额度，并将在“2天后”再次入侵（直至当季累计击杀达到2次或季节结束）。
- 入侵形态参数（安全与公平）：
  - 血量为常规Boss的40%（可配置），掉落为常规的50%（至少掉1个季芯，均可配置），多人联机时，血量和掉落都*人数^0.5。
  - 入侵开始有5秒显著预警（地面符文圈+染色闪光，复用moon_base与staffcastfx改色），期间不会攻击。
  - 战斗半径限制为30格，超出则Boss返回、回血至该入侵开场血量的50%，并在同日不再追击（避免远程风筝）。
- 与祭坛召唤的关系：入侵与祭坛召唤互不冲突；祭坛为完整体Boss与完整奖励，入侵为赛季巡猎挑战。

实现提示：Boss为自定义prefab，行为树沿用基础近战+阶段技能定时器；护冠使用可网络同步的状态机（netbyte phase + netbool shielded）。破盾判定来自sigil地面实体或weapon_onhit计数；阶段技能用定时/投点AOE。

---

## 配方与数值（TUNING）
- TUNING.SEASON_BLADE_DAMAGE = 34
- TUNING.SEASON_BLADE_USES = 150
- TUNING.CLIMATE_CLOAK_HEAT = 90
- TUNING.CLIMATE_CLOAK_COLD = 150
- TUNING.SEASON_CORE_SHARDS = 4
- TUNING.SEASON_EVENT_MIN_DAYS = 8
- TUNING.SEASON_EVENT_MAX_DAYS = 12
- TUNING.SEASON_BOSS_HP = 8000
- TUNING.SEASON_BOSS_COOLDOWN_DAYS = 5
- TUNING.SEASON_BOSS_SHIELD_ABSORB = 0.8

- TUNING.SEASON_BLADE_STACKS_MAX = 3
- TUNING.SEASON_BLADE_STACK_WINDOW = 8
- TUNING.SEASON_BLADE_BURST_CD = 3
- TUNING.SEASON_BLADE_BURST_DMG_SPRING = 50
- TUNING.SEASON_BLADE_BURST_DMG_SPRING_BOSS = 30
- TUNING.SEASON_BLADE_BURST_DMG_SUMMER = 25
- TUNING.SEASON_BLADE_BURST_DMG_AUTUMN = 25
- TUNING.SEASON_BLADE_BURST_DMG_AUTUMN_BOSS = 15
- TUNING.SEASON_BLADE_BURST_DMG_WINTER = 20
- TUNING.SEASON_WARDEN_INVASIONS_PER_SEASON = 2
- TUNING.SEASON_WARDEN_INVASION_RESPAWN_DAYS = 2
- TUNING.SEASON_WARDEN_INVASION_HP_MUL = 0.4
- TUNING.SEASON_WARDEN_INVASION_LOOT_MUL = 0.5
- TUNING.SEASON_WARDEN_BATTLE_RADIUS = 30
- TUNING.SEASON_WARDEN_INVASION_WARN_SECS = 5

配方（在Season Workbench下）：
- 季节之刃：twigs x2 + houndstooth x1 + season_core x1
- 气候披风：
  - 主配方：beefalowool x4 + silk x4 + season_core x1
  - 备选配方：beefalowool x4 + manrabbit_tail x4 + season_core x1
- 季芯合成：
  - 通用季芯：season_shard_spring x1 + season_shard_summer x1 + season_shard_autumn x1 + season_shard_winter x1
  - 春季芯：season_shard_spring x4
  - 夏季芯：season_shard_summer x4
  - 秋季芯：season_shard_autumn x4
  - 冬季芯：season_shard_winter x4
- 季芯炸符（Season Sigil）：
  - 春季炸符：cutstone x1 + nitre x1 + season_core_spring x1
  - 夏季炸符：cutstone x1 + nitre x1 + season_core_summer x1
  - 秋季炸符：cutstone x1 + nitre x1 + season_core_autumn x1
  - 冬季炸符：cutstone x1 + nitre x1 + season_core_winter x1
- 季节符印（Season Seal）：
  - 春季符印：papyrus x1 + season_shard_spring x2
  - 夏季符印：papyrus x1 + season_shard_summer x2
  - 秋季符印：papyrus x1 + season_shard_autumn x2
  - 冬季符印：papyrus x1 + season_shard_winter x2

---

## 配置项（Mod Config）

### Boss相关配置
- **Boss血量倍率**：0.8x / 1.0x（默认）/ 1.2x / 1.5x
  - 影响季冠树守的基础血量（8000）和入侵Boss血量
  - 入侵Boss最终血量 = 基础血量 × Boss血量倍率 × 入侵HP倍率 × 多人缩放
- **Boss护盾减免**：60% / 70% / 80%（默认）/ 90%
  - 护盾状态下的伤害减免比例，减免的伤害会以回血形式返还
  - 例：80%减免时，100点伤害只造成20点实际伤害，80点以回血形式返还

### 建筑与事件配置
- **祭坛冷却天数**：3 / 5（默认）/ 7
  - Boss被击败后祭坛的冷却时间
- **季风乱流频率**：低（每季1次）/ 中（默认，每季2次）/ 高（每季3次）
- **季风乱流持续时间**：短（6小时）/ 中（默认，12小时）/ 长（24小时）

### 装备效果配置
- **季节之刃特效强度**：低/中（默认）/高
  - 影响基础季节效果的强度倍率（0.7x / 1.0x / 1.3x）
  - 春季：潮湿额外伤害，夏季：灼伤伤害，秋季：耐久返还概率，冬季：减速持续时间
  - 同时影响爆发特效的音量、持续时间和视觉效果
- **气候披风属性强度**：低/中（默认）/高
  - 影响各季节属性的强度倍率（0.7x / 1.0x / 1.3x）
  - 春季：防水效果40%→28%/40%/52%，夏季：隔热值90→63/90/117
  - 秋季：饥饿减免5%→3.5%/5%/6.5%，冬季：保温值150→105/150/195
- **季节之刃叠层爆发强度**：低（0.8x）/中（默认，1.0x）/高（1.2x）
  - 影响所有季节爆发技能的伤害倍率
  - 春季：电链伤害和跳跃伤害，夏季：热浪伤害，秋季：伤害和回复量，冬季：冰霜伤害

### Boss入侵配置
- **Boss野外入侵**：开启（默认）/关闭
- **入侵HP倍率**：0.3 / 0.4（默认）/ 0.5
  - 入侵Boss相对于正常Boss的血量比例
- **入侵掉落倍率**：0.5（默认）/ 0.75 / 1.0
  - 入侵Boss的掉落物数量倍率
- **入侵次数/季**：1 / 2（默认）/ 3
- **入侵重试间隔**：1 / 2（默认）/ 3天

### 视觉效果配置
- **爆盾/爆发VFX强度**：低/中（默认）/高
  - 影响破盾和爆发特效的光效亮度、粒子数量、屏幕震动幅度
  - 应用于警告圈、Boss技能特效等通用视觉效果
  - 低：透明度0.4，缩放1.0x，音量0.3；中：透明度0.7，缩放1.2x，音量0.6；高：透明度0.9，缩放1.8x，音量1.0

---

## 贴图与美术复用
- **武器（季节之刃）**：复用spear build，改色（春：青绿，夏：橙，秋：褐，冬：蓝），动态根据持有者季节刻印调整颜色。
- **披风（气候披风）**：复用trunkvest build改色，根据季节动态调整颜色（春：绿调，夏：橙调，秋：褐调，冬：蓝调）。
- **工坊（季工坊台）**：复用alchemist（researchlab2）build，固定四季融合暖色调（0.9, 0.8, 0.7）+ 微发光效果。
- **祭坛（季祭坛）**：复用moonbase build，静态结构，小地图图标根据冷却状态切换（正常：moonbase.png，冷却：firepit.png）。
- **Boss（季冠树守）**：复用leif（树精）build，护盾状态时添加蓝色调（0.15, 0.15, 0.35），破盾后恢复原色。
- **宝珠与特效**：复用staff_castinglight特效，根据季节改色与缩放，吸收时播放同色特效。
- **季节符印**：复用trinket_6 build，根据季节设置不同颜色（春：绿，夏：橙，秋：褐，冬：蓝）。
- **季节炸符**：统一复用trinket_6 build并改色，不使用不同build，保持视觉一致性。
- **季节碎片**：复用nightmarefuel build，根据季节改色（春：鲜绿，夏：温橙，秋：深褐，冬：清蓝）。
- **季芯**：复用gears build，根据季节改色，体现机械核心主题。

- **爆盾/爆发特效**：
  - 破盾：复用staff_castinglight + ghost_spawn音效，短暂强光瞬闪，性能友好。
  - 爆发：按季节对应色的特效扩散，音效复用staff_spell/lightning，支持VFX强度配置调节。
  - 预警特效：复用stafflight作为警告圈，支持透明度和缩放的强度配置。

### 具体贴图复用实现
| 物品/建筑 | 复用的原版build | 改色方案 | 实现方式 |
|----------|----------------|----------|----------|
| 季节之刃 | spear | 统一颜色方案 | 动态根据持有者季节刻印调整 |
| 气候披风 | trunkvest | 统一颜色方案的柔和版本（适合服装） | 动态根据世界季节调整 |
| 季工坊台 | researchlab2 | 四季融合暖色调(0.9,0.8,0.7) | 固定改色+微发光 |
| 季祭坛 | moonbase | 原色，小地图图标动态切换 | 冷却时切换为firepit.png图标 |
| 季冠树守 | leif | 护盾时蓝色调(+0.15,+0.15,+0.35) | 护盾状态动态调整 |
| 季节宝珠 | stafflight | 统一颜色方案+0.8透明度 | 1.2倍缩放+半透明 |
| 季节符印 | trinket_6 | 统一颜色方案 | 静态改色 |
| 季节炸符 | trinket_6 | 统一颜色方案 | 统一build，仅改色区分 |
| 季节碎片 | nightmarefuel | 统一颜色方案 | 静态改色 |
| 季芯 | gears | 统一颜色方案 | 静态改色，体现机械主题 |

### 统一颜色方案
所有季节相关物品使用统一的颜色方案（RGB值）：
- **春季**：(0.5, 1.0, 0.5) - 鲜绿色，象征生机与成长
- **夏季**：(1.0, 0.6, 0.2) - 温暖橙色，象征炎热与活力
- **秋季**：(0.8, 0.5, 0.2) - 深褐色，象征丰收与稳重
- **冬季**：(0.6, 0.8, 1.0) - 清冷蓝色，象征寒冷与宁静

**设计理念**：
- 颜色饱和度适中，避免过于刺眼
- 保持与原版游戏风格的协调性
- 不同物品类型可使用柔和版本（如气候披风）
- 透明度和强度可通过配置项调节

### 特效复用策略

#### 核心特效组件
- **主特效**：staff_castinglight（万能特效基础，支持改色、缩放、持续时间调节）
- **警告圈**：fx_warning_circle（基于stafflight，支持配置化强度调节）
- **音效库**：复用原版音效，支持音量调节

#### 详细特效实现

**1. 武器爆发特效系统**
- 基础：staff_castinglight + staff_spell音效
- 配置化参数：
  - 音量：低(0.3) / 中(0.6) / 高(1.0)
  - 持续时间：低(0.2s) / 中(0.4s) / 高(0.6s)
  - 强度倍率：低(0.8x) / 中(1.0x) / 高(1.2x)
- 季节差异：通过颜色和伤害类型区分，特效基础一致

**2. Boss技能预警系统**
- 春季雷鸣：fx_warning_circle（默认大小）→ 0.7秒后伤害判定
- 夏季炎热：fx_warning_circle（1.6倍缩放）→ 0.7秒后范围灼烧
- 秋季旋风：无预警圈，直接生成追踪旋风（staff_castinglight）
- 冬季寒潮：fx_warning_circle（2倍缩放）→ 0.7秒后锥形攻击

**3. 破盾与状态特效**
- 炸符破盾：staff_castinglight脉冲（5次，间隔0.4秒）+ ghost_spawn音效
- 武器破盾：staff_castinglight + ghost_spawn音效
- 护盾状态：Boss添加蓝色调(+0.15, +0.15, +0.35)，破盾后清除

**4. 交互特效**
- 季节切换（角色）：staff_castinglight + staff_coldlight音效 + 角色颜色调整
- 季节切换（符印）：staff_castinglight + staff_spell音效 + 角色对话
- 宝珠吸收：staff_castinglight（季节颜色）+ 0.5秒持续时间
- 建筑建造：alchemy_engine音效（工坊台）/ teleportworm音效（Boss召唤）

**5. 入侵预警系统**
- 双重圈效果：
  - 内圈：staff_castinglight（橙红色 1.0, 0.5, 0.2）
  - 外圈：staff_castinglight（淡蓝色 0.8, 0.8, 1.0，1.5倍缩放）
- 小地图标记：moonbase图标，预警结束后10秒移除
- 持续时间：配置化预警时间（默认5秒）

### 音效复用策略
| 场景 | 复用的原版音效 | 配置化参数 | 使用场景 | 触发条件 |
|------|---------------|------------|----------|----------|
| 武器爆发 | staff_spell | 音量(0.3/0.6/1.0) | 季节之刃连击爆发 | 3层叠加触发 |
| 破盾成功 | ghost_spawn | 固定音量 | Boss护盾被破除 | 炸符或武器破盾 |
| 季节切换（自动） | staff_coldlight | 固定音量 | 角色季节刻印自动切换 | 世界季节变化 |
| 季节切换（手动） | staff_spell | 固定音量 | 季节符印手动切换 | 使用季节符印 |
| 建筑建造 | alchemy_engine | 固定音量 | 季工坊台建造完成 | 建筑完成事件 |
| Boss召唤 | teleportworm/travel | 固定音量 | 祭坛召唤Boss | 4个季芯激活 |
| 宝珠吸收 | 无音效 | - | 季节宝珠被吸收 | 近距离交互 |
| 炸符部署 | staff_spell | 固定音量 | 季节炸符部署 | 地面部署时 |

### 资源优化策略
- **零额外资源**：完全复用原版贴图和音效，通过AnimState:SetMultColour()实现改色
- **动态颜色系统**：支持运行时颜色调整，无需预制多套资源
- **配置化特效**：透明度、缩放、强度、音量均可通过配置项调节
- **性能友好**：避免额外的atlas和音频文件加载，减少内存占用
- **兼容性强**：不依赖自定义资源文件，降低安装和维护成本
- **模块化设计**：特效组件可独立配置，支持用户个性化调节

### 特效性能优化
- **统一特效基础**：90%以上特效基于staff_castinglight，减少prefab种类
- **智能生命周期**：所有特效都有明确的移除时间，避免内存泄漏
- **配置化强度**：用户可根据设备性能调节特效强度（低/中/高）
- **网络优化**：特效主要在客户端渲染，减少网络同步开销
- **批量处理**：范围特效使用单次查找+批量处理，提高效率
- **条件渲染**：根据距离和重要性动态决定是否播放特效

### 特效生命周期管理
- **自动清理**：所有特效都有明确的DoTaskInTime移除时间
- **任务管理**：DoPeriodicTask和DoTaskInTime任务在对象移除前正确取消
- **内存安全**：特效对象标记为persists=false，避免存档污染
- **网络同步**：debuff状态通过net_vars同步到客户端
- **异常处理**：特效播放前检查对象有效性，避免空指针错误
- **资源回收**：组件清理时主动取消所有相关任务和特效

---

## 技术实现规划

### 项目文件结构

#### 核心配置文件
- **modinfo.lua** - 模组信息与配置选项定义
- **modmain.lua** - 模组主入口，组件注册与初始化

#### 组件系统 (/scripts/components)
- **season_engraving.lua** - 季节符印系统核心组件
  - 管理角色的季节状态（自动/手动模式）
  - 处理季节切换逻辑与状态同步
  - 提供季节符印使用接口

- **seasonal_debuff.lua** - 季节性状态效果管理
  - 减速、灼烧等轻量级debuff实现
  - 统一的DoT伤害处理机制
  - 效果叠加与持续时间管理

- **seasonal_gust_manager.lua** - 季风乱流世界事件
  - 事件触发时机控制（8-12天间隔）
  - 季节宝珠生成与分布管理
  - 多人游戏同步机制

- **boss_shield.lua** - Boss护盾系统
  - 四季护盾状态管理
  - 破盾条件检测与处理
  - 护盾视觉效果控制

- **season_warden_invasion.lua** - 季节守护者入侵系统
  - Boss入侵调度与触发条件
  - 场上Boss数量限制
  - 安全区域检测与保护

#### 实体预制体 (/prefabs)

##### 角色与装备
- **season_crafter.lua** - 季匠角色
  - 基础属性与生存能力设定
  - 季节被动技能实现
  - 专属物品制作权限

- **season_blade.lua** - 季节之刃武器
  - 四季形态切换机制
  - 连击系统与爆发伤害
  - 耐久度特殊机制

- **climate_cloak.lua** - 气候披风装备
  - 四季属性加成系统
  - 环境适应性效果
  - 与角色被动的协同机制

##### 建筑设施
- **season_workbench.lua** - 季工坊台
  - 专属科技树实现
  - 季节性配方系统
  - 制作界面与权限控制

- **season_altar.lua** - 季祭坛
  - Boss召唤机制
  - 冷却时间管理
  - 祭品消耗与验证

##### 材料与道具
- **season_orb.lua** - 季节宝珠系统
  - 基础宝珠类与四季变体
  - 拾取检测与自动合成
  - 库存管理与使用限制

- **season_core.lua & season_shard.lua** - 核心材料
  - 季芯与季节碎片的属性定义
  - 合成配方与获取途径
  - 堆叠规则与存储机制

- **season_sigil.lua** - 季节符印道具
  - 四季符印变体实现
  - 使用效果与消耗机制
  - 制作成本与平衡性

- **boss_season_warden.lua** - 季节守护者Boss
  - Boss基础属性与AI行为
  - 四季阶段切换机制
  - 特殊技能与攻击模式

#### 资源管理 (/images & /anim)
- **动态着色系统**：
  - 复用原版资源，通过 `AnimState:SetMultColour()` 实现四季主题色彩
  - 无需自定义atlas文件，显著减少模组体积
  - 支持配置项控制特效强度与视觉效果

### 网络同步与性能优化

#### 数据同步策略
- **角色状态同步**：
  - 服务端权威，客户端显示
  - 使用 `net_ushort` 同步季节阶段标识
  - 使用 `net_bool` 同步护盾状态

- **世界事件同步**：
  - 事件触发由服务端统一管理
  - 宝珠生成位置与数量同步
  - 玩家拾取状态实时更新

#### 性能控制措施
- **实体数量限制**：
  - 每玩家最多同时存在3个季节宝珠
  - 定时回收机制，防止实体堆积
  - Boss入侵数量限制（全服最多1个）

- **计算频率优化**：
  - DoT效果tick频率限制为0.5秒
  - 季节检测使用缓存机制
  - 视觉效果按需更新

### 游戏系统集成

#### 科技树扩展
- **新科技等级**：SEASONAL（等级1）
  - 独立于原版科技树
  - 专属解锁条件与配方
  - 渐进式内容开放

#### 标签系统
- **功能性标签**：
  - `"seasonal"` - 季节性物品通用标签
  - `"season_core"` - 季芯材料识别
  - `"season_orb"` - 宝珠类物品标签
  - `"season_sigil"` - 符印类道具标签
  - `"wardenshield"` - Boss护盾状态标签

---

## 测试计划（手动）
- 角色被动：四季切换时数值是否更新；春季防水是否显著；冬季减速对Boss是否按减免生效。
- 武器：季节之刃在四季效果是否切换；耐久返还是否有上限与提示。
- 披风：四季属性是否正确；与角色被动叠加是否符合预期。
- 世界事件：8~12天间隔触发；宝珠生成数量与拾取合成是否正常；多人情况下同步。
- Boss：护盾机制是否需要对应手段破盾；阶段技能是否按血量进度触发；祭坛冷却是否生效。
- 武器叠层：3连内在8秒窗口是否正确触发爆发；冷却后是否可重新叠层；多人同目标时只按各自叠层计算。
- 入侵：每季尝试两次是否按规则触发；未击杀是否2天后重试；安全区与半径限制是否生效；掉落倍率正确。
- 爆盾VFX：破盾瞬间是否有明显视觉/听觉反馈且不刺眼；配置项强度切换有效。


---

## 里程碑与实现顺序
1) 基础骨架：modinfo.lua、modmain.lua、TUNING与配置、目录结构（1天）
2) 季匠角色与季节刻印组件（1-2天）
3) 季工坊台与科技/配方体系（1天）
4) 季节之刃与气候披风（1-2天）
5) 世界事件季风乱流与宝珠/季芯（2天）
6) 季祭坛与Boss（护盾+阶段+基础攻击）（3-5天）
7) 数值与手动测试/修正（2天）
8) ~~改色资源与图标打包~~（已通过代码动态改色实现，无需额外资源）

验收标准：
- 角色被动与两件装备稳定可用；
- 世界事件可可靠触发并合成季芯；
- Boss需使用对应手段可破盾击杀；
- 季节符印系统正常工作，可灵活切换季节刻印；
- 配置项可调，联机无明显不同步。

---

## 季节符印系统补充说明

### 设计目标
解决原设计中Boss破盾机制的实用性问题：原本需要等待特定世界季节才能用武器破盾，现在通过季节符印系统实现灵活的季节控制。

### 核心机制
1. **智能季节管理**：
   - 默认跟随世界季节（自动模式）
   - 可手动切换到任意季节（手动模式）
   - 世界季节变化时自动重置手动设置

2. **战术应用**：
   - 观察Boss当前季节阶段
   - 使用对应季节符印切换刻印
   - 用季节之刃连击3次破盾

3. **平衡考虑**：
   - 符印为一次性消耗品，需要合理使用
   - 制作成本适中（纸莎草+季节碎片x2）
   - 仅季匠角色可使用，保持角色独特性

### 用户体验改进
- 多种方式查看当前季节状态
- 直观的视觉反馈（角色颜色调整）
- 快捷命令支持
- 智能的季节重置机制

