STRINGS.NAMES.SANCOAT = "珊的狼斗篷"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SANCOAT = "扮作狼。 好吧！"
STRINGS.NAMES.SANHAT = "珊的狼外套"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SANHAT = "扮作狼。 好吧！"
STRINGS.NAMES.SANNECKLACE = "珊的部落项链"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SANNECKLACE = "被森林保护者所珍爱。"
STRINGS.NAMES.SANSPEAR = "珊的部落长矛"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SANSPEAR = "被森林保护者所珍爱。"

-- The character select screen lines
STRINGS.CHARACTER_TITLES.san = "幽灵公主"
STRINGS.CHARACTER_NAMES.san = "珊"
STRINGS.CHARACTER_DESCRIPTIONS.san = "*自然清道夫\n*她速度很快\n*非自然的生物会丧失理智"
STRINGS.CHARACTER_QUOTES.san = "\"呃，我闻起来像一个人类。\""

-- Custom speech strings
--STRINGS.CHARACTERS.SAN = require(chinesefolder.."/San/speech_san")
STRINGS.CHARACTERS.SAN = nil

-- The character's name as appears in-game
STRINGS.NAMES.SAN = "珊"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SAN =
{
  GENERIC = "这是珊！",
  ATTACKER = "珊看起来很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "珊, 鬼魂朋友！",
  GHOST = "珊可以使用一颗心。",
}

