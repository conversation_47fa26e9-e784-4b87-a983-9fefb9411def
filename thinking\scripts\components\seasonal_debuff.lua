-- 季节减益组件（重构版 - 移除网络变量）
-- 处理季节相关的减速和灼伤效果
-- 改为纯服务端组件，可安全地在运行时动态添加，符合DST规范

local _G = GLOBAL
local Class = _G.Class
local TheWorld = _G.TheWorld

local SeasonalDebuff = Class(function(self, inst)
    self.inst = inst
    self.slow_mult = 1.0
    self.slow_duration = 0
    self.slow_task = nil

    -- 灼伤效果
    self.burn_damage = 0
    self.burn_duration = 0
    self.burn_task = nil
    self.burn_tick_task = nil

    -- 移除网络变量 - 改为纯服务端组件
    -- 视觉效果通过事件推送到客户端，符合DST规范
    -- 这样可以安全地在运行时动态添加组件
end)

function SeasonalDebuff:ApplySlow(multiplier, duration, source_is_boss)
    if not TheWorld.ismastersim then return end

    -- 错误处理：检查实例和参数有效性
    if not self.inst or not self.inst:IsValid() then
        print("[SeasonWorkshop] Error: Invalid debuff instance in ApplySlow")
        return
    end

    if not multiplier or not duration or multiplier < 0 or multiplier > 1 or duration < 0 then
        print("[SeasonWorkshop] Error: Invalid slow parameters - mult:" .. tostring(multiplier) .. " dur:" .. tostring(duration))
        return
    end

    -- 护盾状态下的异常免疫检查
    if self.inst:HasTag("season_shield_immune") then
        -- 护盾状态下减速效果减弱75%
        multiplier = multiplier + (1 - multiplier) * 0.75  -- 例如0.8变为0.95
        duration = duration * 0.25  -- 持续时间减少75%
        print(string.format("[SeasonWorkshop] Slow effect reduced by shield immunity: %.2f -> %.2f, %.1fs -> %.1fs",
              multiplier + (1 - multiplier) * 0.75, multiplier, duration * 4, duration))
    end

    -- Boss减免效果
    if self.inst:HasTag("epic") and source_is_boss then
        local old_mult = multiplier
        local old_dur = duration
        multiplier = math.max(0.85, multiplier + 0.05) -- -20%变为-15%
        duration = duration * 0.75 -- 2秒变为1.5秒
        print(string.format("[SeasonWorkshop] Boss resistance applied: %.2f -> %.2f, %.1fs -> %.1fs",
              old_mult, multiplier, old_dur, duration))
    end

    -- 检查是否已有其他减速效果，确保总减速不超过50%
    local current_mult = 1.0
    if self.inst.components and self.inst.components.locomotor then
        -- 获取当前所有外部速度修正
        local external_mults = self.inst.components.locomotor.external_speed_multipliers or {}
        for source, mult in pairs(external_mults) do
            if source ~= "seasonal_slow" then -- 排除我们即将设置的
                current_mult = current_mult * mult
            end
        end
    end

    -- 计算叠加后的总倍率，确保不低于0.5（即不超过50%减速）
    local combined_mult = current_mult * multiplier
    if combined_mult < 0.5 then
        multiplier = 0.5 / current_mult
        multiplier = math.max(0.5, multiplier) -- 确保不会出现负值或过小值
    end

    self.slow_mult = multiplier
    self.slow_duration = duration

    -- 使用DST原生机制，无需自定义网络同步
    -- locomotor组件的速度修改会自动同步到客户端

    -- 应用移动速度减益
    if self.inst.components and self.inst.components.locomotor then
        self.inst.components.locomotor:SetExternalSpeedMultiplier(self.inst, "seasonal_slow", multiplier)
    else
        print("[SeasonWorkshop] Warning: Target has no locomotor component for slow effect")
    end

    -- 清理旧任务
    if self.slow_task then
        self.slow_task:Cancel()
        self.slow_task = nil
    end

    -- 设置持续时间
    self.slow_task = self.inst:DoTaskInTime(duration, function()
        if self.inst and self.inst:IsValid() then
            self:ClearSlow()
        else
            print("[SeasonWorkshop] Warning: Debuff instance became invalid during slow effect")
        end
    end)

    if not self.slow_task then
        print("[SeasonWorkshop] Error: Failed to create slow duration task")
    end

    -- 视觉效果
    self:UpdateVisuals()

    print(string.format("[SeasonWorkshop] Slow applied: %.2f speed for %.1fs", multiplier, duration))
end

function SeasonalDebuff:ClearSlow()
    if not TheWorld.ismastersim then return end

    -- 错误处理：检查实例有效性
    if not self.inst or not self.inst:IsValid() then
        print("[SeasonWorkshop] Error: Invalid debuff instance in ClearSlow")
        return
    end

    self.slow_mult = 1.0
    self.slow_duration = 0

    -- 使用DST原生机制，locomotor速度恢复会自动同步

    -- 移除移动速度减益
    if self.inst.components and self.inst.components.locomotor then
        self.inst.components.locomotor:RemoveExternalSpeedMultiplier(self.inst, "seasonal_slow")
    else
        print("[SeasonWorkshop] Warning: No locomotor component found for slow clear")
    end

    -- 清理任务
    if self.slow_task then
        self.slow_task:Cancel()
        self.slow_task = nil
    end

    -- 更新视觉效果
    self:UpdateVisuals()
end

function SeasonalDebuff:ApplyBurn(damage_per_second, duration)
    if not TheWorld.ismastersim then return end

    -- 护盾状态下的异常免疫检查
    if self.inst:HasTag("season_shield_immune") then
        -- 护盾状态下灼伤效果减弱75%
        damage_per_second = damage_per_second * 0.25
        duration = duration * 0.25
    end

    self.burn_damage = damage_per_second
    self.burn_duration = duration

    -- 灼伤效果通过health组件的伤害自动同步到客户端

    -- 清理旧任务
    if self.burn_task then
        self.burn_task:Cancel()
    end
    if self.burn_tick_task then
        self.burn_tick_task:Cancel()
    end

    -- 每秒造成伤害
    self.burn_tick_task = self.inst:DoPeriodicTask(1, function()
        if self.inst.components and self.inst.components.health then
            self.inst.components.health:DoDelta(-self.burn_damage, nil, "seasonal_burn")
        end
    end)

    -- 设置持续时间
    self.burn_task = self.inst:DoTaskInTime(duration, function()
        self:ClearBurn()
    end)

    -- 视觉效果
    self:UpdateVisuals()
end

function SeasonalDebuff:ClearBurn()
    if not TheWorld.ismastersim then return end

    self.burn_damage = 0
    self.burn_duration = 0

    -- 灼伤清除无需特殊同步

    -- 清理任务
    if self.burn_task then
        self.burn_task:Cancel()
        self.burn_task = nil
    end
    if self.burn_tick_task then
        self.burn_tick_task:Cancel()
        self.burn_tick_task = nil
    end

    -- 更新视觉效果
    self:UpdateVisuals()
end

function SeasonalDebuff:UpdateVisuals()
    if not self.inst.AnimState then return end

    local r, g, b = 0, 0, 0

    -- 减速效果：蓝色调
    if self.slow_mult < 1.0 then
        local intensity = (1.0 - self.slow_mult) * 0.3 -- 最大30%蓝色调
        b = intensity
    end

    -- 灼伤效果：红色调
    if self.burn_damage > 0 then
        local intensity = math.min(self.burn_damage / 5, 0.3) -- 最大30%红色调
        r = intensity
    end

    self.inst.AnimState:SetAddColour(r, g, b, 0)
end

function SeasonalDebuff:ClearVisuals()
    if self.inst.AnimState then
        self.inst.AnimState:SetAddColour(0, 0, 0, 0)
    end
end

function SeasonalDebuff:GetSlowMultiplier()
    return self.slow_mult
end

function SeasonalDebuff:IsSlowed()
    return self.slow_mult < 1.0
end

function SeasonalDebuff:GetBurnDamage()
    return self.burn_damage
end

function SeasonalDebuff:IsBurning()
    return self.burn_damage > 0
end

-- 数据持久化：保存季节减益状态
function SeasonalDebuff:OnSave()
    local slow_time_left = 0
    local burn_time_left = 0

    -- 计算剩余时间
    if self.slow_task and self.slow_task.GetTimeLeft then
        slow_time_left = self.slow_task:GetTimeLeft()
    end
    if self.burn_task and self.burn_task.GetTimeLeft then
        burn_time_left = self.burn_task:GetTimeLeft()
    end

    return {
        slow_mult = self.slow_mult,
        slow_duration = self.slow_duration,
        slow_time_left = slow_time_left,
        burn_damage = self.burn_damage,
        burn_duration = self.burn_duration,
        burn_time_left = burn_time_left
    }
end

-- 数据持久化：加载季节减益状态
function SeasonalDebuff:OnLoad(data)
    if not TheWorld.ismastersim then return end

    if data then
        -- 恢复减速效果
        if data.slow_time_left and data.slow_time_left > 0 then
            self:ApplySlow(data.slow_mult or 1.0, data.slow_time_left, false)
        end

        -- 恢复灼伤效果
        if data.burn_time_left and data.burn_time_left > 0 then
            self:ApplyBurn(data.burn_damage or 0, data.burn_time_left)
        end

        print(string.format("[SeasonWorkshop] Seasonal debuff loaded: slow=%.2f(%.1fs), burn=%.1f(%.1fs)",
              data.slow_mult or 1.0, data.slow_time_left or 0,
              data.burn_damage or 0, data.burn_time_left or 0))
    end
end

-- 组件清理：确保资源正确释放
function SeasonalDebuff:OnRemoveFromEntity()
    -- 清理所有任务
    if self.slow_task then
        self.slow_task:Cancel()
        self.slow_task = nil
    end
    if self.burn_task then
        self.burn_task:Cancel()
        self.burn_task = nil
    end
    if self.burn_tick_task then
        self.burn_tick_task:Cancel()
        self.burn_tick_task = nil
    end

    -- 清理移动速度修正
    if self.inst and self.inst:IsValid() and self.inst.components and self.inst.components.locomotor then
        self.inst.components.locomotor:RemoveExternalSpeedMultiplier(self.inst, "seasonal_slow")
    end

    -- 清理视觉效果
    self:ClearVisuals()

    -- 组件移除时效果已通过原生机制清理

    print("[SeasonWorkshop] Seasonal debuff component cleaned up")
end

return SeasonalDebuff
