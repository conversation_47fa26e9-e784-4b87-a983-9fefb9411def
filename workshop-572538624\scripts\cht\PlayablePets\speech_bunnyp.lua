return {
	ACTIONFAIL =
	{
		BUILD =
		{
			MOUNTED = "太高了！",
		},
		SHAVE =
		{
			AWAKEBEEFALO = "或許等到它睡死了。",
			GENERIC = "兔男不能刮！",
			NOBITS = "沒有了！",
		},
		STORE =
		{
			GENERIC = "沒空間！",
			NOTALLOWED = "不！",
			INUSE = "正在使用！",
		},
		RUMMAGE =
		{
			GENERIC = "不能做這個！",
			INUSE = "正在使用！",
		},
		COOK =
		{
			GENERIC = "不能做這個！",
			INUSE = "必須等等！",
			TOOFAR = "太遠了！",
		},
		GIVE =
		{
			DEAD = "兔男拿著這個！",
			SLEEPING = "太困了！",
			BUSY = "或許再等等！",
		},
		GIVETOPLAYER =
		{
			FULL = "滿了！",
			DEAD = "死了",
			SLEEPING = "太困了！",
			BUSY = "或許再等等！",
		},
		GIVEALLTOPLAYER =
		{
			FULL = "滿了",
			DEAD = "死了",
			SLEEPING = "太困了！",
			BUSY = "或許再等等！",
		},
		WRITE =
		{
			GENERIC = "不能做這個！",
			INUSE = "朋友先亂寫的！",
		},
		CHANGEIN =
		{
			GENERIC = "不！",
			BURNING = "太嚇人了！",
			INUSE = "等等才輪到妳！",
		},
		ATTUNE =
		{
			NOHEALTH = "感覺不太好！",
		},
		MOUNT =
		{
			TARGETINCOMBAT = "太生氣了！",
			INUSE = "我太遲了！",
		},
		SADDLE =
		{
			TARGETINCOMBAT = "它生氣時不讓我做這個。",
		},
	},
	ACTIONFAIL_GENERIC = "不！",
	ANNOUNCE_ADVENTUREFAIL = "我下次會做得更好！",
	ANNOUNCE_MOUNT_LOWHEALTH = "毛還好嗎？",
	ANNOUNCE_BEES = "嗡嗡嗡的蟲子！",
	ANNOUNCE_BOOMERANG = "木頭混蛋！",
	ANNOUNCE_CHARLIE = "怪物在這裏！",
	ANNOUNCE_CHARLIE_ATTACK = "受傷了！幫助兔男！",
	ANNOUNCE_COLD = "好冷！",
	ANNOUNCE_HOT = "好熱！",
	ANNOUNCE_CRAFTING_FAIL = "東西不夠！",
	ANNOUNCE_DEERCLOPS = "兔男感覺不好！",
	ANNOUNCE_DUSK = "天太暗了！",
	ANNOUNCE_EAT =
	{
		GENERIC = "好吃！",
		PAINFUL = "食物傷害！",
		SPOILED = "食物總量",
		STALE = "食物不新鮮",
		INVALID = "兔男有標準",
		YUCKY = "我拒絕！",
	},
	ANNOUNCE_ENTER_DARK = "天黑了！",
	ANNOUNCE_ENTER_LIGHT = "有光就好！",
	ANNOUNCE_FREEDOM = "我，自由！",
	ANNOUNCE_HIGHRESEARCH = "我，天才！",
	ANNOUNCE_HOUNDS = "危險的聲音",
	ANNOUNCE_WORMS = "我感覺到了有東西靠近！",
	ANNOUNCE_HUNGRY = "我餓了！",
	ANNOUNCE_HUNT_BEAST_NEARBY = "我聞到野獸在這裏！",
	ANNOUNCE_HUNT_LOST_TRAIL = "我找不到蹤跡",
	ANNOUNCE_HUNT_LOST_TRAIL_SPRING = "道路太濕了！",
	ANNOUNCE_INV_FULL = "我太滿了",
	ANNOUNCE_KNOCKEDOUT = "我的頭很痛...",
	ANNOUNCE_LOWRESEARCH = "我學會了點",
	ANNOUNCE_MOSQUITOS = "煩人的蟲子！",
	ANNOUNCE_NOWARDROBEONFIRE = "太熱了！",
	ANNOUNCE_NODANGERGIFT = "現在很危險！",
	ANNOUNCE_NODANGERSLEEP = "太嚇人了！",
	ANNOUNCE_NODAYSLEEP = "不是睡眠時間！",
	ANNOUNCE_NODAYSLEEP_CAVE = "我不累",
	ANNOUNCE_NOHUNGERSLEEP = "我沒有睡覺的小吃",
	ANNOUNCE_NOSLEEPONFIRE = "太熱了！",
	ANNOUNCE_NODANGERSIESTA = "太嚇人了！",
	ANNOUNCE_NONIGHTSIESTA = "太暗了！",
	ANNOUNCE_NONIGHTSIESTA_CAVE = "洞穴太可怕！",
	ANNOUNCE_NOHUNGERSIESTA = "我太餓了！",
	ANNOUNCE_NODANGERAFK = "我必須攻擊！",
	ANNOUNCE_NO_TRAP = "太容易了！",
	ANNOUNCE_PECKED = "壞鳥！",
	ANNOUNCE_QUAKE = "地面太生氣了！",
	ANNOUNCE_RESEARCH = "我聰明了！",
	ANNOUNCE_SHELTER = "樹，幫我！",
	ANNOUNCE_THORNS = "植物是小氣鬼",
	ANNOUNCE_BURNT = "熱！",
	ANNOUNCE_TORCH_OUT = "發光的樹枝走了！",
	ANNOUNCE_FAN_OUT = "風扇走了！",
	ANNOUNCE_COMPASS_OUT = "上吧！兔男！",
	ANNOUNCE_TRAP_WENT_OFF = "我失敗了！",
	ANNOUNCE_UNIMPLEMENTED = "也許還沒有準備好！",
	ANNOUNCE_WORMHOLE = "洞是可怕的！",
	ANNOUNCE_CANFIX = "\n我可以幫助！",
	ANNOUNCE_ACCOMPLISHMENT = "我做到了！ ",
	ANNOUNCE_ACCOMPLISHMENT_DONE = "我必須給村民看！",
	ANNOUNCE_INSUFFICIENTFERTILIZER = "植物是餓了嗎？",
	ANNOUNCE_TOOL_SLIP = "回來！",
	ANNOUNCE_LIGHTNING_DAMAGE_AVOIDED = "我太強大了！",

	ANNOUNCE_DAMP = "兔男濕了！",
	ANNOUNCE_WET = "兔男的衣服濕了",
	ANNOUNCE_WETTER = "我討厭潮濕",
	ANNOUNCE_SOAKED = "我太濕了！",

	ANNOUNCE_BECOMEGHOST = "ooOOoooOOOoOoooink！！",
	ANNOUNCE_GHOSTDRAIN = "我失去了理智！",

	DESCRIBE_SAMECHARACTER = "我看起來更好！",

	BATTLECRY =
	{
		GENERIC = "我攻擊！",
		PIG = "其他的兔男真醜！",
		PREY = "現在摧毀它！",
		SPIDER = "兔男討厭蜘蛛！",
		SPIDER_WARRIOR = "兔男更討厭黃色的蜘蛛！",
	},
	COMBAT_QUIT =
	{
		GENERIC = "兔男太嚇人！",
		PIG = "兔男讓他活到現在。",
		PREY = "它太害怕兔男！兔男贏了！",
		SPIDER = "妳不值得呢！",
		SPIDER_WARRIOR = "再見了，笨蛋！",
	},
	DESCRIBE =
	{

		BERNIE_INACTIVE =
		{
			BROKEN = "可憐的玩具。",
			GENERIC = "玩具被燒了。",
		},
		BERNIE_ACTIVE = "玩具正在移動！",


		PLAYER =
		{
			GENERIC = "%s 是朋友！",
			ATTACKER = "我不相信 %s...",
			MURDERER = "%s 不是朋友！",
			REVIVER = "%s 死了很多。",
			GHOST = "%s 陰森森的。",
		},
		WILSON =
		{
			GENERIC = "頭發有趣的人",
			ATTACKER = "威爾遜看上去狡猾...",
			MURDERER = "殺人兇手！",
			REVIVER = "威爾遜，朋友的鬼魂。",
			GHOST = "威爾遜可以使用壹個心。",
		},
		WOLFGANG =
		{
			GENERIC = "這是沃爾夫岡！",
			ATTACKER = "沃爾夫岡看起來很狡猾...",
			MURDERER = "殺人兇手！",
			REVIVER = "沃爾夫岡，朋友的鬼魂。",
			GHOST = "沃爾夫岡可以使用壹個心。",
		},
		WAXWELL =
		{
			GENERIC = "這是麥斯威爾！",
			ATTACKER = "麥斯威爾看起來很狡猾...",
			MURDERER = "殺人兇手！",
			REVIVER = "麥斯威爾，朋友的鬼魂。",
			GHOST = "麥斯威爾可以使用壹個心。",
		},
		WX78 =
		{
			GENERIC = "這是WX-78！",
			ATTACKER = "WX-78看起來很狡猾...",
			MURDERER = "殺人兇手！",
			REVIVER = "WX-78，朋友的鬼魂。",
			GHOST = "WX-78可以使用壹個心。",
		},
		WILLOW =
		{
			GENERIC = "這是薇洛！",
			ATTACKER = "薇洛看起來很狡猾...",
			MURDERER = "殺人兇手！",
			REVIVER = "薇洛，朋友的鬼魂。",
			GHOST = "薇洛可以使用壹個心。",
		},
		WENDY =
		{
			GENERIC = "這是溫蒂！",
			ATTACKER = "溫蒂看起來很狡猾...",
			MURDERER = "殺人兇手！",
			REVIVER = "溫蒂，朋友的鬼魂。",
			GHOST = "溫蒂可以使用壹個心。",
		},
		WOODIE =
		{
			GENERIC = "這是伍迪！",
			ATTACKER = "伍迪看起來很狡猾...",
			MURDERER = "殺人兇手！",
			REVIVER = "伍迪，朋友的鬼魂。",
			GHOST = "伍迪可以使用壹個心。",
		},
		WICKERBOTTOM =
		{
			GENERIC = "這是薇克波頓！",
			ATTACKER = "薇克波頓看起來很狡猾...",
			MURDERER = "殺人兇手！",
			REVIVER = "薇克波頓，朋友的鬼魂。",
			GHOST = "薇克波頓可以使用壹個心。",
		},
		WES =
		{
			GENERIC = "這是韋斯！",
			ATTACKER = "韋斯看起來很狡猾...",
			MURDERER = "殺人兇手！",
			REVIVER = "韋斯，朋友的鬼魂。",
			GHOST = "韋斯可以使用壹個心。",
		},
		MULTIPLAYER_PORTAL = "我用這個去了新的地方",
		MIGRATION_PORTAL = {
			GENERIC = "如果我有朋友，這可以帶我去他們那。",
			OPEN = "如果我壹步壹步過去，我還是我嗎？",
			FULL = "似乎很受歡迎。",
		},
		GLOMMER = "他很可愛！",
		GLOMMERFLOWER =
		{
			GENERIC = "花的氣味",
			DEAD = "現在，花和死亡的氣味",
		},
		GLOMMERWINGS = "我會想妳的！",
		GLOMMERFUEL = "這是寶貴的便便",
		BELL = "它將召喚眾神",
		STATUEGLOMMER =
		{
			GENERIC = "蜘蛛俠雕像",
			EMPTY = "蜘蛛俠雕像不是朋友",
		},

		WEBBERSKULL = "繼續死著",
		WORMLIGHT = "那是食物嗎？",
		WORMLIGHT_LESSER = "那是食物嗎？",
		WORM =
		{
			PLANT = "我想植物",
			DIRT = "臭汙垢",
			WORM = "蠕蟲的意思",
		},
		WORMLIGHT_PLANT = "我想要它",
		MOLE =
		{
			HELD = "妳好",
			UNDERGROUND = "我最好看壹下我掉的東西",
			ABOVEGROUND = "妳好小鼴鼠！",
		},
		MOLEHILL = "它像壹個兔男洞，但是蠕蟲的",
		MOLEHAT = "兔男為蠕蟲感到抱歉",

		EEL = "豬很快就會吃",
		EEL_COOKED = "會讓豬饑餓",
		UNAGI = "豬是好菜",
		EYETURRET = "我現在感到很安全",
		EYETURRET_ITEM = "這有助於兔男",
		MINOTAURHORN = "古老的巖石碎片",
		MINOTAURCHEST = "兔男必須打開！",
		THULECITE_PIECES = "小的金子碎片",
		POND_ALGAE = "醜陋的東西",
		GREENSTAFF = "兔男最後還是很效率",
		POTTEDFERN = "兔男喜歡這個",

		THULECITE = "在過去使用過",
		ARMORRUINS = "上帝之觸",
		RUINS_BAT = "兇手的武器",
		RUINSHAT = "兔男將大說特說。",
		NIGHTMARE_TIMEPIECE =
		{
			CALM = "壹切都很好",
			WARN = "也許很好",
			WAXING = "不是很好",
			STEADY = "仍然不好",
			WANING = "變好了嗎？",
			DAWN = "現在很好",
			NOMAGIC = "壹切完美",
		},
		BISHOP_NIGHTMARE = "我看起來更好了",
		ROOK_NIGHTMARE = "壞了仍舊不友好",
		KNIGHT_NIGHTMARE = "即使壞了我也不喜歡妳",
		MINOTAUR = "我聽過妳的故事",
		SPIDER_DROPPER = "蜘蛛在洞裏的天上嗎？",
		NIGHTMARELIGHT = "它在我的噩夢裏燃燒",
		NIGHTSTICK = "這令人震驚",
		GREENGEM = "綠寶石是閃亮的",
		RELIC = "兔男有更好的東西",
		RUINS_RUBBLE = "兔男會幫助妳把這個做的更好",
		MULTITOOL_AXE_PICKAXE = "兔男制造最好的工具",
		ORANGESTAFF = "我覺得拿著這個就像國王壹樣",
		YELLOWAMULET = "我感到溫暖和富有",
		GREENAMULET = "我更加高效",
		SLURPERPELT = "它還令我忍俊不禁",

		SLURPER = "饑餓的搞笑怪獸",
		SLURPER_PELT = "它還令我忍俊不禁",
		ARMORSLURPER = "很臭的盔甲",
		ORANGEAMULET = "兔男有移動魔法了",
		YELLOWSTAFF = "兔男可以召喚太陽",
		YELLOWGEM = "溫暖的發光的石頭",
		ORANGEGEM = "最喜歡的石頭",
		TELEBASE =
		{
			VALID = "它很好",
			GEMS = "需要更多的邪惡石頭",
		},
		GEMSOCKET =
		{
			VALID = "它很好",
			GEMS = "需要邪惡石頭",
		},
		STAFFLIGHT = "兔男感覺很強大",

		ANCIENT_ALTAR = "和圖紙上的壹樣",

		ANCIENT_ALTAR_BROKEN = "魚人做的嗎？",

		ANCIENT_STATUE = "過去的兔男嗎？",

		LICHEN = "奇怪的植物",
		CUTLICHEN = "奇怪但美味",

		CAVE_BANANA = "從來沒有見過這個",
		CAVE_BANANA_COOKED = "兔男的嘴流著口水",
		CAVE_BANANA_TREE = "好吃好吃樹",
		ROCKY = "它看起來很不友好",

		COMPASS =
		{
			GENERIC="哪條路！",
			N = "北",
			S = "南",
			E = "東",
			W = "西",
			NE = "東北",
			SE = "東南",
			NW = "西北",
			SW = "西南",
		},

		NIGHTMARE_TIMEPIECE =
		{
			WAXING = "意味著事情發生",
			STEADY = "意味著的東西還在這裏",
			WANING = "意味著東西離開了",
			DAWN = "意味著東西幾乎消失了",
			WARN = "兔男預言的事情",
			CALM = "壹切都好",
			NOMAGIC = "意味著這裏沒有什麽",
		},

		HOUNDSTOOTH="壞狗的獎杯",
		ARMORSNURTLESHELL= "我覺得安全！",
		BAT="蜘蛛飛了！？",
		BATBAT = "飛翔蜘蛛的力量",
		BATWING="所有蜘蛛都很臭！",
		BATWING_COOKED="誰會吃這個",
		BATCAVE = "兔男應該小心",
		BEDROLL_FURRY="兔男不知道如何感覺這個！",
		BUNNYMAN="妳好，兄弟！",
		FLOWER_CAVE="科學使它發光。",
		FLOWER_CAVE_DOUBLE="科學使它發光。",
		FLOWER_CAVE_TRIPLE="科學使它發光。",
		GUANO="兔男白大便",
		LANTERN="人類喜歡使用這個",
		LIGHTBULB="安全的燈",
		MANRABBIT_TAIL="它幫助我忘記我的兄弟死了！",
		MUSHTREE_TALL = {
			GENERIC = "食物大樹",
			BLOOM = "好漂亮啊！",
		},
		MUSHTREE_MEDIUM = {
			GENERIC = "不是很好吃的食物樹",
			BLOOM = "它在繁殖",
		},
		MUSHTREE_SMALL = {
			GENERIC = "妳好，食物樹",
			BLOOM = "它射出漂亮的燈光",
		},
		MUSHTREE_TALL_WEBBED = "被肉食者破壞了",
		SPORE_TALL = "妳真漂亮",
		SPORE_MEDIUM = "妳好，小小的光",
		SPORE_SMALL = "是小小的發光朋友嗎？",
		SPORE_TALL_INV = "漂亮的東西是我的！",
		SPORE_MEDIUM_INV = "小小的光喜歡這裏",
		SPORE_SMALL_INV = "朋友在我的口袋裏",
		RABBITHOUSE=
		{
			GENERIC = " 展示",
			BURNT = "現在誰的房子更好？",
		},
		SLURTLE="我認為妳很醜，但是可愛。",
		SLURTLE_SHELLPIECES="這曾經是個家",
		SLURTLEHAT="我現在有醜陋的粘液了。",
		SLURTLEHOLE="我想看看裏面。",
		SLURTLESLIME="和它玩的很好",
		SNURTLE="兔男沒有經常看到他們",
		SPIDER_HIDER="他們喜歡躲起來！",
		SPIDER_SPITTER="蜘蛛在醜陋的房子裏不應該吐出石頭",
		SPIDERHOLE="它很惡心",
		STALAGMITE="大自然是整潔的",
		STALAGMITE_FULL="大自然是整潔的",
		STALAGMITE_LOW="大自然是整潔的",
		STALAGMITE_MED="大自然是整潔的",
		STALAGMITE_TALL="大自然是整潔的",
		STALAGMITE_TALL_FULL="大自然是整潔的",
		STALAGMITE_TALL_LOW="大自然是整潔的",
		STALAGMITE_TALL_MED="大自然是整潔的",

		TURF_CARPETFLOOR = "軟軟的",
		TURF_CHECKERFLOOR = "討厭的人喜歡這個",
		TURF_DIRT = "泥土",
		TURF_FOREST = "家裏的草坪",
		TURF_GRASS = "看起來很舒服",
		TURF_MARSH = "它比較臭",
		TURF_ROAD = "更好的地板",
		TURF_ROCKY = "不會睡在這上面",
		TURF_SAVANNA = "希望毛茸茸的野獸不吃它",
		TURF_WOODFLOOR = "豬王有這個",

		TURF_CAVE="它不軟",
		TURF_FUNGUS="另壹種地面類型。",
		TURF_SINKHOLE="另壹種地面類型。",
		TURF_UNDERROCK="另壹種地面類型。",
		TURF_MUD="另壹種地面類型。",

		TURF_DECIDUOUS = "另壹種地面類型。",
		TURF_SANDY = "另壹種地面類型。",
		TURF_BADLANDS = "另壹種地面類型。",

		POWCAKE = "我不餓",
		CAVE_ENTRANCE = "這通向家",
		CAVE_ENTRANCE_RUINS = "這可能藏著什麽。",
		CAVE_ENTRANCE_OPEN = {
			GENERIC = "哦",
			OPEN = "這兒通向家！",
			FULL = "現在還不能回家",
		},
		CAVE_EXIT = {
			GENERIC = "兔男會在家呆壹會兒",
			OPEN = "兔男想探索地面",
			FULL = "太多人在那裏！",
		},
		MAXWELLPHONOGRAPH = "那就是音樂的來源。",
		BOOMERANG = "神奇的木頭",
		PIGGUARD = "它們不僅是肉食者，而且也很討厭",
		ABIGAIL = "可愛的幽靈",
		ADVENTURE_PORTAL = "我不確定我想來第二次。",
		AMULET = "這個護身符是強大的，我能感覺到",
		ANIMAL_TRACK = "也許奇怪的長毛獸丟下了它",
		ARMORGRASS = "大自然會保護兔男！",
		ARMORMARBLE = "華麗的盔甲",
		ARMORWOOD = "現在兔男安全了！",
		ARMOR_SANITY = "壹個聲音告訴我要穿上它",
		ASH =
		{
			GENERIC = "火留下這個。可憐的東西。",
			REMAINS_GLOMMERFLOWER = "哦，不~",
			REMAINS_EYE_BONE = "哦，不~",
			REMAINS_THINGIE = "這以前可能很酷",
		},
		AXE = "這東西可以砍樹",
		BABYBEEFALO = "妳爸爸去哪兒啦？",
		BACKPACK = "兔男旅行時使用！",
		BACONEGGS = "肉是兇手",
		BANDAGE = "這看起來很酷！",
		BASALT = "罕見的石頭牢不可破",
		BATBAT = "會飛的蜘蛛幫助我",
		BEARDHAIR = "人類有時制造這個",
		BEARGER = "大惡霸喜歡吃我們！",
		BEARGERVEST = "大惡霸的外套",
		ICEPACK = "妳再也不能吃我們了！",
		BEARGER_FUR = "妳再也不能吃我們了！",
		BEDROLL_STRAW = "大自然的床",
		BEE =
		{
			GENERIC = "它可以做漂亮的東西",
			HELD = "妳好，朋友！",
		},
		BEEBOX =
		{
			READY = "它充滿了美味",
			FULLHONEY = "它充滿了美味",
			GENERIC = "蜜蜂！",
			NOHONEY = "沒有好吃的",
			SOMEHONEY = "也許應該等待更多好吃的",
			BURNT = "不會有美味了嗎？",
		},
		BEEFALO =
		{
			FOLLOWER = "長毛獸來了",
			GENERIC = "妳好，長毛獸！",
			NAKED = "抱歉，長毛獸...",
			SLEEPING = "我想在長毛獸邊睡覺。",
			--Domesticated states:
			DOMESTICATED = "兔男的長毛獸是最好的！",
			ORNERY = "長毛獸是強硬的",
			RIDER = "長毛獸很友善",
			PUDGY = "兔男認為妳看起來有趣！",
		},
		BEEFALOHAT = "很軟的帽子！",
		BEEFALOWOOL = "兔男要用這個做床",
		BEEHAT = "蜜蜂不能碰到我了！",
		BEEHIVE = "蜜蜂的房子很吵！",
		BEEMINE = "蜜蜂的球！",
		BEEMINE_MAXWELL = "討厭的人做的蜜蜂球",
		BERRIES = "灌木食物是美味的",
		BERRIES_COOKED = "灌木食物味道壹樣",
		BERRYBUSH =
		{
			BARREN = "妳為什麽不做食物呢？",
			WITHERED = "熱死了我的灌木！",
			GENERIC = "灌木食物是美味的",
			PICKED = "我稍後再回來",
		},
		BIGFOOT = "妳是神嗎？",
		BIRDCAGE =
		{
			GENERIC = "這東西用來放鳥",
			OCCUPIED = "兔男的鳥！",
			SLEEPING = "妳有夢想嗎？",
			HUNGRY = "小鳥餓了！",
			STARVING = "小鳥太餓了！",
			DEAD = "小鳥?",
			SKELETON = "鳥哪去了？",
		},
		BIRDTRAP = "小鳥會是我的",
		BIRD_EGG = "鳥寶寶在這裏",
		BIRD_EGG_COOKED = "糟糕！",
		BISHOP = "奇怪的東西是個混蛋！",
		BLOWDART_FIRE = "兔男會讓東西燃燒！",
		BLOWDART_SLEEP = "我的瞌睡蟲！",
		BLOWDART_PIPE = "冬季兔男有這個",
		BLUEAMULET = "冷冷的！",
		BLUEGEM = "漂亮閃亮寶石！",
		BLUEPRINT = "我喜歡學習！",
		BELL_BLUEPRINT = "這是能做什麽？",
		BLUE_CAP = "臭的食物",
		BLUE_CAP_COOKED = "臭的食物仍然是臭的",
		BLUE_MUSHROOM =
		{
			GENERIC = "地上的食物",
			INGROUND = "夜晚",
			PICKED = "新的不久就來了",
		},
		BOARDS = "我們用這個做房子",
		BOAT = "那是什麽？",
		BONESHARD = "這東西出現在死亡的地方",
		BONESTEW = "嘗起來像死亡",
		BUGNET = "我現在抓蟲子",
		BUSHHAT = "這頭兔男看起來很滑稽",
		BUTTER = "這使得食物美味",
		BUTTERFLY =
		{
			GENERIC = "漂亮的蟲！",
			HELD = "如此美麗！",
		},
		BUTTERFLYMUFFIN = "很好吃",
		BUTTERFLYWINGS = "依然漂亮！",
		BUZZARD = "醜陋的小鳥！",
		CACTUS =
		{
			GENERIC = "會痛的美味食物",
			PICKED = "現在它只是痛了",
		},
		CACTUS_MEAT_COOKED = "熱的食物",
		CACTUS_MEAT = "會痛的美味食物",
		CACTUS_FLOWER = "會痛的美麗食物",

		COLDFIRE =
		{
			EMBERS = "很快就完了",
			GENERIC = "燃燒吧！",
			HIGH = "好冷啊！",
			LOW = "它不多啦",
			NORMAL = "冷的火",
			OUT = "再見，火",
		},
		CAMPFIRE =
		{
			EMBERS = "很快就完了",
			GENERIC = "燃燒吧！",
			HIGH = "好熱啊！",
			LOW = "它不多啦",
			NORMAL = "熱的火",
			OUT = "再見，火",
		},
		CANE = "兔男的動作快了！",
		CATCOON = "KITTY!",
		CATCOONDEN =
		{
			GENERIC = "是KITTY KITTY！",
			EMPTY = "KITTY?...",
		},
		CATCOONHAT = "KITTY的帽子！",
		COONTAIL = "抱歉KITTY...",
		CARROT = "兔人的食物",
		CARROT_COOKED = "熟兔人的食物",
		CARROT_PLANTED = "兔人在嗎？",
		CARROT_SEEDS = "兔人會喜歡這些",
		WATERMELON_SEEDS = "種子",
		CAVE_FERN = "這是蕨類植物。",
		CHARCOAL = "這個又小又黑，聞起來像燒焦的木頭。",
		CHESSJUNK1 = "壹堆討厭的東西",
		CHESSJUNK2 = "我能修理它嗎？",
		CHESSJUNK3 = "他們看起來很傷心...",
		CHESTER = "妳好，毛茸茸的東西！",
		CHESTER_EYEBONE =
		{
			GENERIC = "妳好",
			WAITING = "夜晚",
		},
		COOKEDMANDRAKE = "妳現在很好吃",
		COOKEDMEAT = "現在更好吃了",
		COOKEDMONSTERMEAT = "仍舊不太好吃",
		COOKEDSMALLMEAT = "小但是好吃",
		COOKPOT =
		{
			COOKING_LONG = "兔男應該做壹些美味的東西",
			COOKING_SHORT = "差不多了",
			DONE = "好吃!",
			EMPTY = "它用來做好吃的",
			BURNT = "鍋並不好吃",
		},
		CORN = "好吃的東西",
		CORN_COOKED = "好吃的東西",
		CORN_SEEDS = "種子",
		CROW =
		{
			GENERIC = "妳好！",
			HELD = "哈哈！",
		},
		CUTGRASS = "柔軟，有用",
		CUTREEDS = "沼澤的草",
		CUTSTONE = "光滑的石頭",
		DEADLYFEAST = "兔男不知道這個",
		DEERCLOPS = "好恐怖！！！",
		DEERCLOPS_EYEBALL = "現在我覺得安全了",
		EYEBRELLAHAT =	"兔男不相信這個",
		DEPLETED_GRASS =
		{
			GENERIC = "草?",
		},
		DEVTOOL = "它聞起來有培根味！",
		DEVTOOL_NODEV = "我沒有強大到足以用它",
		DIRTPILE = "奇怪的泥土，我要調查！",
		DIVININGROD =
		{
			COLD = "冷",
			GENERIC = "它發現了... 壹些東西",
			HOT = "兔男太靠近了！",
			WARM = "兔男太靠近了！！",
			WARMER = "兔男太靠近了！！",
		},
		DIVININGRODBASE =
		{
			GENERIC = "兔男從來都不知道那是什麽",
			READY = "也許樹枝桿感覺有什麽東西",
			UNLOCKED = "兔男幹了什麽？",
		},
		DIVININGRODSTART = "那棒子看起來很有用！",
		DRAGONFLY = "這意味著東西會燃燒！",
		ARMORDRAGONFLY = "兔男很熱的證據",
		DRAGON_SCALES = "蠕蟲皮",
		DRAGONFLYCHEST = "最好的箱子",
		LAVASPIT =
		{
			HOT = "那看起來太燙了",
			COOL = "我不會碰它",
		},

		LAVAE = "發燙寶寶",
		LAVAE_PET =
		{
			STARVING = "這聽起來很餓！",
			HUNGRY = "妳餓了嗎？",
			CONTENT = "很自豪的家長",
			GENERIC = "妳好，發燙寶寶！",
		},
		LAVAE_EGG =
		{
			GENERIC = "不知道是什麽在裏面",
		},
		LAVAE_EGG_CRACKED =
		{
			COLD = "需要溫暖！",
			COMFY = "兔男必須是好家長",
		},
		LAVAE_TOOTH = "那顆牙齒從哪裏來的？",

		DRAGONFRUIT = "我們在王聚會吃這些",
		DRAGONFRUIT_COOKED = "他們不會持續很久",
		DRAGONFRUIT_SEEDS = "種子",
		DRAGONPIE = "我敢打賭王會喜歡這個",
		DRUMSTICK = "兇手",
		DRUMSTICK_COOKED = "兇手！",
		DUG_BERRYBUSH = "它屬於地面",
		DUG_GRASS = "它屬於地面",
		DUG_MARSH_BUSH = "它屬於地面",
		DUG_SAPLING = "它屬於地面",
		DURIAN = "真惡心",
		DURIAN_COOKED = "更加惡心了",
		DURIAN_SEEDS = "惡心的種子",
		EARMUFFSHAT = "我的耳朵暖和了",
		EGGPLANT = "怪異的植物",
		EGGPLANT_COOKED = "現在更加怪異了",
		EGGPLANT_SEEDS = "種子",
		DECIDUOUSTREE =
		{
			BURNING = "漂亮的樹太燙了！",
			BURNT = "漂亮的樹不漂亮了嗎？",
			CHOPPED = "它倒下了",
			POISON = "妳討厭我吃堅果嗎？",
			GENERIC = "它是壹棵漂亮的樹",
		},
		ACORN = "樹的蛋",
		ACORN_SAPLING = "妳好，寶寶樹",
		ACORN_COOKED = "樹蛋很好吃",
		BIRCHNUTDRAKE = "壞樹蛋！",
		EVERGREEN =
		{
			BURNING = "兔男應該做些什麽？",
			BURNT = "兔男去幫助已經太晚了",
			CHOPPED = "木材",
			GENERIC = "妳好，樹！",
		},
		EVERGREEN_SPARSE =
		{
			BURNING = "兔男應該做些什麽？",
			BURNT = "兔男去幫助已經太晚了",
			CHOPPED = "木材",
			GENERIC = "這棵樹沒有孩子。",
		},
		EYEPLANT = "妳好，奇怪的花",
		FARMPLOT =
		{
			GENERIC = "現在兔男是農民",
			GROWING = "兔男是好農民",
			NEEDSFERTILIZER = "需要便便",
			BURNT = "現在它燒了",
		},
		FEATHERHAT = "我的鳥",
		FEATHER_CROW = "它是黑色的柔軟的",
		FEATHER_ROBIN = "紅色的柔軟的",
		FEATHER_ROBIN_WINTER = "它是白色的柔軟的",
		FEM_PUPPET = "妳還好嗎？",
		FIREFLIES =
		{
			GENERIC = "妳好，發光的蟲子！",
			HELD = "我喜歡他們燃燒！",
		},
		FIREHOUND = "這東西會放火",
		FIREPIT =
		{
			EMBERS = "它幾乎沒有了",
			GENERIC = "燃燒吧！",
			HIGH = "熱但安全！",
			LOW = "它不多啦！",
			NORMAL = "冷的",
			OUT = "兔男可以以後再啟動它",
		},
		COLDFIREPIT =
		{
			EMBERS = "它幾乎沒有了",
			GENERIC = "燃燒吧！",
			HIGH = "冷啊",
			LOW = "它不多啦！",
			NORMAL = "這還不錯",
			OUT = "兔男可以以後再啟動它",
		},
		FIRESTAFF = "兔男有毀滅壹切的沖動！",
		FIRESUPPRESSOR =
		{
			ON = "防止火災！",
			OFF = "是個英雄！",
			LOWFUEL = "需要更多的木頭！",
		},

		FISH = "兔男不喜歡妳，但喜歡妳的美味！",
		FISHINGROD = "兔男不能用這個",
		FISHSTICKS = "美味的魚是美味的",
		FISHTACOS = "美味的魚是美味的",
		FISH_COOKED = "美味的魚在我的肚子裏",
		FLINT = "成為王的壹步",
		FLOWER = "漂亮！",
		FLOWER_WITHERED = "妳還好嗎？",
		FLOWERHAT = "兔男很漂亮！",
		FLOWER_EVIL = "妳是醜陋的",
		FOLIAGE = "綠色",
		FOOTBALLHAT = "兔男會記得妳",
		FROG =
		{
			DEAD = "好",
			GENERIC = "反派",
			SLEEPING = "毛骨悚然",
		},
		FROGGLEBUNWICH = "混蛋三明治",
		FROGLEGS = "我應該把混蛋煮了",
		FROGLEGS_COOKED = "現在妳的很美味",
		FRUITMEDLEY = "美味！",
		FURTUFT = "暴徒的毛",
		GEARS = "兔男想用這個做東西",
		GHOST = "幽靈！",
		GOLDENAXE = "適合王",
		GOLDENPICKAXE = "希望它不會輕易打破",
		GOLDENPITCHFORK = "如此閃亮！",
		GOLDENSHOVEL = "挖啊挖",
		GOLDNUGGET = "我們用胡蘿蔔交換。",
		GRASS =
		{
			BARREN = "為什麽妳沒有長大！",
			WITHERED = "討厭炎熱！",
			BURNING = "太熱了！",
			GENERIC = "草",
			PICKED = "謝謝草",
		},
		GREEN_CAP = "醜陋的食物",
		GREEN_CAP_COOKED = "還是醜陋？",
		GREEN_MUSHROOM =
		{
			GENERIC = "醜陋的食物",
			INGROUND = "或許再等等！",
			PICKED = "稍後回來",
		},
		GUNPOWDER = "兔男現在變得越來越危險了",
		HAMBAT = "兔男現在感到內疚...",
		HAMMER = "現在兔男可以摧毀壹切！",
		HEALINGSALVE = "它造成疼痛但是會使兔男更好",
		HEATROCK =
		{
			FROZEN = "好冷！",
			COLD = "石頭是冷的！",
			GENERIC = "特殊的石頭可以變熱！",
			WARM = "溫暖的石頭",
			HOT = "非常熱！",
		},
		HOME = "妳好?",
		HOMESIGN =
		{
			GENERIC = "它說'妳在這兒'",
			UNWRITTEN = "空的",
			BURNT = "\"不要玩火柴\"",
		},
		ARROWSIGN_POST =
		{
			GENERIC = "它說'這條路'",
			UNWRITTEN = "空的。",
			BURNT = "\"不要玩火柴\"",
		},
		ARROWSIGN_PANEL =
		{
			GENERIC = "它說'這條路'",
			UNWRITTEN = "空的。",
			BURNT = "\"不要玩火柴\"",
		},
		HONEY = "！",
		HONEYCOMB = "好吃的糖漿做的！",
		HONEYHAM = "好吃的糖漿的家！",
		HONEYNUGGETS = "我的最愛",
		HORN = "兔男現在粗野嗎？",
		HOUND = "獵犬！",
		HOUNDBONE = "或許該走遠點...",
		HOUNDMOUND = "獵犬住在那兒",
		ICEBOX = "它保持食物新鮮",
		ICEHAT = "兔男被凍僵了！",
		ICEHOUND = "冰獵犬！",
		INSANITYROCK =
		{
			ACTIVE = "高大的石頭！",
			INACTIVE = "弱小的石頭！",
		},
		JAMMYPRESERVES = "美味的紅色液體",
		KABOBS = "好吃",
		KILLERBEE =
		{
			GENERIC = "那只蜜蜂很討厭！",
			HELD = "不要傷害我",
		},
		KNIGHT = "兔男想有壹天可以制造妳",
		KOALEFANT_SUMMER = "妳好，奇怪的長毛獸",
		KOALEFANT_WINTER = "奇怪的長毛獸看起來溫暖！",
		KRAMPUS = "走開！ 我的東西！",
		KRAMPUS_SACK = "我拿了妳的東西！",
		LEIF = "樹很生氣！",
		LEIF_SPARSE = "樹很生氣！",
		LIGHTNING_ROD =
		{
			CHARGED = "兔男感覺像是在笑",
			GENERIC = "兔男從天空帶來的光！",
		},
		LIGHTNINGGOAT =
		{
			GENERIC = "妳好",
			CHARGED = "妳為什麽生氣？",
		},
		LIGHTNINGGOATHORN = "小小的棍子",
		GOATMILK = "好吃！",
		LITTLE_WALRUS = "妳爸爸很討厭！",
		LIVINGLOG = "它不喜歡火！",
		LOG =
		{
			BURNING = "燃燒的木頭",
			GENERIC = "兔男會用這個做東西",
		},
		LUREPLANT = "妳好？",
		LUREPLANTBULB = "我也許可以使用妳",
		MALE_PUPPET = "妳還好嗎？",

		MANDRAKE_ACTIVE = "我的時間！",
		MANDRAKE_PLANTED = "兔男沒有看到很多",
		MANDRAKE = "奇怪的植物是神奇的",

		MANDRAKESOUP = "好吃",
		MANDRAKE_COOKED = "再見，奇怪的植物",
		MARBLE = "特殊的石頭！",
		MARBLEPILLAR = "看起來不錯！",
		MARBLETREE = "堅硬的樹",
		MARSH_BUSH =
		{
			BURNING = "再見！",
			GENERIC = "也許兔男沒有觸摸過",
			PICKED = "哼哼！",
		},
		BURNT_MARSH_BUSH = "它死了",
		MARSH_PLANT = "壹個植物",
		MARSH_TREE =
		{
			BURNING = "燒吧！",
			BURNT = "再見！",
			CHOPPED = "現在它只是個樹！",
			GENERIC = "恐怖的樹！",
		},
		MAXWELL = "討厭的人",
		MAXWELLHEAD = "可怕又討厭的人",
		MAXWELLLIGHT = "妳有魔力嗎？",
		MAXWELLLOCK = "看起來像個洞",
		MAXWELLTHRONE = "兔男並不喜歡它",
		MEAT = "肉是原兇！",
		MEATBALLS = "糟糕啊！",
		MEATRACK =
		{
			DONE = "肉幹！",
			DRYING = "肉在變成肉幹",
			DRYINGINRAIN = "雨沒有幫助！",
			GENERIC = "肉到這裏來",
			BURNT = "現在是垃圾了",
		},
		MEAT_DRIED = "肉幹",
		MERM = "兔男討厭他！",
		MERMHEAD =
		{
			GENERIC = "兔男的戰利品",
			BURNT = "兔男可以做新的",
		},
		MERMHOUSE =
		{
			GENERIC = "我們把它做得更好",
			BURNT = "沒有太大的不同",
		},
		MINERHAT = "燃燒吧，腦袋！",
		MONKEY = "妳好，猴子！.",
		MONKEYBARREL = "妳好？",
		MONSTERLASAGNA = "惡心",
		FLOWERSALAD = "惡心但是漂亮",
		ICECREAM = "我喜歡這個",
		WATERMELONICLE = "討厭",
		TRAILMIX = "美味的糖果",
		HOTCHILI = "兔男的嘴巴很熱",
		GUACAMOLE = "討厭",
		MONSTERMEAT = "兔男討厭肉",
		MONSTERMEAT_DRIED = "這肉壹直不好吃",
		MOOSE = "春天的鳥？",
		MOOSEEGG = "好大的蛋！",
		MOSSLING = "兔男打賭妳很軟，兔男會擁抱妳！",
		FEATHERFAN = "把我的溫度降下來",
		MINIFAN = "不知怎的微風從背後吹來。",
		GOOSE_FEATHER = "很軟！",
		STAFF_TORNADO = "圓圓的自旋",
		MOSQUITO =
		{
			GENERIC = "吸血鬼",
			HELD = "他喜歡兔男血！",
		},
		MOSQUITOSACK = "聞起來像兔男血",
		MOUND =
		{
			DUG = "再見了，小偷洞！",
			GENERIC = "好奇藏了什麽？",
		},
		NIGHTLIGHT = "幽靈般的火！",
		NIGHTMAREFUEL = "噩夢！",
		NIGHTSWORD = "亦真亦幻",
		NITRE = "奇怪的石頭是什麽",
		ONEMANBAND = "制作音樂！！！",
		PANDORASCHEST = "舊箱子但是有好吃的東西！",
		PANFLUTE = "音樂會使世界和平",
		PAPYRUS = "希望有壹天會寫點什麽",
		PENGUIN = "奇怪的小鳥很吵",
		PERD = "壞鳥！我的食物！",
		PEROGIES = "好吃",
		PETALS = "美麗的花",
		PETALS_EVIL = "討厭這些",
		PHLEGM = "無用的液體！",
		PICKAXE = "兔男用大腳代替！",
		PIGGYBACK = "豬總算有點用了！",
		PIGHEAD =
		{
			GENERIC = "這是誰做的？",
			BURNT = "安息安息。",
		},
		PIGHOUSE =
		{
			FULL = "妳好，朋友！",
			GENERIC = "朋友的房子！",
			LIGHTSOUT = "兔男從來就不那麽喜歡",
			BURNT = "小兔男感覺不好",
		},
		PIGKING = "最醜的王！",
		PIGMAN =
		{
			DEAD = "噢",
			FOLLOWER = "也許豬不是太壞",
			GENERIC = "他們恨我們，我們恨他們",
			GUARD = "他們是最壞的",
			WEREPIG = "他們像兔男壹樣被詛咒！？",
		},
		PIGSKIN = "這個謀殺案可以很好地加以利用",
		PIGTENT = "豬舍！臭！",
		PIGTORCH = "為什麽那麽重要？",
		PINECONE = "樹的蛋！",
		PINECONE_SAPLING = "寶寶樹將變成大樹！",
		LUMPY_SAPLING = "寶寶樹妳怎麽會在這兒的？",
		PITCHFORK = "基地的設計師！",
		PLANTMEAT = "惡心的肉",
		PLANTMEAT_COOKED = "額，吃得太糟了",
		PLANT_NORMAL =
		{
			GENERIC = "綠葉！",
			GROWING = "長的真慢！",
			READY = "嗯~. 準備收獲！",
			WITHERED = "熱死的。",
		},
		POMEGRANATE = "好的食物！",
		POMEGRANATE_COOKED = "好吃！",
		POMEGRANATE_SEEDS = "種子",
		POND = "魚和青蛙在那裏！",
		POOP = "聞起來惡心！",
		FERTILIZER = "便便桶",
		PUMPKIN = "兔男會喜歡這個",
		PUMPKINCOOKIE = "餅幹！",
		PUMPKIN_COOKED = "南瓜，好吃！",
		PUMPKIN_LANTERN = "幽靈般的光！",
		PUMPKIN_SEEDS = "種子",
		PURPLEAMULET = "兔男討厭聲音。",
		PURPLEGEM = "閃亮的邪惡石頭！",
		RABBIT =
		{
			GENERIC = "小的兔人",
			HELD = "兔男很軟！兔男喜歡小兔男！",
		},
		RABBITHOLE =
		{
			GENERIC = "希望兔人呆在那裏",
			SPRING = "今天至少沒有兔人！",
		},
		RAINOMETER =
		{
			GENERIC = "兔男很無聊",
			BURNT = "兔男不在乎",
		},
		RAINCOAT = "防雨",
		RAINHAT = "幹燥是幸福的",
		RATATOUILLE = "壹種美味",
		RAZOR = "兔男希望有頭發",
		REDGEM = "閃亮的紅色石頭！",
		RED_CAP = "醜陋的食物",
		RED_CAP_COOKED = "依舊惡心",
		RED_MUSHROOM =
		{
			GENERIC = "地上的食物。",
			INGROUND = "夜晚",
			PICKED = "回頭見！",
		},
		REEDS =
		{
			BURNING = "這可不好！",
			GENERIC = "沼澤的草！",
			PICKED = "沒有更多的了！",
		},
		RELIC =
		{
			GENERIC = "古代的家居用品",
			BROKEN = "沒什麽可以在這裏幹的了。",
		},
		RUINS_RUBBLE = "可以修好它！",
		RUBBLE = "以前是某個東西",
		RESEARCHLAB =
		{
			GENERIC = "兔男現在聰明的兔男",
			BURNT = "不！不能沒有它！",
		},
		RESEARCHLAB2 =
		{
			GENERIC = "兔男將成為天才！",
			BURNT = "不！不是天才了！",
		},
		RESEARCHLAB3 =
		{
			GENERIC = "兔男現在變成邪惡的魔法師",
			BURNT = "或許很好",
		},
		RESEARCHLAB4 =
		{
			GENERIC = "兔男是很好的魔法師",
			BURNT = "兔男很傷心",
		},
		RESURRECTIONSTATUE =
		{
			GENERIC = "看起來像人",
			BURNT = "沒多大用處了。",
		},
		RESURRECTIONSTONE = "有用的石頭",
		ROBIN =
		{
			GENERIC = "兔男喜歡紅色的鳥",
			HELD = "妳好，紅鳥。",
		},
		ROBIN_WINTER =
		{
			GENERIC = "妳好，漂亮的小鳥！",
			HELD = "漂亮的小鳥很軟！",
		},
		ROBOT_PUPPET = "妳還好嗎？",
		ROCK_LIGHT =
		{
			GENERIC = "壹個陳舊的熔巖坑。",
			OUT = "看起來很脆弱。",
			LOW = "巖漿正在冷卻。",
			NORMAL = "好看又舒服",
		},
		ROCK = "不知道兔男能否打碎這個",
		ROCK_ICE =
		{
			GENERIC = "冰冷的石頭！",
			MELTED = "冰冷的石頭在哪裏？",
		},
		ROCK_ICE_MELTED = "冰冷的石頭在哪裏？",
		ICE = "妳好，冰",
		ROCKS = "用來制造東西",
		ROOK = "看起來很討厭",
		ROPE = "兔男會做很多東西",
		ROTTENEGG = "臭",
		SADDLE_BASIC = "兔男不能使用這個",
		SADDLE_WAR = "這是什麽？",
		SANITYROCK =
		{
			ACTIVE = "它太大了",
			INACTIVE = "它太小了",
		},
		SAPLING =
		{
			BURNING = "燃燒吧",
			WITHERED = "太陽的樹枝",
			GENERIC = "妳好，樹枝。",
			PICKED = "樹枝不見了",
		},
		SEEDS = "它會長成什麽",
		SEEDS_COOKED = "美味的種子",
		SEWING_KIT = "兔男用這個修補衣服",
		SHOVEL = "兔男要挖",
		SILK = "蜘蛛的糞便",
		SKELETON = "死人",
		SCORCHED_SKELETON = "熟的人很臭",
		SKULLCHEST = "我不知道我是否想打開它。",
		SMALLBIRD =
		{
			GENERIC = "這是壹個相當小的鳥。",
			HUNGRY = "它看起來很餓。",
			STARVING = "它壹定很餓。",
		},
		SMALLMEAT = "死的動物壹小塊",
		SMALLMEAT_DRIED = "肉幹。",
		SPAT = "讓人惡心",
		SPEAR = "兔男不需要這個",
		SPIDER =
		{
			DEAD = "兔男贏了！",
			GENERIC = "蜘蛛是很醜的毛茸茸的混蛋！",
			SLEEPING = "兔男睡得更好！",
		},
		SPIDERDEN = "蜘蛛巢讓人惡心",
		SPIDEREGGSACK = "兔男已經控制！",
		SPIDERGLAND = "蜘蛛內臟",
		SPIDERHAT = "蜘蛛的戰利品",
		SPIDERQUEEN = "王比妳更好",
		SPIDER_WARRIOR =
		{
			DEAD = "兔男贏了！",
			GENERIC = "蜘蛛守衛！",
			SLEEPING = "兔男會殺了妳！",
		},
		SPOILED_FOOD = "現在食物不好了",
		STATUEHARP = "去哪兒呢？",
		STATUEMAXWELL = "卑鄙的人假裝是上帝",
		STEELWOOL = "這個真讓人惡心",
		STINGER = "蜜蜂帶刺",
		STRAWHAT = "兔男喜歡帽子",
		STUFFEDEGGPLANT = "不太好吃！",
		SUNKBOAT = "浮木？",
		SWEATERVEST = "兔男喜歡這個！",
		REFLECTIVEVEST = "現在兔男發光了",
		HAWAIIANSHIRT = "休假的兔男",
		TAFFY = "好吃",
		TALLBIRD = "妳好眼睛鳥！",
		TALLBIRDEGG = "妳好眼睛鳥蛋！",
		TALLBIRDEGG_COOKED = "妳好眼睛鳥蛋食品",
		TALLBIRDEGG_CRACKED =
		{
			COLD = "妳冷嗎？",
			GENERIC = "眼睛鳥寶寶？",
			HOT = "也許太熱了",
			LONG = "兔男是好家長",
			SHORT = "兔男很興奮！",
		},
		TALLBIRDNEST =
		{
			GENERIC = "妳好眼睛鳥蛋！",
			PICKED = "今天沒有眼睛鳥蛋！",
		},
		TEENBIRD =
		{
			GENERIC = "兔男可能艱難的時間",
			HUNGRY = "兔男會餵妳！",
			STARVING = "並不開心",
		},
		TELEBASE =
		{
			VALID = "準備",
			GEMS = "需要邪惡的石頭",
		},
		GEMSOCKET =
		{
			VALID = "邪惡的石頭制造邪惡的聲音",
			GEMS = "它需要邪惡的石頭！",
		},
		TELEPORTATO_BASE =
		{
			ACTIVE = "兔男將進入新世界！",
			GENERIC = "兔男很奇怪",
			LOCKED = "需要壹些東西",
			PARTIAL = "很快",
		},
		TELEPORTATO_BOX = "盒子可能是有用的",
		TELEPORTATO_CRANK = "兔男可以用這個",
		TELEPORTATO_POTATO = "兔男從這感覺到壹些東西",
		TELEPORTATO_RING = "有這個的兔男很有想法",
		TELESTAFF = "兔男很感興趣",
		TENT =
		{
			GENERIC = "兔男喜歡的房子",
			BURNT = "今晚不能睡覺",
		},
		SIESTAHUT =
		{
			GENERIC = "兔男躲避陽光",
			BURNT = "現在它不能拯救兔男！",
		},
		TENTACLE = "那東西真的很不友好",
		TENTACLESPIKE = "兔男不需要這個！",
		TENTACLESPOTS = "兔男認為這讓人惡心",
		TENTACLE_PILLAR = "巨大的地下",
		TENTACLE_PILLAR_HOLE = "兔男很好奇...",
		TENTACLE_PILLAR_ARM = "孩子的東西",
		TENTACLE_GARDEN = "粘糊糊的",
		TOPHAT = "很好的帽子",
		TORCH = "光在動",
		TRANSISTOR = "它嗡嗡叫",
		TRAP = "我捕捉食物",
		TRAP_TEETH = "兔男是聰明的",
		TRAP_TEETH_MAXWELL = "不友好的人討厭兔男！",
		TREASURECHEST =
		{
			GENERIC = "這是箱子",
			BURNT = "再見，箱子",
		},
		TREASURECHEST_TRAP = "兔男被愚弄了嗎？",
		TREECLUMP = "這好像有人試圖阻止我去某個地方。",

		TRINKET_1 = "臭臭的家夥會喜歡這個",
		TRINKET_2 = "臭臭的家夥會喜歡這個",
		TRINKET_3 = "臭臭的家夥會喜歡這個",
		TRINKET_4 = "臭臭的家夥會喜歡這個",
		TRINKET_5 = "臭臭的家夥會喜歡這個",
		TRINKET_6 = "臭臭的家夥會喜歡這個",
		TRINKET_7 = "臭臭的家夥會喜歡這個",
		TRINKET_8 = "臭臭的家夥會喜歡這個",
		TRINKET_9 = "臭臭的家夥會喜歡這個",
		TRINKET_10 = "臭臭的家夥會喜歡這個",
		TRINKET_11 = "臭臭的家夥會喜歡這個",
		TRINKET_12 = "臭臭的家夥會喜歡這個",
		TRINKET_13 = "臭臭的家夥會喜歡這個",
		TRINKET_14 = "臭臭的家夥會喜歡這個",
		TRINKET_15 = "臭臭的家夥會喜歡這個",
		TRINKET_16 = "臭臭的家夥會喜歡這個",
		TRINKET_17 = "臭臭的家夥會喜歡這個",
		TRINKET_18 = "臭臭的家夥會喜歡這個",
		TRINKET_19 = "臭臭的家夥會喜歡這個",
		TRINKET_20 = "臭臭的家夥會喜歡這個",
		TRINKET_21 = "臭臭的家夥會喜歡這個.",
		TRINKET_22 = "臭臭的家夥會喜歡這個.",
		TRINKET_23 = "臭臭的家夥會喜歡這個.",
		TRINKET_24 = "臭臭的家夥會喜歡這個",
		TRINKET_25 = "臭臭的家夥會喜歡這個.",
		TRINKET_26 = "臭臭的家夥會喜歡這個.",
		TRINKET_27 = "臭臭的家夥會喜歡這個.",

		TRUNKVEST_SUMMER = "這讓兔男溫暖。",
		TRUNKVEST_WINTER = "這讓兔男溫暖。",
		TRUNK_COOKED = "誰會吃這個？",
		TRUNK_SUMMER = "野獸的鼻子。",
		TRUNK_WINTER = "模糊的野獸的鼻子",
		TUMBLEWEED = "草的禮物！",
		TURF_CARPETFLOOR = "兔男可以睡在這。",
		TURF_CHECKERFLOOR = "記得不友好的人的兔男",
		TURF_DIRT = "臟。",
		TURF_FOREST = "草。",
		TURF_GRASS = "草。",
		TURF_MARSH = "兔男要在這滾來滾去。",
		TURF_ROAD = "兔男越來越花哨。",
		TURF_ROCKY = "巖石的。",
		TURF_SAVANNA = "草。",
		TURF_WOODFLOOR = "木頭的",
		TURKEYDINNER = "*流口水*",
		TWIGS = "壹個不那麽好吃的樹枝",
		UMBRELLA = "雨啊雨快走開",
		GRASS_UMBRELLA = "雨啊雨快走開",
		UNIMPLEMENTED = "兔男不知道這是什麽",
		WAFFLES = "兔男喜歡華夫餅",
		WALL_HAY =
		{
			GENERIC = "他們不會持有太多.",
			BURNT = "無用的墻！",
		},
		WALL_HAY_ITEM = "脆弱的墻。",
		WALL_STONE = "優於木頭",
		WALL_STONE_ITEM = "優於木頭",
		WALL_RUINS = "老舊但閃亮的墻壁。",
		WALL_RUINS_ITEM = "閃亮的硬墻",
		WALL_WOOD =
		{
			GENERIC = "木制",
			BURNT = "哎呀~",
		},
		WALL_WOOD_ITEM = "保衛者使用這些墻壁",
		WALL_MOONROCK = "兔男利用月亮！",
		WALL_MOONROCK_ITEM = "太空的兔男",
		WALRUS = "冬季兔男很壞",
		WALRUSHAT = "冬季兔男是兔男嗎？",
		WALRUS_CAMP =
		{
			EMPTY = "冬兔男在這裏",
			GENERIC = "可能看起來很暖和，但還是兔男房比較好！",
		},
		WALRUS_TUSK = "兔男用這個會做好東西",
		WARDROBE =
		{
			GENERIC = "現在兔男看起來很酷！",
			BURNING = "不！",
			BURNT = "但是兔男喜歡衣櫃...",
		},
		WARG = "兔男應該離開...",
		WASPHIVE = "蜜蜂的巢穴",
		WATERMELON = "美味的粘粘的食物",
		WATERMELON_COOKED = "美味果汁食品",
		WATERMELONHAT = "希望不要讓兔男頭粘粘的...",
		WETGOOP = "兔男做的惡心食物",
		WHIP = "兔男是長毛獸馴獸師",
		WINTERHAT = "兔男喜歡溫暖而蓬松的帽子",
		WINTEROMETER =
		{
			GENERIC = "兔男不需要這個，但是...",
			BURNT = "兔男用這個很好",
		},
		WORMHOLE =
		{
			GENERIC = "妳好，洞！",
			OPEN = "兔男很好奇它通向哪裏?",
		},
		WORMHOLE_LIMITED = "妳還好嗎？洞。",
		ACCOMPLISHMENT_SHRINE = "我想使用它，我想讓世界知道我做了什麽。",
		LIVINGTREE = "妳好，木頭！",
		ICESTAFF = "兔男是很酷的",
		REVIVER = "兔男將復活人類來幫助兔男",
		LIFEINJECTOR = "兔男喜歡藥物",
		SKELETON_PLAYER =
		{
			MALE = "%s好笨。",
			FEMALE = "%s好笨。",
			ROBOT = "%s好笨。",
			DEFAULT = "%s好笨。",
		},
		HUMANMEAT = "人類的肉看起來有點笨",
		HUMANMEAT_COOKED = "還是惡心",
		HUMANMEAT_DRIED = "額額~",
		MOONROCKNUGGET = "空間石頭",
	},
	DESCRIBE_GENERIC = "玩意兒。",
	DESCRIBE_TOODARK = "太黑了！",
	DESCRIBE_SMOLDERING = "越來越熱！",
	EAT_FOOD =
	{
		TALLBIRDEGG_CRACKED = "有眼睛的鳥好可憐啊！",
	},
}
