_G.ChinesePlus.SetConfigTranslation(mod_to_c,
  "这个间谍已经突破了我们的防线…",
  {

  },
  {
    {
      name = "bknifedmg",
      label = "蝴蝶刀基础伤害",
      options =
      {
        {description = "10", data = 10},
        {description = "17", data = 17},
        {description = "20", data = 20},
        {description = "25", data = 25},
        {description = "27.2(默认)", data = 27.2},
        {description = "29", data = 29},
        {description = "30", data = 30},
        {description = "30.6", data = 30.6},
        {description = "35", data = 35},
      },
      default = 27.2,
    },

    {
      name = "bknifeuses",
      label = "蝴蝶刀使用次数",
      options =
      {
        {description = "33", data = 33},
        {description = "75", data = 75},
        {description = "100", data = 100},
        {description = "150(默认)", data = 150},
        {description = "175", data = 175},
        {description = "200", data = 200},
        {description = "250", data = 250},
      },
      default = 150,
    },

    {
      name = "goldenknifedmg",
      label = "澳元素刀基础伤害",
      options =
      {
        {description = "20", data = 20},
        {description = "25", data = 25},
        {description = "27.2", data = 27.2},
        {description = "30.6(默认)", data = 30.6},
        {description = "35", data = 35},
        {description = "40", data = 40},
        {description = "40.5", data = 40.5},
        {description = "45", data = 45},
        {description = "50", data = 50},
      },
      default = 30.6,
    },

    {
      name = "goldenknifeuses",
      label = "澳元素刀使用次数",
      options =
      {
        {description = "75", data = 75},
        {description = "100", data = 100},
        {description = "150", data = 150},
        {description = "200(默认)", data = 200},
        {description = "250", data = 250},
        {description = "275", data = 275},
        {description = "300", data = 300},
      },
      default = 200,
    },

    {
      name = "kunaidmg",
      label = "苦无基础伤害",
      options =
      {
        {description = "17", data = 17},
        {description = "20", data = 20},
        {description = "25", data = 25},
        {description = "27.2(默认)", data = 27.2},
        {description = "29", data = 29},
        {description = "30", data = 30},
        {description = "30.6", data = 30.6},
        {description = "35", data = 35},
        {description = "40", data = 40},
        {description = "47.5", data = 47.5},
        {description = "50", data = 50},
      },
      default = 27.2,
    },

    {
      name = "kunaiuses",
      label = "苦无使用次数",
      options =
      {
        {description = "33", data = 33},
        {description = "70", data = 70},
        {description = "75", data = 75},
        {description = "100(默认)", data = 100},
        {description = "150", data = 150},
        {description = "175", data = 175},
        {description = "200", data = 200},
        {description = "250", data = 250},
      },
      default = 100,
    },

    {
      name = "scicledmg",
      label = "寒冽冰锥基础伤害",
      options =
      {
        {description = "7", data = 7},
        {description = "10", data = 10},
        {description = "13.6", data = 13.6},
        {description = "15", data = 15},
        {description = "17(默认)", data = 17},
        {description = "20", data = 20},
        {description = "27.2", data = 27.2},
        {description = "29.92", data = 29.92},
        {description = "34", data = 34},
      },
      default = 17,
    },

    {
      name = "scicleuses",
      label = "寒冽冰锥使用次数",
      hover = "不是熔化速度。",
      options =
      {
        {description = "15", data = 15},
        {description = "20", data = 20},
        {description = "25", data = 25},
        {description = "33", data = 33},
        {description = "70(默认)", data = 70},
        {description = "75", data = 75},
        {description = "100", data = 100},
        {description = "175", data = 175},
        {description = "190", data = 190},
        {description = "200", data = 200},
      },
      default = 70,
    },

    {
      name = "boxtrotcrafting",
      label = "盒子戏法",
      hover = "每个人都可以制作盒子戏法?",
      options =
      {
        {description = "是", data = "y"},
        {description = "否", data = "n"},
      },
      default = "y",
    },

  }
)
