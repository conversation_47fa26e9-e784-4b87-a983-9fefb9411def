-- The character select screen lines
STRINGS.CHARACTER_TITLES.crashbandi = "实验品"
STRINGS.CHARACTER_NAMES.crashbandi = "Crash"
STRINGS.CHARACTER_DESCRIPTIONS.crashbandi = "*伴随他的同伴 AKU AKU 出生。\n*有一个大的理智帽子并且比别人快。\n*砸碎箱子，吃了海象！"
STRINGS.CHARACTER_QUOTES.crashbandi = "\"哇！\""

-- Custom speech strings
--STRINGS.CHARACTERS.CRASHBANDI = require(chinesefolder.."/Crash/speech_crashbandi")
STRINGS.CHARACTERS.CRASHBANDI = nil

--STRINGS.NAMES.FOLLOWER = "Aku Aku"
--STRINGS.CHARACTERS.GENERIC.DESCRIBE.FOLLOWER = "An ancient spirit lies within this tiki mask."
STRINGS.NAMES.WUMPA = "Wumpa"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.WUMPA = "如果我收集一百个可以获得额外的生命吗？"
STRINGS.NAMES.WUMPA_COOKED = "烤Wumpa"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.WUMPA_COOKED = "在我的比赛中从来没有看到过！"
STRINGS.NAMES.WUMPA_PLANTED = "Wumpa箱"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.WUMPA_PLANTED = "我可以把这只箱子打碎吗？"

-- The character's name as appears in-game
STRINGS.NAMES.CRASHBANDI = "Crash"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CRASHBANDI =
{
  GENERIC = "这是只袋鼠!",
  ATTACKER = "那只袋鼠看起来很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "Crash, 不是 Casper, 鬼魂朋友！",
  GHOST = "那只袋鼠可以用一颗心。",
}

STRINGS.NAMES.AKUAKU = "Aku Aku"
--STRINGS.CHARACTERS.CRASHBANDI.DESCRIBE.AKUAKU = "Aku Aku!"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.AKUAKU = "我可以发誓这是我从另一个游戏看到的夏威夷面具。"
