name = "Season Workshop (四季工坊)"
version = "0.1.0"
author = "thinking + Augment"
description = [[
四季工坊：围绕季节切换的机制玩法。包含：
- 新角色：季匠（四季被动）
- 装备：季节之刃（连击叠层爆发）、气候披风（随季调校）
- 建筑：季工坊台、季祭坛
- 世界事件：季风乱流（季节宝珠/季芯）
- Boss：季冠树守（机制导向护冠破盾）

全部贴图复用原版改色；联机以服务端权威为准，客户端仅显示特效与提示。
]]

forumthread = ""

-- DST专用API版本
api_version = 10
api_version_dst = 10

-- 兼容性
priority = 0
icon_atlas = nil
icon = nil

-- DST 选项
dont_starve_compatible = false
reign_of_giants_compatible = false
dst_compatible = true
all_clients_require_mod = true
client_only_mod = false
server_filter_tags = {"season","workshop","co-op"}

-- 配置项
configuration_options = {
    {
        name = "boss_hp_mult",
        label = "Boss血量倍率",
        options = {
            {description = "0.8x", data = 0.8},
            {description = "1.0x", data = 1.0},
            {description = "1.2x", data = 1.2},
            {description = "1.5x", data = 1.5},
        },
        default = 1.0,
    },
    {
        name = "boss_shield_absorb",
        label = "Boss护盾减免",
        options = {
            {description = "60%", data = 0.6},
            {description = "70%", data = 0.7},
            {description = "80%", data = 0.8},
            {description = "90%", data = 0.9},
        },
        default = 0.8,
    },
    {
        name = "altar_cooldown_days",
        label = "祭坛冷却(天)",
        options = {
            {description = "3", data = 3},
            {description = "5", data = 5},
            {description = "7", data = 7},
        },
        default = 5,
    },
    {
        name = "gust_frequency",
        label = "季风乱流频率(每季)",
        options = {
            {description = "低(1)", data = 1},
            {description = "中(2)", data = 2},
            {description = "高(3)", data = 3},
        },
        default = 2,
    },
    {
        name = "gust_duration",
        label = "季风乱流持续时间",
        options = {
            {description = "短(6小时)", data = "short"},
            {description = "中(12小时)", data = "medium"},
            {description = "长(24小时)", data = "long"},
        },
        default = "medium",
    },
    {
        name = "blade_fx_strength",
        label = "季刃特效强度",
        options = {
            {description = "低", data = 0},
            {description = "中", data = 1},
            {description = "高", data = 2},
        },
        default = 1,
    },
    {
        name = "cloak_strength",
        label = "披风属性强度",
        options = {
            {description = "低", data = 0},
            {description = "中", data = 1},
            {description = "高", data = 2},
        },
        default = 1,
    },
    {
        name = "blade_stack_strength",
        label = "叠层爆发强度",
        options = {
            {description = "低(0.8)", data = 0},
            {description = "中(1.0)", data = 1},
            {description = "高(1.2)", data = 2},
        },
        default = 1,
    },
    {
        name = "invasion_enabled",
        label = "季Boss野外入侵",
        options = {
            {description = "开启", data = true},
            {description = "关闭", data = false},
        },
        default = true,
    },
    {
        name = "invasion_hp_mul",
        label = "入侵HP倍率",
        options = {
            {description = "0.3", data = 0.3},
            {description = "0.4", data = 0.4},
            {description = "0.5", data = 0.5},
        },
        default = 0.4,
    },
    {
        name = "invasion_loot_mul",
        label = "入侵掉落倍率",
        options = {
            {description = "0.5", data = 0.5},
            {description = "0.75", data = 0.75},
            {description = "1.0", data = 1.0},
        },
        default = 0.5,
    },
    {
        name = "invasion_count",
        label = "入侵次数/季",
        options = {
            {description = "1", data = 1},
            {description = "2", data = 2},
            {description = "3", data = 3},
        },
        default = 2,
    },
    {
        name = "invasion_respawn_days",
        label = "入侵重试(天)",
        options = {
            {description = "1", data = 1},
            {description = "2", data = 2},
            {description = "3", data = 3},
        },
        default = 2,
    },
    {
        name = "vfx_strength",
        label = "爆盾/爆发VFX强度",
        options = {
            {description = "低", data = 0},
            {description = "中", data = 1},
            {description = "高", data = 2},
        },
        default = 1,
    },
}

