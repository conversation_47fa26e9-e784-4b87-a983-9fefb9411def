local _G = GLOBAL
local SpawnPrefab = _G.SpawnPrefab
local GetModConfigData = _G.GetModConfigData
local net_bool = _G.net_bool

local function MakeOrb(name, season)
    local assets = {}
    
    local function fn()
        local inst = CreateEntity()
        inst.entity:AddTransform()
        inst.entity:AddAnimState()
        inst.entity:AddNetwork()
        inst.entity:AddSoundEmitter()

        -- 使用简单的物理体积，但不可拾取
        MakeInventoryPhysics(inst)
        RemovePhysicsColliders(inst)
        -- 移除非典型碰撞体以避免兼容性问题；靠距离判定实现交互

        -- 基础外观：使用stafflight作为基础
        inst.AnimState:SetBank("stafflight")
        inst.AnimState:SetBuild("stafflight")
        inst.AnimState:PlayAnimation("idle_loop", true)

        -- 根据季节设置颜色和发光效果
        if season == "spring" then
            inst.AnimState:SetMultColour(0.5, 1.0, 0.5, 0.8) -- 绿色
        elseif season == "summer" then
            inst.AnimState:SetMultColour(1.0, 0.6, 0.2, 0.8) -- 橙色
        elseif season == "autumn" then
            inst.AnimState:SetMultColour(0.8, 0.5, 0.2, 0.8) -- 褐色
        elseif season == "winter" then
            inst.AnimState:SetMultColour(0.6, 0.8, 1.0, 0.8) -- 蓝色
        end

        -- 设置缩放，让宝珠看起来更显眼
        inst.Transform:SetScale(1.2, 1.2, 1.2)

        inst:AddTag("season_orb")
        inst:AddTag("FX") -- 标记为特效，避免被其他系统干扰
        if season then
            inst:AddTag("season_orb_" .. season)
        end

        -- 网络同步吸收状态
        inst._net_absorbed = net_bool(inst.GUID, "season_orb.absorbed", "absorbed_dirty")

        inst.entity:SetPristine()
        if not TheWorld.ismastersim then
            -- 客户端监听吸收状态变化
            inst:ListenForEvent("absorbed_dirty", function()
                inst._absorbed = inst._net_absorbed:value()
                if inst._absorbed then
                    -- 客户端播放吸收特效
                    local fx = SpawnPrefab("staff_castinglight")
                    if fx then
                        local x, y, z = inst.Transform:GetWorldPosition()
                        fx.Transform:SetPosition(x, y, z)

                        -- 设置特效颜色与宝珠一致
                        if season == "spring" then
                            fx.AnimState:SetMultColour(0.5, 1.0, 0.5, 1.0)
                        elseif season == "summer" then
                            fx.AnimState:SetMultColour(1.0, 0.6, 0.2, 1.0)
                        elseif season == "autumn" then
                            fx.AnimState:SetMultColour(0.8, 0.5, 0.2, 1.0)
                        elseif season == "winter" then
                            fx.AnimState:SetMultColour(0.6, 0.8, 1.0, 1.0)
                        end

                        fx:DoTaskInTime(0.5, fx.Remove)
                    end

                    -- 延迟移除，确保特效播放完成
                    inst:DoTaskInTime(0.6, inst.Remove)
                end
            end)
            return inst
        end

        inst._season = season
        inst._absorbed = false
        inst._net_absorbed:set(false)

        -- 添加光源组件，实现发光效果
        inst:AddComponent("light")
        inst.components.light:SetFalloff(0.7)
        inst.components.light:SetIntensity(0.6)
        inst.components.light:SetRadius(3)
        
        -- 根据季节设置光源颜色
        if season == "spring" then
            inst.components.light:SetColour(0.5, 1.0, 0.5)
        elseif season == "summer" then
            inst.components.light:SetColour(1.0, 0.6, 0.2)
        elseif season == "autumn" then
            inst.components.light:SetColour(0.8, 0.5, 0.2)
        elseif season == "winter" then
            inst.components.light:SetColour(0.6, 0.8, 1.0)
        end
        inst.components.light:Enable(true)

        -- 添加检查组件，用于检测玩家接近
        inst:AddComponent("inspectable")
        inst.components.inspectable.getstatus = function(inst)
            local season_names = {
                spring = "春季",
                summer = "夏季", 
                autumn = "秋季",
                winter = "冬季"
            }
            if inst._season then
                return string.format("%s宝珠，靠近可吸收获得碎片", season_names[inst._season] or "未知")
            else
                return "季节宝珠"
            end
        end

        -- 服务端验证的吸收功能
        local function TryAbsorb(inst, player)
            -- 服务端验证：防止重复吸收
            if inst._absorbed or not player or not player:IsValid() then
                return false
            end

            -- 检查距离
            local dist = inst:GetDistanceSqToInst(player)
            if dist > 4 then -- 2格距离内可吸收
                return false
            end

            -- 原子操作：标记为已吸收
            inst._absorbed = true
            inst._net_absorbed:set(true)

            -- 播放吸收音效
            if inst.SoundEmitter then
                inst.SoundEmitter:PlaySound("dontstarve/common/together/celestial_orb/active")
            end

            -- 生成对应的季节碎片
            local shard_prefab = "season_shard_" .. (inst._season or "autumn")
            local shard = SpawnPrefab(shard_prefab)
            if shard and player.components and player.components.inventory then
                local success = player.components.inventory:GiveItem(shard)

                -- 如果背包满了，掉在地上
                if not success then
                    local x, y, z = player.Transform:GetWorldPosition()
                    shard.Transform:SetPosition(x, 0, z)
                end
            end

            -- 延迟移除，让网络同步和特效有时间播放
            inst:DoTaskInTime(0.7, function()
                if inst:IsValid() then
                    inst:Remove()
                end
            end)

            return true
        end

        -- 优化：降低检查频率，减少性能消耗
        inst:DoPeriodicTask(1.0, function()
            if inst._absorbed then return end

            local x, y, z = inst.Transform:GetWorldPosition()
            local players = TheSim:FindEntities(x, y, z, 3, {"player"}, {"playerghost"})
            for _, player in ipairs(players) do
                if TryAbsorb(inst, player) then
                    break -- 成功吸收后立即退出循环
                end
            end
        end)

        -- 监听季风乱流事件结束，事件结束时消失
        inst:ListenForEvent("seasonal_gust_ended", TheWorld, function()
            if not inst._absorbed and inst:IsValid() then
                -- 播放消失特效
                local fx = SpawnPrefab("staff_castinglight")
                if fx then
                    local x, y, z = inst.Transform:GetWorldPosition()
                    fx.Transform:SetPosition(x, y, z)
                    fx.AnimState:SetMultColour(0.5, 0.5, 0.5, 0.5) -- 灰色表示消失
                    fx:DoTaskInTime(0.3, fx.Remove)
                end
                inst:Remove()
            end
        end)

        return inst
    end

    return Prefab(name, fn, assets)
end

-- 创建四种季节宝珠
local spring_orb = MakeOrb("season_orb_spring", "spring")
local summer_orb = MakeOrb("season_orb_summer", "summer") 
local autumn_orb = MakeOrb("season_orb_autumn", "autumn")
local winter_orb = MakeOrb("season_orb_winter", "winter")

return spring_orb, summer_orb, autumn_orb, winter_orb
