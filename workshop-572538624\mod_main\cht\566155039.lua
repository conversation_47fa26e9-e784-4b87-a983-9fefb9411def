-- The character select screen lines
STRINGS.CHARACTER_TITLES.saitama = "披斗篷的禿子"
STRINGS.CHARACTER_NAMES.saitama = "埼玉"
STRINGS.CHARACTER_DESCRIPTIONS.saitama = "*一\n*拳\n*超人"
STRINGS.CHARACTER_QUOTES.saitama = "\"壓倒性的力量無聊得要命。\""

STRINGS.CHARACTER_TITLES.saitamatwo = "披斗篷的禿子（角色扮演）"
STRINGS.CHARACTER_NAMES.saitamatwo = "埼玉"
STRINGS.CHARACTER_DESCRIPTIONS.saitamatwo = "*一\n*拳\n*超人"
STRINGS.CHARACTER_QUOTES.saitamatwo = "\"不是壓倒性的力量就不無聊了。\""

-- Custom speech strings
--STRINGS.CHARACTERS.SAITAMA = require(chinesefolder.."/Saitama/speech_saitama")
--STRINGS.CHARACTERS.SAITAMATWO = require(chinesefolder.."/Saitama/speech_saitama")
STRINGS.CHARACTERS.SAITAMA = nil
STRINGS.CHARACTERS.SAITAMATWO = nil

-- The character's name as appears in-game
STRINGS.NAMES.SAITAMA = "埼玉"
STRINGS.NAMES.SAITAMATWO = "埼玉"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SAITAMA =
{
  GENERIC = "這是埼玉！",
  ATTACKER = "埼玉看起來很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "埼玉, 鬼魂朋友！",
  GHOST = "埼玉可以使用一顆心。",
}

STRINGS.CHARACTERS.GENERIC.DESCRIBE.SAITAMATWO =
{
  GENERIC = "這是埼玉！",
  ATTACKER = "埼玉看起來很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "埼玉, 鬼魂朋友！",
  GHOST = "埼玉可以使用一顆心。",
}

