-- 性能监控和调试工具
-- 提供性能监控、自动恢复和调试辅助功能

local _G = GLOBAL
local TheWorld = _G.TheWorld

local PerformanceMonitor = {}

-- 性能统计
PerformanceMonitor.stats = {
    network_sync_times = {},
    component_update_times = {},
    memory_usage = {},
    error_recovery_count = 0,
    last_check_time = 0,
    performance_warnings = {}
}

-- 性能阈值配置
PerformanceMonitor.thresholds = {
    slow_network_sync = 0.1,    -- 100ms
    slow_component_update = 0.05, -- 50ms
    memory_leak_threshold = 1000, -- 1000个对象
    error_recovery_interval = 60  -- 60秒
}

-- 开始性能监控
function PerformanceMonitor.StartMonitoring(operation_name)
    local start_time = GetTime()
    
    return function(category)
        local duration = GetTime() - start_time
        category = category or "general"
        
        -- 记录性能数据
        if not PerformanceMonitor.stats[category .. "_times"] then
            PerformanceMonitor.stats[category .. "_times"] = {}
        end
        
        table.insert(PerformanceMonitor.stats[category .. "_times"], {
            operation = operation_name,
            duration = duration,
            timestamp = GetTime()
        })
        
        -- 检查是否超过阈值
        local threshold = PerformanceMonitor.thresholds["slow_" .. category] or 0.1
        if duration > threshold then
            PerformanceMonitor.RecordPerformanceWarning(category, operation_name, duration)
        end
        
        return duration
    end
end

-- 记录性能警告
function PerformanceMonitor.RecordPerformanceWarning(category, operation, duration)
    local warning = {
        category = category,
        operation = operation,
        duration = duration,
        timestamp = GetTime()
    }
    
    table.insert(PerformanceMonitor.stats.performance_warnings, warning)
    
    -- 只保留最近100个警告
    if #PerformanceMonitor.stats.performance_warnings > 100 then
        table.remove(PerformanceMonitor.stats.performance_warnings, 1)
    end
    
    print(string.format("[SeasonWorkshop] Performance warning: %s.%s took %.3fs", 
          category, operation, duration))
end

-- 监控网络同步性能
function PerformanceMonitor.MonitorNetworkSync(component_name, var_name, func)
    local monitor = PerformanceMonitor.StartMonitoring(component_name .. "." .. var_name)
    
    local success, result = pcall(func)
    monitor("network_sync")
    
    if not success then
        print(string.format("[SeasonWorkshop] Network sync failed: %s.%s - %s", 
              component_name, var_name, tostring(result)))
    end
    
    return success, result
end

-- 监控组件更新性能
function PerformanceMonitor.MonitorComponentUpdate(component_name, update_func)
    local monitor = PerformanceMonitor.StartMonitoring(component_name .. ".update")
    
    local success, result = pcall(update_func)
    monitor("component_update")
    
    if not success then
        print(string.format("[SeasonWorkshop] Component update failed: %s - %s", 
              component_name, tostring(result)))
    end
    
    return success, result
end

-- 检查内存使用情况
function PerformanceMonitor.CheckMemoryUsage()
    local current_time = GetTime()
    
    -- 统计各种对象数量
    local memory_info = {
        entities = 0,
        components = 0,
        network_vars = 0,
        tasks = 0,
        timestamp = current_time
    }
    
    -- 统计实体数量
    if _G.Ents then
        for _ in pairs(_G.Ents) do
            memory_info.entities = memory_info.entities + 1
        end
    end
    
    -- 统计网络同步管理器的变量数量（使用forest_network）
    if TheWorld and TheWorld.net and TheWorld.net.components and TheWorld.net.components.network_sync_manager then
        local sync_manager = TheWorld.net.components.network_sync_manager
        for _ in pairs(sync_manager.network_vars or {}) do
            memory_info.network_vars = memory_info.network_vars + 1
        end
    end
    
    table.insert(PerformanceMonitor.stats.memory_usage, memory_info)
    
    -- 只保留最近50个记录
    if #PerformanceMonitor.stats.memory_usage > 50 then
        table.remove(PerformanceMonitor.stats.memory_usage, 1)
    end
    
    return memory_info
end

-- 获取性能报告
function PerformanceMonitor.GetPerformanceReport()
    local report = {
        summary = {},
        warnings = PerformanceMonitor.stats.performance_warnings,
        memory_usage = PerformanceMonitor.stats.memory_usage,
        error_recovery_count = PerformanceMonitor.stats.error_recovery_count
    }
    
    -- 计算各类操作的平均时间
    for category, times in pairs(PerformanceMonitor.stats) do
        if category:find("_times$") and type(times) == "table" then
            local total_time = 0
            local count = 0
            local max_time = 0
            
            for _, record in ipairs(times) do
                total_time = total_time + record.duration
                count = count + 1
                max_time = math.max(max_time, record.duration)
            end
            
            if count > 0 then
                report.summary[category] = {
                    average_time = total_time / count,
                    max_time = max_time,
                    total_operations = count
                }
            end
        end
    end
    
    return report
end

-- 自动性能检查和恢复
function PerformanceMonitor.PerformAutoCheck()
    local current_time = GetTime()
    
    -- 避免过于频繁的检查
    if current_time - PerformanceMonitor.stats.last_check_time < 30 then
        return
    end
    
    PerformanceMonitor.stats.last_check_time = current_time
    
    -- 检查内存使用
    local memory_info = PerformanceMonitor.CheckMemoryUsage()
    
    -- 检查是否需要清理
    local need_cleanup = false
    
    -- 检查警告数量
    if #PerformanceMonitor.stats.performance_warnings > 50 then
        need_cleanup = true
        print("[SeasonWorkshop] Performance: Too many warnings, cleanup needed")
    end
    
    -- 检查内存使用
    if memory_info.entities > PerformanceMonitor.thresholds.memory_leak_threshold then
        need_cleanup = true
        print(string.format("[SeasonWorkshop] Performance: High entity count (%d), cleanup needed", 
              memory_info.entities))
    end
    
    -- 执行清理
    if need_cleanup then
        PerformanceMonitor.PerformCleanup()
    end
end

-- 执行性能清理
function PerformanceMonitor.PerformCleanup()
    print("[SeasonWorkshop] Performing performance cleanup...")
    
    -- 清理旧的性能记录
    for category, times in pairs(PerformanceMonitor.stats) do
        if category:find("_times$") and type(times) == "table" then
            -- 只保留最近100个记录
            while #times > 100 do
                table.remove(times, 1)
            end
        end
    end
    
    -- 清理旧的警告
    while #PerformanceMonitor.stats.performance_warnings > 50 do
        table.remove(PerformanceMonitor.stats.performance_warnings, 1)
    end
    
    -- 清理旧的内存记录
    while #PerformanceMonitor.stats.memory_usage > 30 do
        table.remove(PerformanceMonitor.stats.memory_usage, 1)
    end
    
    PerformanceMonitor.stats.error_recovery_count = PerformanceMonitor.stats.error_recovery_count + 1
    
    print("[SeasonWorkshop] Performance cleanup completed")
end

-- 重置性能统计
function PerformanceMonitor.ResetStats()
    PerformanceMonitor.stats = {
        network_sync_times = {},
        component_update_times = {},
        memory_usage = {},
        error_recovery_count = 0,
        last_check_time = 0,
        performance_warnings = {}
    }
    
    print("[SeasonWorkshop] Performance statistics reset")
end

-- 导出全局访问函数
_G.SeasonWorkshopPerformanceMonitor = PerformanceMonitor

return PerformanceMonitor
