-- 网络同步验证工具
-- 用于验证DST多人联机的网络同步规范

local _G = GLOBAL
local TheWorld = _G.TheWorld
local TheNet = _G.TheNet

local NetworkValidator = {}

-- 验证网络变量是否正确挂载
function NetworkValidator.ValidateNetworkEntity(entity)
    if not entity then
        return false, "Entity is nil"
    end

    if not entity:IsValid() then
        return false, "Entity is not valid"
    end

    if not entity.GUID then
        return false, "Entity has no GUID"
    end

    if not entity.entity then
        return false, "Entity has no entity component"
    end

    -- 检查是否为正确的网络实体
    local is_forest_network = entity == TheWorld.net
    local is_cave_network = entity.prefab == "cave_network"
    local is_player = entity:HasTag("player")
    local has_network_component = entity.Network ~= nil

    if not (is_forest_network or is_cave_network or is_player or has_network_component) then
        return false, "Entity is not a valid network entity"
    end

    -- 额外检查：确保网络实体具有必要的网络功能
    if is_forest_network or is_cave_network then
        -- 这些是官方推荐的网络变量载体
        return true, "Valid official network entity"
    elseif is_player then
        -- 玩家实体也可以承载网络变量
        return true, "Valid player network entity"
    elseif has_network_component then
        -- 其他具有网络组件的实体
        return true, "Valid custom network entity"
    end

    return true, "Valid network entity"
end

-- 验证网络变量命名是否符合最佳实践
function NetworkValidator.ValidateNetworkVariableNaming(name, component_name)
    local valid, msg = NetworkValidator.ValidateNetworkVariableName(name)
    if not valid then
        return false, msg
    end

    -- 检查是否遵循推荐的命名规范
    local expected_prefix = "seasonworkshop_" .. (component_name or "unknown") .. "_"
    if not name:find("^seasonworkshop_") then
        return false, "Network variable should start with 'seasonworkshop_' prefix to avoid conflicts"
    end

    if component_name and not name:find(component_name) then
        return false, "Network variable should include component name for clarity"
    end

    return true, "Follows naming best practices"
end

-- 验证网络变量命名规范
function NetworkValidator.ValidateNetworkVariableName(name)
    if not name or type(name) ~= "string" then
        return false, "Name must be a string"
    end

    if name == "" then
        return false, "Name cannot be empty"
    end

    if #name > 64 then
        return false, "Name too long (max 64 characters)"
    end

    -- 检查是否包含非法字符
    if name:match("[^%w%._%-]") then
        return false, "Name contains invalid characters (only alphanumeric, dot, underscore, hyphen allowed)"
    end

    return true, "Valid network variable name"
end

-- 验证网络同步状态
function NetworkValidator.ValidateNetworkSync()
    local issues = {}
    local warnings = {}

    -- 检查基本网络状态
    if not TheWorld then
        table.insert(issues, "TheWorld not available")
        return false, issues, warnings
    end

    if not TheNet then
        table.insert(issues, "TheNet not available")
        return false, issues, warnings
    end

    -- 检查服务端/客户端状态一致性
    local is_master_sim = TheWorld.ismastersim
    local is_server = TheNet:GetIsServer()
    local is_client = TheNet:GetIsClient()
    local is_dedicated = TheNet:IsDedicated()

    if is_master_sim ~= is_server then
        table.insert(issues, "Inconsistent server state: ismastersim=" .. tostring(is_master_sim) .. ", GetIsServer=" .. tostring(is_server))
    end

    if is_client and is_server and not is_dedicated then
        -- 这是正常的客户端主机模式
        table.insert(warnings, "Client host mode detected")
    end

    -- 检查forest_network可用性
    if not TheWorld.net then
        table.insert(issues, "forest_network not available - this is critical for DST network sync")
    else
        local valid, msg = NetworkValidator.ValidateNetworkEntity(TheWorld.net)
        if not valid then
            table.insert(issues, "forest_network validation failed: " .. msg)
        else
            -- 额外检查forest_network的网络能力
            if not TheWorld.net.GUID then
                table.insert(issues, "forest_network missing GUID")
            end
            if not TheWorld.net.entity then
                table.insert(issues, "forest_network missing entity component")
            end
        end
    end

    -- 检查分片（shard）同步状态
    if TheNet:GetIsServer() then
        -- 在主服务器上检查分片连接
        local shard_id = TheNet:GetServerShardId()
        if shard_id then
            table.insert(warnings, "Running on shard: " .. tostring(shard_id))
        end

        -- 检查是否为主分片
        local is_master_shard = TheNet:GetIsMasterShard()
        if not is_master_shard then
            table.insert(warnings, "Running on secondary shard - some network sync may be limited")
        end
    end

    -- 检查网络协议版本
    local api_version = _G.KnownModIndex and _G.KnownModIndex:GetModInfo("workshop-season-workshop") and
                       _G.KnownModIndex:GetModInfo("workshop-season-workshop").api_version_dst
    if api_version and api_version < 10 then
        table.insert(warnings, "Old API version detected: " .. tostring(api_version) .. " (recommended: 10+)")
    end

    return #issues == 0, issues, warnings
end

-- 验证组件的网络同步设置
function NetworkValidator.ValidateComponentNetworkSync(component, expected_vars)
    if not component then
        return false, {"Component is nil"}
    end

    local issues = {}
    local warnings = {}

    -- 检查网络变量是否存在
    if expected_vars then
        for var_name, var_info in pairs(expected_vars) do
            local net_var = component[var_name]
            if not net_var then
                table.insert(issues, "Missing network variable: " .. var_name)
            else
                -- 检查网络变量类型
                if var_info.type then
                    local expected_type = var_info.type
                    -- 这里可以添加更详细的类型检查
                end
            end
        end
    end

    -- 检查组件是否在正确的实体上
    if component.inst then
        local valid, msg = NetworkValidator.ValidateNetworkEntity(component.inst)
        if not valid then
            table.insert(warnings, "Component attached to invalid network entity: " .. msg)
        end
    end

    return #issues == 0, issues, warnings
end

-- 生成网络同步报告
function NetworkValidator.GenerateNetworkReport()
    local report = {
        timestamp = os.time(),
        world_state = {},
        network_state = {},
        components = {},
        issues = {},
        warnings = {}
    }

    -- 收集世界状态信息
    if TheWorld then
        report.world_state = {
            is_master_sim = TheWorld.ismastersim,
            season = TheWorld.state and TheWorld.state.season or "unknown",
            day = TheWorld.state and TheWorld.state.cycles or 0,
            phase = TheWorld.state and TheWorld.state.phase or "unknown"
        }
    end

    -- 收集网络状态信息
    if TheNet then
        report.network_state = {
            is_server = TheNet:GetIsServer(),
            is_client = TheNet:GetIsClient(),
            is_dedicated = TheNet:IsDedicated(),
            player_count = #(_G.AllPlayers or {})
        }
    end

    -- 验证整体网络同步
    local sync_valid, sync_issues, sync_warnings = NetworkValidator.ValidateNetworkSync()
    for _, issue in ipairs(sync_issues) do
        table.insert(report.issues, issue)
    end
    for _, warning in ipairs(sync_warnings) do
        table.insert(report.warnings, warning)
    end

    -- 检查Season Workshop特定组件
    if TheWorld and TheWorld.net then
        local forest_network = TheWorld.net
        
        -- 检查季风乱流管理器
        if forest_network.components and forest_network.components.seasonal_gust_manager then
            local gust_manager = forest_network.components.seasonal_gust_manager
            local expected_vars = {
                _net_active = {type = "bool"},
                _net_season = {type = "string"},
                _net_remaining = {type = "float"},
                _net_events_left = {type = "tinybyte"},
                _net_next_event_time = {type = "float"}
            }
            
            local valid, issues, warnings = NetworkValidator.ValidateComponentNetworkSync(gust_manager, expected_vars)
            report.components.seasonal_gust_manager = {
                valid = valid,
                issues = issues,
                warnings = warnings
            }
        end

        -- 检查入侵管理器
        if forest_network.components and forest_network.components.season_warden_invasion then
            report.components.season_warden_invasion = {
                valid = true,
                issues = {},
                warnings = {}
            }
        end

        -- 检查季节刻印网络代理
        if forest_network.components and forest_network.components.season_engraving_network then
            local engraving_network = forest_network.components.season_engraving_network
            local expected_vars = {
                player_net_vars = {type = "table"}
            }

            local valid, issues, warnings = NetworkValidator.ValidateComponentNetworkSync(engraving_network, expected_vars)
            report.components.season_engraving_network = {
                valid = valid,
                issues = issues,
                warnings = warnings
            }
        end

        -- 检查网络同步管理器
        if forest_network.components and forest_network.components.network_sync_manager then
            report.components.network_sync_manager = {
                valid = true,
                issues = {},
                warnings = {}
            }
        end
    end

    return report
end

-- 打印网络同步报告
function NetworkValidator.PrintNetworkReport()
    local report = NetworkValidator.GenerateNetworkReport()
    
    print("=== Season Workshop 网络同步报告 ===")
    print("时间戳: " .. (report.timestamp or "unknown"))
    
    print("\n世界状态:")
    print("  主服务器: " .. tostring(report.world_state.is_master_sim))
    print("  季节: " .. (report.world_state.season or "unknown"))
    print("  天数: " .. (report.world_state.day or 0))
    print("  阶段: " .. (report.world_state.phase or "unknown"))
    
    print("\n网络状态:")
    print("  服务器: " .. tostring(report.network_state.is_server))
    print("  客户端: " .. tostring(report.network_state.is_client))
    print("  专用服务器: " .. tostring(report.network_state.is_dedicated))
    print("  玩家数量: " .. (report.network_state.player_count or 0))
    
    if #report.issues > 0 then
        print("\n发现问题:")
        for _, issue in ipairs(report.issues) do
            print("  ❌ " .. issue)
        end
    end
    
    if #report.warnings > 0 then
        print("\n警告:")
        for _, warning in ipairs(report.warnings) do
            print("  ⚠️ " .. warning)
        end
    end
    
    print("\n组件状态:")
    for comp_name, comp_info in pairs(report.components) do
        local status = comp_info.valid and "✅" or "❌"
        print("  " .. status .. " " .. comp_name)
        
        if #comp_info.issues > 0 then
            for _, issue in ipairs(comp_info.issues) do
                print("    ❌ " .. issue)
            end
        end
        
        if #comp_info.warnings > 0 then
            for _, warning in ipairs(comp_info.warnings) do
                print("    ⚠️ " .. warning)
            end
        end
    end
    
    if #report.issues == 0 and #report.warnings == 0 then
        print("\n✅ 网络同步状态良好")
    end
    
    return report
end

-- 检查DST多人联机最佳实践合规性
function NetworkValidator.ValidateDSTBestPractices()
    local issues = {}
    local warnings = {}
    local recommendations = {}

    -- 1. 检查网络变量是否正确挂载在forest_network上
    if TheWorld and TheWorld.net then
        local forest_network = TheWorld.net

        -- 检查Season Workshop组件是否正确挂载
        local expected_components = {
            "seasonal_gust_manager",
            "season_warden_invasion",
            "season_engraving_network",
            "network_sync_manager"
        }

        for _, comp_name in ipairs(expected_components) do
            if not forest_network.components or not forest_network.components[comp_name] then
                table.insert(issues, "Component " .. comp_name .. " not found on forest_network")
            else
                -- 检查组件的网络变量命名
                local component = forest_network.components[comp_name]
                if component._network_initialized == false then
                    table.insert(warnings, "Component " .. comp_name .. " network not properly initialized")
                end
            end
        end
    else
        table.insert(issues, "forest_network not available for component mounting")
    end

    -- 2. 检查网络变量命名规范
    -- 这需要在运行时检查实际的网络变量，这里提供指导
    table.insert(recommendations, "Ensure all network variables use 'seasonworkshop_' prefix")
    table.insert(recommendations, "Include component name in network variable names")
    table.insert(recommendations, "Use GUID suffix to avoid conflicts")

    -- 3. 检查服务端权威性
    if TheWorld and TheWorld.ismastersim then
        table.insert(recommendations, "Server authority correctly established")
    elseif TheNet and TheNet:GetIsClient() then
        table.insert(recommendations, "Client should only read network variables, not set them")
    end

    -- 4. 检查错误处理机制
    table.insert(recommendations, "Implement pcall() for all network variable operations")
    table.insert(recommendations, "Add network delay tolerance for client-side updates")
    table.insert(recommendations, "Validate network data before synchronization")

    -- 5. 检查性能优化
    table.insert(recommendations, "Use throttling for frequently updated network variables")
    table.insert(recommendations, "Minimize network variable count per entity")
    table.insert(recommendations, "Use appropriate data types (tinybyte vs int vs float)")

    -- 6. 检查分片兼容性
    if TheNet and TheNet:GetIsServer() then
        local is_master_shard = TheNet:GetIsMasterShard()
        if not is_master_shard then
            table.insert(warnings, "Secondary shard detected - ensure cross-shard sync is handled")
            table.insert(recommendations, "Use RPC for cross-shard communication")
        end
    end

    return {
        compliant = #issues == 0,
        issues = issues,
        warnings = warnings,
        recommendations = recommendations
    }
end

-- 生成完整的DST合规性报告
function NetworkValidator.GenerateComplianceReport()
    local report = {
        timestamp = os.time(),
        basic_sync = {},
        best_practices = {},
        component_analysis = {},
        overall_score = 0
    }

    -- 基础网络同步检查
    local sync_valid, sync_issues, sync_warnings = NetworkValidator.ValidateNetworkSync()
    report.basic_sync = {
        valid = sync_valid,
        issues = sync_issues,
        warnings = sync_warnings
    }

    -- 最佳实践检查
    report.best_practices = NetworkValidator.ValidateDSTBestPractices()

    -- 组件详细分析
    report.component_analysis = NetworkValidator.GenerateNetworkReport().components

    -- 计算总体合规分数
    local score = 100
    score = score - (#sync_issues * 20)  -- 每个严重问题扣20分
    score = score - (#sync_warnings * 5) -- 每个警告扣5分
    score = score - (#report.best_practices.issues * 15) -- 每个最佳实践问题扣15分
    score = score - (#report.best_practices.warnings * 3) -- 每个最佳实践警告扣3分

    report.overall_score = math.max(0, score)

    return report
end

-- 打印完整的合规性报告
function NetworkValidator.PrintComplianceReport()
    local report = NetworkValidator.GenerateComplianceReport()

    print("=== DST多人联机合规性报告 ===")
    print("时间戳: " .. (report.timestamp or "unknown"))
    print("总体评分: " .. report.overall_score .. "/100")

    if report.overall_score >= 90 then
        print("✅ 优秀 - 完全符合DST多人联机规范")
    elseif report.overall_score >= 75 then
        print("🟡 良好 - 基本符合规范，有少量改进空间")
    elseif report.overall_score >= 60 then
        print("🟠 一般 - 存在一些问题，建议改进")
    else
        print("🔴 需要改进 - 存在严重的合规性问题")
    end

    print("\n=== 基础网络同步 ===")
    if report.basic_sync.valid then
        print("✅ 基础网络同步正常")
    else
        print("❌ 基础网络同步存在问题:")
        for _, issue in ipairs(report.basic_sync.issues) do
            print("  - " .. issue)
        end
    end

    if #report.basic_sync.warnings > 0 then
        print("警告:")
        for _, warning in ipairs(report.basic_sync.warnings) do
            print("  ⚠️ " .. warning)
        end
    end

    print("\n=== 最佳实践合规性 ===")
    if report.best_practices.compliant then
        print("✅ 符合DST最佳实践")
    else
        print("❌ 最佳实践问题:")
        for _, issue in ipairs(report.best_practices.issues) do
            print("  - " .. issue)
        end
    end

    if #report.best_practices.warnings > 0 then
        print("警告:")
        for _, warning in ipairs(report.best_practices.warnings) do
            print("  ⚠️ " .. warning)
        end
    end

    if #report.best_practices.recommendations > 0 then
        print("建议:")
        for _, rec in ipairs(report.best_practices.recommendations) do
            print("  💡 " .. rec)
        end
    end

    print("\n=== 组件分析 ===")
    for comp_name, comp_info in pairs(report.component_analysis) do
        local status = comp_info.valid and "✅" or "❌"
        print("  " .. status .. " " .. comp_name)

        if #comp_info.issues > 0 then
            for _, issue in ipairs(comp_info.issues) do
                print("    ❌ " .. issue)
            end
        end
    end

    return report
end

return NetworkValidator
