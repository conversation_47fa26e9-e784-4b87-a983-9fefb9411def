local assets = {}
local prefabs = {}

-- 引入网络变量
local net_string = GLOBAL.net_string

local function SyncSeasonToClients(inst, season)
    -- 服务端同步季节状态到客户端
    if TheWorld.ismastersim and inst._net_season then
        inst._net_season:set(season or "")
    end
end

local function ApplySeasonStats(inst, owner)
    if not owner or not owner:IsValid() then return end
    local season = TheWorld and TheWorld.state and TheWorld.state.season or "autumn"

    -- 同步季节状态到客户端
    SyncSeasonToClients(inst, season)

    -- 获取披风属性强度配置
    local cloak_strength = GetModConfigData("cloak_strength") or 1
    local strength_mult = cloak_strength == 0 and 0.7 or (cloak_strength == 2 and 1.3 or 1.0)

    -- 先清理之前的饥饿调整
    if owner.components and owner.components.hunger and inst._old_rate then
        owner.components.hunger:SetRate(inst._old_rate)
        inst._old_rate = nil
    end

    -- 默认清空，再按季节设置
    if inst.components.waterproofer then
        inst.components.waterproofer:SetEffectiveness(0)
    end
    if inst.components.insulator then
        inst.components.insulator:SetInsulation(0)
        inst.components.insulator.type = nil
    end

    -- 根据季节调整颜色和属性（使用统一颜色方案的柔和版本）
    if season == "spring" then
        inst.AnimState:SetMultColour(0.7, 1.0, 0.7, 1.0) -- 春季绿色调（柔和版）
        if inst.components.waterproofer then
            inst.components.waterproofer:SetEffectiveness(0.4 * strength_mult)
        end
    elseif season == "summer" then
        inst.AnimState:SetMultColour(1.0, 0.8, 0.6, 1.0) -- 夏季橙色调（柔和版）
        if inst.components.insulator then
            -- 夏季隔热：设置夏季隔热值
            inst.components.insulator:SetInsulation(TUNING.CLIMATE_CLOAK_HEAT * strength_mult)
            inst.components.insulator.type = "summer"
        end
    elseif season == "autumn" then
        inst.AnimState:SetMultColour(0.9, 0.7, 0.6, 1.0) -- 秋季褐色调（柔和版）
        -- 饥饿-5%：基于当前饥饿速率进行调整（与角色被动叠加）
        if owner.components and owner.components.hunger then
            inst._old_rate = owner.components.hunger:GetRate()
            -- 在当前速率基础上再减少5%，应用强度倍率
            local hunger_reduction = 0.05 * strength_mult
            owner.components.hunger:SetRate(inst._old_rate * (1 - hunger_reduction))
        end
    elseif season == "winter" then
        inst.AnimState:SetMultColour(0.8, 0.9, 1.0, 1.0) -- 冬季蓝色调（柔和版）
        if inst.components.insulator then
            -- 冬季保温：设置冬季保温值
            inst.components.insulator:SetInsulation(TUNING.CLIMATE_CLOAK_COLD * strength_mult)
            inst.components.insulator.type = "winter"
        end
    end
end

local function ClearOwnerAdjust(inst, owner)
    if owner and owner.components and owner.components.hunger and inst._old_rate then
        owner.components.hunger:SetRate(inst._old_rate)
    end
    inst._old_rate = nil
end

local function onequip(inst, owner)
    owner.AnimState:OverrideSymbol("swap_body", "sweatervest", "swap_body") -- 基于trunkvest/夏装
    ApplySeasonStats(inst, owner)
    if inst._seasonwatch ~= nil then inst._seasonwatch:Cancel() end
    inst._seasonwatch = owner:WatchWorldState("season", function()
        ApplySeasonStats(inst, owner)
    end)
end

local function onunequip(inst, owner)
    owner.AnimState:ClearOverrideSymbol("swap_body")
    if inst._seasonwatch ~= nil then
        inst._seasonwatch:Cancel()
        inst._seasonwatch = nil
    end
    ClearOwnerAdjust(inst, owner)
end

local function fn()
    local inst = CreateEntity()
    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddNetwork()

    MakeInventoryPhysics(inst)

    inst.AnimState:SetBank("trunkvest")
    inst.AnimState:SetBuild("trunkvest")
    inst.AnimState:PlayAnimation("idle")

    -- 默认颜色（四季融合的中性色调）
    inst.AnimState:SetMultColour(0.9, 0.85, 0.8, 1.0)

    -- 网络变量初始化
    inst._net_season = net_string(inst.GUID, "climate_cloak.season", "cloak_season_dirty")

    inst.entity:SetPristine()

    inst:AddTag("waterproofer")

    if not TheWorld.ismastersim then
        -- 客户端监听季节变化
        inst:ListenForEvent("cloak_season_dirty", function()
            if inst and inst:IsValid() then
                local season = inst._net_season:value()
                if season and season ~= "" then
                    -- 客户端更新视觉效果
                    local colors = {
                        spring = {0.7, 1.0, 0.7, 1.0},
                        summer = {1.0, 0.8, 0.6, 1.0},
                        autumn = {0.9, 0.7, 0.6, 1.0},
                        winter = {0.8, 0.9, 1.0, 1.0}
                    }
                    local color = colors[season]
                    if color and inst.AnimState then
                        inst.AnimState:SetMultColour(color[1], color[2], color[3], color[4])
                    end
                end
            end
        end)
        return inst
    end

    inst:AddComponent("inspectable")
    inst:AddComponent("inventoryitem")

    inst:AddComponent("equippable")
    inst.components.equippable.equipslot = GLOBAL.EQUIPSLOTS.BODY
    inst.components.equippable:SetOnEquip(onequip)
    inst.components.equippable:SetOnUnequip(onunequip)

    inst:AddComponent("waterproofer")
    inst.components.waterproofer:SetEffectiveness(0)

    inst:AddComponent("insulator")
    inst.components.insulator:SetInsulation(0)

    -- 数据持久化
    inst.OnSave = function(inst, data)
        data.current_season = inst._current_season
    end

    inst.OnLoad = function(inst, data)
        if data and data.current_season then
            inst._current_season = data.current_season
            if inst._net_season then
                inst._net_season:set(inst._current_season)
            end
            -- 重新应用季节效果（修复：移除未定义函数，安全调用现有应用函数）
            inst:DoTaskInTime(0, function()
                if inst and inst:IsValid() then
                    -- 如果已被穿戴则对当前持有者重新应用数值，否则仅更新外观状态
                    local owner = inst.components and inst.components.inventoryitem and inst.components.inventoryitem.owner or nil
                    if owner ~= nil then
                        ApplySeasonStats(inst, owner)
                    end
                end
            end)
        end
    end

    return inst
end

return Prefab("climate_cloak", fn, assets, prefabs)
