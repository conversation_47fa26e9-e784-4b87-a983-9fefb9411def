-- The character select screen lines
STRINGS.CHARACTER_TITLES.devon = "猎人"
STRINGS.CHARACTER_NAMES.devon = "德文"
STRINGS.CHARACTER_DESCRIPTIONS.devon = "*生存主义者：不需要食物；快速而弱小。 \n*曾于死亡擦肩而过，现在他看到它无处不在。\n*他最好的朋友，柏蒂"
STRINGS.CHARACTER_QUOTES.devon = "\"你从哪儿下车了，小鸟？\""

-- Custom speech strings
--STRINGS.CHARACTERS.DEVON = require(chinesefolder.."/Devon/speech_devon")
STRINGS.CHARACTERS.DEVON = nil

STRINGS.NAMES.BIRDY = "柏蒂"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.BIRDY = "嘿，漂亮的小鸟！"

-- The character's name as appears in-game
STRINGS.NAMES.DEVON = "德文"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.DEVON =
{
  GENERIC = "这是德文",
  ATTACKER = "德文看起来很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "德文, 鬼魂朋友！",
  GHOST = "德文可以使用一颗心。",
}

