-- 季节刻印Replica组件
-- 根据DST官方规范实现的客户端数据副本组件
-- 用于在客户端提供季节刻印数据的只读访问

local _G = GLOBAL
local Class = _G.Class

local SeasonEngravingReplica = Class(function(self, inst)
    self.inst = inst
    
    -- 客户端数据副本
    self._current_season = ""
    self._manual_season = ""
    self._is_manual = false
    
    -- 网络变量引用（由classified设置）
    self._net_current = nil
    self._net_manual = nil
    
    -- 数据变化回调
    self._onseasonchanged = {}
    self._onmanualchanged = {}
end)

-- 设置网络变量引用（由classified调用）
function SeasonEngravingReplica:SetNetworkVariables(net_current, net_manual)
    self._net_current = net_current
    self._net_manual = net_manual
    
    -- 监听网络变量变化
    if net_current then
        self.inst:ListenForEvent("season_current_dirty", function()
            self:OnCurrentSeasonDirty()
        end)
    end
    
    if net_manual then
        self.inst:ListenForEvent("season_manual_dirty", function()
            self:OnManualSeasonDirty()
        end)
    end
end

-- 处理当前季节变化
function SeasonEngravingReplica:OnCurrentSeasonDirty()
    if not self._net_current then return end
    
    local new_season = self._net_current:value()
    if new_season ~= self._current_season then
        local old_season = self._current_season
        self._current_season = new_season
        
        -- 触发回调
        for _, fn in ipairs(self._onseasonchanged) do
            fn(self.inst, new_season, old_season)
        end
        
        -- 发送事件
        self.inst:PushEvent("season_engraving_changed", {
            season = new_season,
            old_season = old_season,
            is_manual = self._is_manual
        })
    end
end

-- 处理手动季节变化
function SeasonEngravingReplica:OnManualSeasonDirty()
    if not self._net_manual then return end
    
    local new_manual = self._net_manual:value()
    local is_manual = new_manual and new_manual ~= ""
    
    if new_manual ~= self._manual_season or is_manual ~= self._is_manual then
        local old_manual = self._manual_season
        local old_is_manual = self._is_manual
        
        self._manual_season = new_manual
        self._is_manual = is_manual
        
        -- 触发回调
        for _, fn in ipairs(self._onmanualchanged) do
            fn(self.inst, new_manual, old_manual, is_manual, old_is_manual)
        end
        
        -- 发送事件
        self.inst:PushEvent("season_engraving_manual_changed", {
            manual_season = new_manual,
            old_manual = old_manual,
            is_manual = is_manual,
            was_manual = old_is_manual
        })
    end
end

-- 获取当前季节
function SeasonEngravingReplica:GetCurrentSeason()
    return self._current_season
end

-- 获取手动季节
function SeasonEngravingReplica:GetManualSeason()
    return self._manual_season
end

-- 是否为手动设置
function SeasonEngravingReplica:IsManual()
    return self._is_manual
end

-- 获取有效季节（手动优先）
function SeasonEngravingReplica:GetEffectiveSeason()
    return self._is_manual and self._manual_season or self._current_season
end

-- 注册季节变化回调
function SeasonEngravingReplica:ListenForSeasonChange(fn)
    if type(fn) == "function" then
        table.insert(self._onseasonchanged, fn)
    end
end

-- 注册手动季节变化回调
function SeasonEngravingReplica:ListenForManualChange(fn)
    if type(fn) == "function" then
        table.insert(self._onmanualchanged, fn)
    end
end

-- 移除回调
function SeasonEngravingReplica:RemoveSeasonChangeListener(fn)
    for i, callback in ipairs(self._onseasonchanged) do
        if callback == fn then
            table.remove(self._onseasonchanged, i)
            break
        end
    end
end

function SeasonEngravingReplica:RemoveManualChangeListener(fn)
    for i, callback in ipairs(self._onmanualchanged) do
        if callback == fn then
            table.remove(self._onmanualchanged, i)
            break
        end
    end
end

-- 获取季节信息摘要
function SeasonEngravingReplica:GetSeasonInfo()
    return {
        current = self._current_season,
        manual = self._manual_season,
        is_manual = self._is_manual,
        effective = self:GetEffectiveSeason()
    }
end

-- 验证季节有效性
function SeasonEngravingReplica:IsValidSeason(season)
    local valid_seasons = {spring = true, summer = true, autumn = true, winter = true}
    return valid_seasons[season] == true
end

-- 组件清理
function SeasonEngravingReplica:OnRemoveFromEntity()
    -- 清理回调
    self._onseasonchanged = {}
    self._onmanualchanged = {}
    
    -- 清理网络变量引用
    self._net_current = nil
    self._net_manual = nil
    
    print("[SeasonWorkshop] Season engraving replica cleaned up")
end

-- 调试信息
function SeasonEngravingReplica:GetDebugString()
    return string.format("Season: %s (manual: %s, is_manual: %s)", 
        self._current_season, 
        self._manual_season or "none", 
        tostring(self._is_manual))
end

return SeasonEngravingReplica
