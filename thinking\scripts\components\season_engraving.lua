local _G = GLOBAL
local Class = _G.Class
local TheWorld = _G.TheWorld
local net_string = _G.net_string
local ACTIONS = _G.ACTIONS
local TUNING = _G.TUNING
local SpawnPrefab = _G.SpawnPrefab

local SeasonEngraving = Class(function(self, inst)
    self.inst = inst
    self.current = nil -- "spring"|"summer"|"autumn"|"winter"
    self.manual_season = nil -- 手动设置的季节，在当前世界季节内有效
    self.last_world_season = nil -- 记录上次的世界季节
    self._sanityhook = nil
    self._old_hunger_rate = nil
    self._old_work_multipliers = {}

    -- 根据DST官方规范，网络同步通过forest_network上的代理组件处理
    -- 这个组件只处理本地逻辑和效果应用
    self._network_proxy = nil -- 将在初始化时设置

    -- 获取网络代理组件的引用
    if TheWorld and TheWorld.net and TheWorld.net.components and TheWorld.net.components.season_engraving_network then
        self._network_proxy = TheWorld.net.components.season_engraving_network
    end

    if not TheWorld.ismastersim then
        -- 客户端：只处理视觉效果，数据同步由网络代理处理
        -- 监听来自网络代理的季节变化事件
        inst:ListenForEvent("season_engraving_changed", function(inst, data)
            if data and data.season then
                self.current = data.season
                self.manual_season = data.is_manual and data.season or nil
                self:UpdateClientVisuals()
            end
        end)

        return
    end

    -- 服务端：定期刷新
    inst:DoTaskInTime(0, function()
        if self.inst and self.inst:IsValid() then
            self:Refresh()
        end
    end)

    inst:WatchWorldState("season", function()
        -- 世界季节变化时总是刷新，但会检查是否需要清除手动设置
        if self.inst and self.inst:IsValid() then
            self:OnWorldSeasonChanged()
        else
            print("[SeasonWorkshop] Warning: Season engraving instance became invalid during world season change")
        end
    end)
end)

function SeasonEngraving:ApplyBuffs(inst, season)
    if not inst.components then return end

    -- 清理所有旧效果
    self:ClearBuffs(inst)

    if season == "spring" then
        self:ApplySpringBuffs(inst)
    elseif season == "summer" then
        self:ApplySummerBuffs(inst)
    elseif season == "autumn" then
        self:ApplyAutumnBuffs(inst)
    elseif season == "winter" then
        self:ApplyWinterBuffs(inst)
    end
end

function SeasonEngraving:ClearBuffs(inst)
    -- 清理移动速度
    if inst.components.locomotor then
        inst.components.locomotor:SetExternalSpeedMultiplier(inst, "season_eng", 1)
    end

    -- 清理饥饿速率
    if inst.components.hunger and self._old_hunger_rate then
        inst.components.hunger:SetRate(self._old_hunger_rate)
        self._old_hunger_rate = nil
    end

    -- 清理温度隔热
    if inst.components.temperature then
        inst.components.temperature.inherentinsulation = 0
        inst.components.temperature.inherentsummerinsulation = 0
    end

    -- 清理防水
    if inst.components.waterproofer then
        inst.components.waterproofer:SetEffectiveness(0)
    end

    -- 清理工作效率
    if inst.components.workmultiplier then
        for action, _ in pairs(self._old_work_multipliers) do
            inst.components.workmultiplier:RemoveMultiplier(action, "season_eng")
        end
        self._old_work_multipliers = {}
    end

    -- 清理理智钩子
    if self._sanityhook then
        inst:RemoveEventCallback("sanitydelta", self._sanityhook)
        self._sanityhook = nil
    end

    -- 清理收获钩子
    if self._harvesthook then
        inst:RemoveEventCallback("picksomething", self._harvesthook)
        self._harvesthook = nil
    end

    -- 清理火焰伤害减免
    if inst.components.health then
        inst.components.health.fire_damage_scale = 1.0
    end
end

function SeasonEngraving:ApplySpringBuffs(inst)
    -- 防水：通过添加waterproofer组件或设置标签
    if not inst.components.waterproofer then
        inst:AddComponent("waterproofer")
    end
    inst.components.waterproofer:SetEffectiveness(TUNING.SEASON_CRAFTER_SPRING_WATERPROOF or 0.5)

    -- 采集/采矿/砍伐效率 +50%
    if not inst.components.workmultiplier then
        inst:AddComponent("workmultiplier")
    end
    local work_mult = TUNING.SEASON_CRAFTER_SPRING_WORK_MULT or 1.5
    inst.components.workmultiplier:AddMultiplier(ACTIONS.CHOP, work_mult, "season_eng")
    inst.components.workmultiplier:AddMultiplier(ACTIONS.MINE, work_mult, "season_eng")
    inst.components.workmultiplier:AddMultiplier(ACTIONS.PICK, work_mult, "season_eng")
    self._old_work_multipliers[ACTIONS.CHOP] = true
    self._old_work_multipliers[ACTIONS.MINE] = true
    self._old_work_multipliers[ACTIONS.PICK] = true

    -- 湿冷理智损失 -50%
    if inst.components.sanity then
        local protect_mult = TUNING.SEASON_CRAFTER_SPRING_SANITY_PROTECT or 0.5
        self._sanityhook = function(inst2, data)
            if data and data.amount and data.amount < 0 and inst2.components and inst2.components.moisture then
                local moisture_pct = inst2.components.moisture:GetMoisturePercent()
                if moisture_pct > 0.1 then
                    local reduce = -data.amount * protect_mult
                    inst2.components.sanity:DoDelta(reduce, true, "season_spring_moist")
                end
            end
        end
        inst:ListenForEvent("sanitydelta", self._sanityhook)
    end
end

function SeasonEngraving:ApplySummerBuffs(inst)
    -- 过热隔热 +60
    if inst.components.temperature then
        inst.components.temperature.inherentsummerinsulation = TUNING.SEASON_CRAFTER_SUMMER_INSULATION or 60
    end

    -- 移动速度 +20%
    if inst.components.locomotor then
        inst.components.locomotor:SetExternalSpeedMultiplier(inst, "season_eng", TUNING.SEASON_CRAFTER_SUMMER_SPEED_MULT or 1.2)
    end

    -- 火焰伤害 -50%
    if inst.components.health then
        inst.components.health.fire_damage_scale = TUNING.SEASON_CRAFTER_SUMMER_FIRE_RESIST or 0.5
    end
end

function SeasonEngraving:ApplyAutumnBuffs(inst)
    -- 饥饿消耗 -50%
    if inst.components.hunger then
        self._old_hunger_rate = inst.components.hunger:GetRate()
        inst.components.hunger:SetRate(self._old_hunger_rate * (TUNING.SEASON_CRAFTER_AUTUMN_HUNGER_MULT or 0.5))
    end

    -- 收获增益：50%概率+1产出
    self._harvesthook = function(inst2, data)
        if data and data.object and data.picker == inst2 then
            local obj = data.object
            -- 检查是否为浆果丛或作物类
            local is_harvestable = false
            if obj.prefab then
                local prefab = obj.prefab
                is_harvestable = prefab:find("berry") or prefab:find("farm") or prefab:find("crop") or
                               prefab == "grass" or prefab == "sapling" or prefab == "reeds" or
                               (obj.components and obj.components.pickable)
            end

            if is_harvestable and math.random() < (TUNING.SEASON_CRAFTER_AUTUMN_HARVEST_CHANCE or 0.5) then
                -- 50%概率额外掉落：尝试从对象的loot或pickable获取产物
                local extra_item = nil
                if obj.components and obj.components.pickable and obj.components.pickable.product then
                    extra_item = obj.components.pickable.product
                elseif obj.components and obj.components.lootdropper then
                    local loot = obj.components.lootdropper:GenerateLoot()
                    if loot and #loot > 0 then
                        extra_item = loot[1].prefab
                    end
                end

                if extra_item then
                    local extra = SpawnPrefab(extra_item)
                    if extra then
                        local x, y, z = obj.Transform:GetWorldPosition()
                        extra.Transform:SetPosition(x + math.random(-1, 1), 0, z + math.random(-1, 1))
                    end
                end
            end
        end
    end
    inst:ListenForEvent("picksomething", self._harvesthook)
end

function SeasonEngraving:ApplyWinterBuffs(inst)
    -- 寒冷隔热 +120
    if inst.components.temperature then
        inst.components.temperature.inherentinsulation = TUNING.SEASON_CRAFTER_WINTER_INSULATION or 120
    end

    -- 冰缓打击通过武器实现，这里不处理
end

function SeasonEngraving:GetSeason()
    return self.current
end

-- 添加客户端视觉更新函数
function SeasonEngraving:UpdateClientVisuals()
    if TheWorld.ismastersim then return end

    -- 客户端预测：立即更新角色颜色
    if self.inst and self.inst:IsValid() and self.inst.AnimState and self.current then
        local colors = {
            spring = {0.05, 0.1, 0.05, 0},    -- 轻微绿色调
            summer = {0.1, 0.05, 0, 0},       -- 轻微橙色调
            autumn = {0.08, 0.03, 0, 0},      -- 轻微褐色调
            winter = {0, 0.05, 0.1, 0}        -- 轻微蓝色调
        }

        local color = colors[self.current]
        if color then
            local intensity = self.manual_season and 1.5 or 1.0
            self.inst.AnimState:SetAddColour(
                color[1] * intensity,
                color[2] * intensity,
                color[3] * intensity,
                color[4]
            )
        end
    end
end

-- 手动设置季节刻印
function SeasonEngraving:SetSeason(season)
    if not TheWorld.ismastersim then return false end

    -- 错误处理：检查实例有效性
    if not self.inst or not self.inst:IsValid() then
        print("[SeasonWorkshop] Error: Invalid engraving instance in SetSeason")
        return false
    end

    local valid_seasons = {spring = true, summer = true, autumn = true, winter = true}
    if not valid_seasons[season] then
        print("[SeasonWorkshop] Error: Invalid season: " .. tostring(season))
        return false
    end

    -- 设置本地状态
    self.manual_season = season
    self.current = season

    -- 通过网络代理同步到客户端
    if self._network_proxy then
        local player_slot = self._network_proxy:GetPlayerSlot(self.inst.userid)
        if player_slot then
            self._network_proxy:SyncPlayerSeason(self.inst, player_slot)
        else
            print("[SeasonWorkshop] Warning: No network slot available for player")
        end
    else
        print("[SeasonWorkshop] Warning: Network proxy not available")
    end

    -- 播放切换特效
    if self.inst:IsValid() then
        self.inst:PushEvent("season_engraving_fx", {season = season})
    end

    -- 应用新的季节效果
    self:ApplyBuffs(self.inst, season)

    print(string.format("[SeasonWorkshop] Season engraving set to %s (manual)", season))
    return true
end

-- 清除手动设置，回到跟随世界季节
function SeasonEngraving:ClearManualSeason()
    if not TheWorld.ismastersim then return end

    -- 错误处理：检查实例有效性
    if not self.inst or not self.inst:IsValid() then
        print("[SeasonWorkshop] Error: Invalid engraving instance in ClearManualSeason")
        return
    end

    self.manual_season = nil

    -- 同步手动季节清除到客户端
    if self._net_manual then
        self._net_manual:set("")
    end

    self:Refresh(true)  -- 重新跟随世界季节
    print("[SeasonWorkshop] Manual season cleared, following world season")
end

-- 检查是否为手动设置的季节
function SeasonEngraving:IsManualSeason()
    return self.manual_season ~= nil
end

-- 处理世界季节变化
function SeasonEngraving:OnWorldSeasonChanged()
    if not TheWorld.ismastersim then return end

    local current_world_season = TheWorld.state.season

    -- 如果世界季节发生了变化，清除手动设置
    if self.last_world_season and self.last_world_season ~= current_world_season then
        self.manual_season = nil
        if self.inst.components and self.inst.components.talker then
            self.inst.components.talker:Say("季节变化，刻印重置...")
        end
    end

    -- 更新记录的世界季节
    self.last_world_season = current_world_season

    -- 刷新季节刻印
    self:Refresh(true)
end

function SeasonEngraving:Refresh(play_fx)
    if not TheWorld.ismastersim then return end

    -- 错误处理：检查实例有效性
    if not self.inst or not self.inst:IsValid() then
        print("[SeasonWorkshop] Error: Invalid engraving instance in Refresh")
        return
    end

    local world_season = TheWorld.state.season

    -- 验证世界季节有效性
    local valid_seasons = {spring = true, summer = true, autumn = true, winter = true}
    if not valid_seasons[world_season] then
        print("[SeasonWorkshop] Error: Invalid world season: " .. tostring(world_season))
        world_season = "autumn"
    end

    -- 初始化记录的世界季节
    if not self.last_world_season then
        self.last_world_season = world_season
    end

    -- 优先使用手动设置的季节，否则使用世界季节
    local season = self.manual_season or world_season
    self.current = season

    -- 通过网络代理同步到客户端
    if self._network_proxy and self.inst.userid then
        local player_slot = self._network_proxy:GetPlayerSlot(self.inst.userid)
        if player_slot then
            self._network_proxy:SyncPlayerSeason(self.inst, player_slot)
        end
    end

    if play_fx and self.inst:IsValid() and self.inst.components and self.inst.components.locomotor then
        -- 简易FX占位：可在角色prefab中生成更合适的FX
        self.inst:PushEvent("season_engraving_fx", {season = season})
    end

    -- 安全应用季节效果
    local success = pcall(function()
        self:ApplyBuffs(self.inst, season)
    end)

    if not success then
        print("[SeasonWorkshop] Error: Failed to apply season buffs for " .. season)
    else
        print(string.format("[SeasonWorkshop] Season refreshed to %s (manual: %s)",
              season, self.manual_season and "true" or "false"))
    end
end

-- 数据持久化：保存季节刻印状态
function SeasonEngraving:OnSave()
    -- 只在服务端保存数据
    if not TheWorld.ismastersim then return end

    return {
        current = self.current,
        manual_season = self.manual_season,
        last_world_season = self.last_world_season,
        -- 保存旧的饥饿速率以便恢复
        old_hunger_rate = self._old_hunger_rate,
        -- 保存工作效率修正记录
        old_work_multipliers = self._old_work_multipliers
    }
end

-- 数据持久化：加载季节刻印状态
function SeasonEngraving:OnLoad(data)
    if not TheWorld.ismastersim then return end

    if data then
        self.current = data.current
        self.manual_season = data.manual_season
        self.last_world_season = data.last_world_season
        self._old_hunger_rate = data.old_hunger_rate
        self._old_work_multipliers = data.old_work_multipliers or {}

        -- 通过网络代理同步到客户端（延迟执行确保代理已初始化）
        self.inst:DoTaskInTime(0.1, function()
            if self.inst and self.inst:IsValid() then
                -- 通过网络代理同步
                if self._network_proxy and self.inst.userid then
                    local player_slot = self._network_proxy:GetPlayerSlot(self.inst.userid)
                    if player_slot then
                        self._network_proxy:SyncPlayerSeason(self.inst, player_slot)
                    end
                end

                -- 重新应用季节效果
                if self.current then
                    self:ApplyBuffs(self.inst, self.current)
                end

                print(string.format("[SeasonWorkshop] Season engraving loaded: %s (manual: %s)",
                      self.current or "none", self.manual_season or "none"))
            end
        end)
    end
end

-- 组件自检功能
function SeasonEngraving:PerformSelfCheck()
    local issues = {}

    -- 检查实例有效性
    if not self.inst or not self.inst:IsValid() then
        table.insert(issues, "Instance invalid")
        return false, issues
    end

    -- 检查网络变量状态
    if TheWorld.ismastersim then
        if not self._net then
            table.insert(issues, "Network variable _net not initialized")
        end
        if not self._net_manual then
            table.insert(issues, "Network variable _net_manual not initialized")
        end
    end

    -- 检查季节状态一致性
    if self.current then
        local valid_seasons = {spring = true, summer = true, autumn = true, winter = true}
        if not valid_seasons[self.current] then
            table.insert(issues, "Invalid current season: " .. tostring(self.current))
        end
    end

    if self.manual_season then
        local valid_seasons = {spring = true, summer = true, autumn = true, winter = true}
        if not valid_seasons[self.manual_season] then
            table.insert(issues, "Invalid manual season: " .. tostring(self.manual_season))
        end
    end

    -- 检查组件依赖
    if not self.inst.components then
        table.insert(issues, "Instance has no components")
    end

    return #issues == 0, issues
end

-- 尝试自动修复
function SeasonEngraving:AttemptSelfRepair()
    local healthy, issues = self:PerformSelfCheck()
    if healthy then
        return true
    end

    print("[SeasonWorkshop] Season engraving attempting self-repair...")

    -- 网络变量无法在运行时修复，只能记录错误
    if TheWorld.ismastersim then
        if not self._net then
            print("[SeasonWorkshop] Error: Cannot repair network variable at runtime - component needs restart")
        end
        if not self._net_manual then
            print("[SeasonWorkshop] Error: Cannot repair manual network variable at runtime - component needs restart")
        end
    end

    -- 修复无效的季节状态
    if self.current then
        local valid_seasons = {spring = true, summer = true, autumn = true, winter = true}
        if not valid_seasons[self.current] then
            self.current = TheWorld.state.season or "autumn"
            print("[SeasonWorkshop] Fixed invalid current season")
        end
    end

    if self.manual_season then
        local valid_seasons = {spring = true, summer = true, autumn = true, winter = true}
        if not valid_seasons[self.manual_season] then
            self.manual_season = nil
            print("[SeasonWorkshop] Cleared invalid manual season")
        end
    end

    -- 重新检查
    local repaired, remaining_issues = self:PerformSelfCheck()
    if repaired then
        print("[SeasonWorkshop] Season engraving self-repair successful")
    else
        print("[SeasonWorkshop] Season engraving self-repair failed, remaining issues:")
        for _, issue in ipairs(remaining_issues) do
            print("  - " .. issue)
        end
    end

    return repaired
end

-- 组件清理：确保资源正确释放
function SeasonEngraving:OnRemoveFromEntity()
    -- 清理所有效果
    if self.inst and self.inst:IsValid() then
        self:ClearBuffs(self.inst)
    end

    -- 清理网络变量引用
    self._net = nil
    self._net_manual = nil

    -- 清理钩子引用
    self._sanityhook = nil
    self._harvesthook = nil

    print("[SeasonWorkshop] Season engraving component cleaned up")
end

return SeasonEngraving
