_G.ChinesePlus.SetConfigTranslation(mod_to_c,
  "奶油肉汁！",
  {

  },
  {
    {
      name="Sentry_Difficulty",
      label="步哨枪配方",
      hover = "制作步哨枪配方的水平。",
      options = {
        {data="easy",description="容易"},
        {data="default",description="默认"},
        {data="hard",description="困难"},
        {data="harder",description="艰难"},
      },
      default="default"
    },

    {
      name="Dispenser_Difficulty",
      label="补给站配方",
      hover = "制作补给站配方的水平。",
      options = {
        {data="easy",description="容易"},
        {data="default",description="默认"},
        {data="hard",description="困难"},
        {data="harder",description="艰难"},
      },
      default="default"
    },

    {
      name="Teleporter_Difficulty",
      label="传送器配方",
      hover = "制作传送器配方的水平。",
      options = {
        {data="easy",description="容易"},
        {data="default",description="默认"},
        {data="hard",description="困难"},
        {data="harder",description="艰难"},
      },
      default="default"
    },

    {
      name="Sentry_Range",
      label="步哨枪范围",
      hover = "步哨枪的范围。",
      options = {
        {data=2,description="2"},
        {data=3,description="3"},
        {data=4,description="4"},
        {data=5,description="5"},
        {data=6,description="6"},
        {data=7,description="7"},
        {data=8,description="8"},
        {data=9,description="9"},
        {data=10,description="10"},
        {data=11,description="11"},
        {data=12,description="12"},
        {data=13,description="13(默认)"},
        {data=14,description="14"},
        {data=15,description="15"},
        {data=16,description="16"},
        {data=17,description="17"},
        {data=18,description="18"},
        {data=19,description="19"},
        {data=20,description="20"},
        {data=21,description="21"},
        {data=22,description="22"},
        {data=23,description="23"},
        {data=24,description="25"},
        {data=25,description="25"},
        {data=26,description="26"},
        {data=27,description="27"},
        {data=28,description="28"},
        {data=29,description="29"},
        {data=30,description="30"},
        {data=50,description="50"},
      },
      default=13,
    },

    {
      name="Sentry_Damage",
      label="步哨枪基础伤害",
      hover = "步哨枪的实际伤害是等级倍数的基础伤害。",
      options = {
        {data=1,description="1"},
        {data=1.5,description="1.5"},
        {data=2,description="2"},
        {data=3,description="3"},
        {data=3.5,description="3.5"},
        {data=4,description="4"},
        {data=5,description="5"},
        {data=6,description="6"},
        {data=7,description="7"},
        {data=7.5,description="7.5"},
        {data=8,description="8"},
        {data=9,description="9"},
        {data=9.5,description="9.5(默认)"},
        {data=9.7,description="9.7"},
        {data=10,description="10"},
        {data=10.5,description="10.5"},
        {data=15,description="15"},
        {data=15.5,description="15.5"},
        {data=16,description="16"},
        {data=17,description="17"},
        {data=20,description="20"},
        {data=25,description="25"},
        {data=30,description="30"},
        {data=35,description="35"},
        {data=40,description="40"},
        {data=40.5,description="40.5"},
        {data=45,description="45"},
        {data=50,description="50"},
        {data=50.5,description="50.5"},
        {data=60,description="60"},
        {data=65,description="65"},
        {data=70,description="70"},
        {data=75,description="75"},
        {data=80,description="80"},
        {data=85,description="85"},
        {data=90,description="90"},
        {data=95,description="95"},
        {data=100,description="100"},
        {data=105,description="105"},
      },
      default=9.5,
    },

    {
      name="Sentry_ROF",
      label="步哨枪开火速度",
      hover = "步哨枪将在在几秒钟内开火。",
      options = {
        {data=0,description="即时(0)"},
        {data=1,description="默认(1)"},
        {data=2,description="中等(2)"},
        {data=3,description="慢(3)"},
        {data=4,description="较慢(4)"},
        {data=5,description="最慢(5)"},
      },
      default=1,
    },

    {
      name="Sentry_Health",
      label="步哨枪生命",
      hover = "步哨枪的实际伤害和自动回复是等级倍数的基础生命。",
      options = {
        {data=15,description="15"},
        {data=20,description="20"},
        {data=50,description="50"},
        {data=100,description="100"},
        {data=110,description="110"},
        {data=120,description="120"},
        {data=130,description="130"},
        {data=140,description="140"},
        {data=150,description="150(TF2)"},
        {data=170,description="170"},
        {data=180,description="180"},
        {data=200,description="200(默认)"},
        {data=230,description="230"},
        {data=250,description="250"},
        {data=260,description="260"},
        {data=285,description="285"},
        {data=300,description="300"},
        {data=350,description="350"},
        {data=400,description="400"},
        {data=500,description="500"},
      },
      default=200,
    },

    {
      name="disprange",
      label="补给站范围",
      hover = "补给站可以治疗玩家的最大距离。",
      options = {
        {data=1,description="1"},
        {data=1.2,description="1.2"},
        {data=1.5,description="1.5"},
        {data=1.8,description="1.8"},
        {data=2,description="2(默认)"},
        {data=2.2,description="2.2"},
        {data=2.5,description="2.5"},
        {data=2.7,description="2.7"},
        {data=2.9,description="2.9"},
        {data=3,description="3"},
        {data=3.2,description="3.2"},
        {data=3.5,description="3.5"},
        {data=3.7,description="3.7"},
        {data=4,description="4"},
        {data=4.2,description="4.2"},
        {data=4.5,description="4.5"},
        {data=4.7,description="4.7"},
        {data=5,description="5"},
      },
      default=2,
    },

    {
      name = "tf2wrenchdmg",
      label = "扳手基础伤害",
      options =
      {
        {description = "8", data = 8},
        {description = "10", data = 10},
        {description = "13.6", data = 13.6},
        {description = "15", data = 15},
        {description = "17(默认)", data = 17},
        {description = "20", data = 20},
        {description = "25", data = 25},
        {description = "27.2", data = 27.2},
        {description = "29", data = 29},
        {description = "30", data = 30},
        {description = "34", data = 34},
      },
      default = 17,
    },

    {
      name = "tf2wrenchuses",
      label = "扳手使用次数",
      options =
      {
        {description = "50", data = 50},
        {description = "75", data = 75},
        {description = "100", data = 100},
        {description = "150(默认)", data = 150},
        {description = "175", data = 175},
        {description = "200", data = 200},
        {description = "250", data = 250},
        {description = "260", data = 260},
      },
      default = 150,
    },

    {
      name="tf2Wrench_Difficulty",
      label="扳手配方",
      hover = "制作扳手配方的水平。",
      options = {
        {data="easier",description="容易"},
        {data="default",description="默认"},
        {data="harder",description="艰难"},
      },
      default="default"
    },

    {
      name="Scrap_Difficulty",
      label="残留金属配方",
      hover = "制作残留金属配方的水平。",
      options = {
        {data="easier",description="简单"},
        {data="easy",description="容易"},
        {data="default",description="默认"},
        {data="hard",description="困难"},
        {data="harder",description="艰难"},
      },
      default="default"
    },

    {
      name = "hardhatabsorb",
      label = "安全帽伤害吸收",
      options =
      {
        {description = "5%", data = .05},
        {description = "10%", data = .1},
        {description = "15%", data = .15},
        {description = "20%", data = .2},
        {description = "30%", data = .3},
        {description = "40%", data = .4},
        {description = "50%", data = .5},
        {description = "60%", data = .6},
        {description = "65%", data = .65},
        {description = "70%(默认)", data = .7},
        {description = "75%", data = .75},
        {description = "80%", data = .8},
        {description = "85%", data = .85},
        {description = "90%", data = .9},
      },
      default = .7,
    },
    {
      name = "ehardhatdura",
      label = "安全帽耐久",
      options =
      {
        {description = "100", data = 100},
        {description = "110", data = 110},
        {description = "120", data = 120},
        {description = "125", data = 125},
        {description = "130", data = 130},
        {description = "140", data = 140},
        {description = "150", data = 150},
        {description = "160", data = 160},
        {description = "170", data = 170},
        {description = "180", data = 180},
        {description = "190", data = 190},
        {description = "200", data = 200},
        {description = "210", data = 210},
        {description = "220", data = 220},
        {description = "230", data = 230},
        {description = "240", data = 240},
        {description = "250", data = 250},
        {description = "255", data = 255},
        {description = "260", data = 260},
        {description = "265", data = 265},
        {description = "270", data = 270},
        {description = "275", data = 275},
        {description = "280", data = 280},
        {description = "285", data = 285},
        {description = "290", data = 290},
        {description = "295(默认)", data = 295},
        {description = "300", data = 300},
        {description = "305", data = 305},
        {description = "310", data = 310},
        {description = "315", data = 315},
        {description = "320", data = 320},
        {description = "325", data = 325},
        {description = "330", data = 330},
        {description = "340", data = 340},
        {description = "350", data = 350},
        {description = "360", data = 360},
        {description = "370", data = 370},
        {description = "380", data = 380},
        {description = "390", data = 390},
        {description = "395", data = 395},
        {description = "400", data = 400},
        {description = "410", data = 410},
        {description = "420", data = 420},
        {description = "425", data = 425},
        {description = "430", data = 430},
        {description = "440", data = 440},
        {description = "450", data = 450},
        {description = "460", data = 460},
        {description = "470", data = 470},
        {description = "480", data = 480},
        {description = "490", data = 490},
        {description = "500", data = 500},
        {description = "525", data = 525},
        {description = "530", data = 530},
      },
      default = 295,
    },

    {
      name="EHat_Difficulty",
      label="安全帽配方",
      hover = "制作安全帽配方的水平。",
      options = {
        {data="easy",description="容易"},
        {data="default",description="默认"},
        {data="hard",description="困难"},
        {data="harder",description="艰难"},
      },
      default="default"
    },

  }
)
