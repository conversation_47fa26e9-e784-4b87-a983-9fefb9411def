--W<PERSON>i
STRINGS.CHARACTER_TITLES.walani = "輕鬆的衝浪者"
STRINGS.CHARACTER_NAMES.walani = "瓦拉尼"
STRINGS.CHARACTER_DESCRIPTIONS.walani = "*喜歡衝浪。\n*潮溼後乾的快\n*一個很潮的女孩"
STRINGS.CHARACTER_QUOTES.walani = "\"有人可以給我一些食物嗎？\""
STRINGS.CHARACTERS.WALANI = nil
STRINGS.NAMES.WALANI = "瓦拉尼"

--Character interaction strings
STRINGS.CHARACTERS.GENERIC.DESCRIBE.WALANI =
{
  GENERIC = "你好, %s!",
  ATTACKER = "%s 看起來壞壞的...",
  MURDERER = "凶手!",
  REVIVER = "%s, 鬼魂之友。",
  GHOST = "%s可以使用救贖之心。",
}
STRINGS.CHARACTERS.WILLOW.DESCRIBE.WALANI = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.WALANI = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.WALANI = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.WALANI = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.WALANI = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.WALANI = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.WALANI = nil

STRINGS.CHARACTERS.GENERIC.DESCRIBE.SURFBOARD = "衝浪板"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.SURFBOARD = "一塊蠢蠢的木板。"
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.SURFBOARD = "小小的木板支撐得了我嗎？"
STRINGS.CHARACTERS.WENDY.DESCRIBE.SURFBOARD = "使用衝浪板給人島嶼的氛圍。"
STRINGS.CHARACTERS.WX78.DESCRIBE.SURFBOARD = "幾乎不能海上旅行的船隻。"
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.SURFBOARD = "一塊沒有使用過的充滿活力，容易操作，舒適衝浪板。"
STRINGS.CHARACTERS.WOODIE.DESCRIBE.SURFBOARD = "可憐的東西不能在這裡衝浪。"
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.SURFBOARD = "對我來說看起來太危險了。"

--Warly
STRINGS.CHARACTER_TITLES.warly = "廚師"
STRINGS.CHARACTER_NAMES.warly = "沃利"
STRINGS.CHARACTER_DESCRIPTIONS.warly = "*具有特殊的品味\n*擁有自己的鍋\n*擁有自己的袋子"
STRINGS.CHARACTER_QUOTES.warly = "\"祝你有個好胃口！\""
STRINGS.CHARACTERS.WARLY = nil
STRINGS.NAMES.WARLY = "沃利"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.WARLY =
{
  GENERIC = "今天好, %s!",
  ATTACKER = "%s看起來打算要把我當食材！",
  MURDERER = "%s已經煮過頭了！",
  REVIVER = "%s烹飪出了友誼。",
  GHOST = "看起來你已經熟了, %s.",
}
STRINGS.CHARACTERS.WILLOW.DESCRIBE.WARLY = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.WARLY = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.WARLY = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.WARLY = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.WARLY = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.WARLY = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.WARLY = nil

STRINGS.FRESHFRUITCREPES = "水果薄餅"
STRINGS.NAMES.FRESHFRUITCREPES = "水果薄餅"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.FRESHFRUITCREPES = "含糖的水果！營養均衡早餐的一部分。"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.FRESHFRUITCREPES = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.FRESHFRUITCREPES = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.FRESHFRUITCREPES = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.FRESHFRUITCREPES = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.FRESHFRUITCREPES = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.FRESHFRUITCREPES = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.FRESHFRUITCREPES = nil

STRINGS.MONSTERTARTARE = "怪物韃靼"
STRINGS.NAMES.MONSTERTARTARE = "怪物韃靼"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MONSTERTARTARE = "這兒一定有什麼別的吃的。"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.MONSTERTARTARE = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.MONSTERTARTARE = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.MONSTERTARTARE = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.MONSTERTARTARE = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.MONSTERTARTARE = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.MONSTERTARTARE = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.MONSTERTARTARE = nil

STRINGS.CHARACTERS.GENERIC.DESCRIBE.PORTABLECOOKPOTINV = "現在我們來做飯！"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.PORTABLECOOKPOTINV = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.PORTABLECOOKPOTINV = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.PORTABLECOOKPOTINV = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.PORTABLECOOKPOTINV = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.PORTABLECOOKPOTINV = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.PORTABLECOOKPOTINV = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.PORTABLECOOKPOTINV = nil

STRINGS.CHARACTERS.GENERIC.DESCRIBE.CHEFPACK = "滿滿都是廚師的技巧。"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.CHEFPACK = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.CHEFPACK = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.CHEFPACK = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.CHEFPACK = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.CHEFPACK = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.CHEFPACK = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.CHEFPACK = nil

--Wilbur
STRINGS.CHARACTER_TITLES.wilbur = "猴王"
STRINGS.CHARACTER_NAMES.wilbur = "威爾伯"
STRINGS.CHARACTER_DESCRIPTIONS.wilbur = "*不會說話\n*雙教跑慢，四肢並用跑快\n*猴子"
STRINGS.CHARACTER_QUOTES.wilbur = "\"Ooo ooa oah ah!\""
STRINGS.CHARACTERS.WILBUR = nil
STRINGS.NAMES.WILBUR = "威爾伯"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.WILBUR =
{
  GENERIC = "今天好, %s!",
  ATTACKER = "%s的香蕉沒了!",
  MURDERER = "%s不再發狂了！",
  REVIVER = "%s, 鬼魂之友。",
  GHOST = "%s可以使用救贖之心和吃些香蕉。",
}
STRINGS.CHARACTERS.WILLOW.DESCRIBE.WILBUR = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.WILBUR = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.WILBUR = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.WILBUR = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.WILBUR = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.WILBUR = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.WILBUR = nil

--Woodlegs:
STRINGS.CHARACTER_TITLES.woodlegs = "海盜船長"
STRINGS.CHARACTER_NAMES.woodlegs = "木腿"
STRINGS.CHARACTER_DESCRIPTIONS.woodlegs = "*有一個幸運帽\n*有一把幸運彎刀\n*海盜"
STRINGS.CHARACTER_QUOTES.woodlegs = "\"呀~~~呀~~吖!\""
STRINGS.CHARACTERS.WOODLEGS = nil
STRINGS.NAMES.WOODLEGS = "木腿"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.WOODLEGS =
{
  GENERIC = "你好, %s!",
  ATTACKER = "%s把他們海盜的事情看得太重。",
  MURDERER = "%s殘酷的飯桶不見了！",
  REVIVER = "%s, 同船的友誼！",
  GHOST = "%s可以用救贖之心，但他們更喜歡黃金。",
}
STRINGS.CHARACTERS.WILLOW.DESCRIBE.WOODLEGS = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.WOODLEGS = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.WOODLEGS = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.WOODLEGS = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.WOODLEGS = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.WOODLEGS = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.WOODLEGS = nil

STRINGS.CHARACTERS.GENERIC.DESCRIBE.LUCKYHAT = "只適合海盜船長。"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.LUCKYHAT = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.LUCKYHAT = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.LUCKYHAT = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.LUCKYHAT = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.LUCKYHAT = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.LUCKYHAT = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.LUCKYHAT = nil

STRINGS.CHARACTERS.GENERIC.DESCRIBE.LUCKYCUTLASS = "有人打算跳海！"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.LUCKYCUTLASS = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.LUCKYCUTLASS = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.LUCKYCUTLASS = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.LUCKYCUTLASS = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.LUCKYCUTLASS = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.LUCKYCUTLASS = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.LUCKYCUTLASS = nil

STRINGS.CHARACTERS.GENERIC.DESCRIBE.PORTABLECOOKPOTMAP =
{
  --BURNT = "His pot got cooked.",
  COOKING_LONG = "這需要一段時間。",
  COOKING_SHORT = "差不多就完成了！",
  DONE = "嗯嗯嗯！！準備吃飯！",
  EMPTY = "他從來沒有不帶它離開家過。",
}
STRINGS.CHARACTERS.WILLOW.DESCRIBE.PORTABLECOOKPOTMAP = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.PORTABLECOOKPOTMAP = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.PORTABLECOOKPOTMAP = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.PORTABLECOOKPOTMAP = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.PORTABLECOOKPOTMAP = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.PORTABLECOOKPOTMAP = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.PORTABLECOOKPOTMAP = nil

local function ForceTranslate()

  STRINGS.CHEFPACK = "廚師袋"
  STRINGS.NAMES.CHEFPACK = "廚師袋"
  _G.ChinesePlus.RenameRecipe("CHEFPACK", "讓你的你的食物新鮮點。")

  STRINGS.LUCKYCUTLASS = "幸運彎刀"
  STRINGS.NAMES.LUCKYCUTLASS = "幸運彎刀"
  _G.ChinesePlus.RenameRecipe("LUCKYCUTLASS", "切割和姑娘的主人。彎刀。")

  STRINGS.LUCKYHAT = "幸運帽"
  STRINGS.NAMES.LUCKYHAT = "幸運帽"
  _G.ChinesePlus.RenameRecipe("LUCKYHAT", "這是一個海盜的生活。")

  STRINGS.PORTABLECOOKPOTINV = "行動式烹飪鍋"
  STRINGS.NAMES.PORTABLECOOKPOTINV = "行動式烹飪鍋"
  _G.ChinesePlus.RenameRecipe("PORTABLECOOKPOTINV", "你烹飪的朋友。")

  STRINGS.PORTABLECOOKPOTMAP = "行動式烹飪鍋"
  STRINGS.NAMES.PORTABLECOOKPOTMAP = "行動式烹飪鍋"
  _G.ChinesePlus.RenameRecipe("PORTABLECOOKPOTMAP", "你烹飪的朋友。")

  STRINGS.SURFBOARD = "衝浪板"
  STRINGS.NAMES.SURFBOARD = "衝浪板"
  _G.ChinesePlus.RenameRecipe("SURFBOARD", "好啊!加油! 帥哥！")
end

table.insert(ForceTranslateList, ForceTranslate)