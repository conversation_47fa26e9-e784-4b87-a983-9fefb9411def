_G.STRINGS.TABS["<PERSON>'s Tab"] = "梅"

-- The character select screen lines
STRINGS.CHARACTER_TITLES.mea = "一縷記憶碎片"
STRINGS.CHARACTER_NAMES.mea = "梅"
STRINGS.CHARACTER_DESCRIPTIONS.mea = "*她把製造一個可以睡覺和吃的生物。\n*牛奶和羊毛可以在滿月收穫。\n*到處都可以睡覺。"
STRINGS.CHARACTER_QUOTES.mea = "\"請教我你的記憶...\""

-- Custom speech strings
STRINGS.CHARACTERS.MEA = nil --require "speech_mea"

-- The character's name as appears in-game
STRINGS.NAMES.MEA = "梅"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MEA =
{
	GENERIC = "這是梅!",
	ATTACKER = "梅看起來很狡猾...",
	MURDERER = "凶手!",
	REVIVER = "梅, 鬼魂之友.",
	GHOST = "梅可以使用救贖之心.",
}

GLOBAL.STRINGS.NAMES.DREAMCANDY = "夢醒掉落"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.DREAMCANDY = "這似乎是短暫和清晰的。"
_G.ChinesePlus.RenameRecipe("DREAMCANDY", "夢是現實的力量！")

GLOBAL.STRINGS.NAMES.COWBELL = "牛鈴"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.COWBELL = "那是一個有光澤的鐘。"
_G.ChinesePlus.RenameRecipe("COWBELL", "讓我們玩“叮叮”。")

GLOBAL.STRINGS.NAMES.SHEEPMILK = "羊奶"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.SHEEPMILK = "這是泥濘但甜的氣味。"
_G.ChinesePlus.RenameRecipe("SHEEPMILK", "一種膚淺的渾濁的香味。")

GLOBAL.STRINGS.NAMES.DREAMWOOL = "夢之羊毛"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.DREAMWOOL = "這是軟的，但不普通。"
_G.ChinesePlus.RenameRecipe("DREAMWOOL", "看起來像綿羊的頭髮。")

GLOBAL.STRINGS.NAMES.DREAMPILLOW = "夢之枕頭"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.DREAMPILLOW = "我不知道它是由什麼製成的。"
_G.ChinesePlus.RenameRecipe("DREAMPILLOW", "它保證會睡個好覺！")

GLOBAL.STRINGS.NAMES.MEA_MANT = "梅的鑽石"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.MEA_MANT = "我不知道它是由什麼製成的。"
_G.ChinesePlus.RenameRecipe("MEA_MANT", "羊可以和這個一起睡。")

GLOBAL.STRINGS.NAMES.DREAMLANTERN = "靈魂碎片"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.DREAMLANTERN = "我不知道它是由什麼製成的。"
_G.ChinesePlus.RenameRecipe("DREAMLANTERN", "飢餓和燃料！")
