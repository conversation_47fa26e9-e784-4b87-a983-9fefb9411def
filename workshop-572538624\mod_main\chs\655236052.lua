_G.ChinesePlus.RenameRecipe("CHESTER_EYEBONE", "召唤切斯特。")
_G.ChinesePlus.RenameRecipe("HUTCH_FISHBOWL", "召唤哈奇。")
_G.ChinesePlus.RenameRecipe("BUTTER", "黄油")
_G.ChinesePlus.RenameRecipe("BEARDHAIR", "你留胡子吗？")
_G.ChinesePlus.RenameRecipe("LUREPLANTBULB", "废物处置的好地方。")
_G.ChinesePlus.RenameRecipe("REDGEM", "彩色宝石。")
_G.ChinesePlus.RenameRecipe("BLUEGEM", "彩色宝石。")
_G.ChinesePlus.RenameRecipe("GREENGEM", "彩色宝石。")
_G.ChinesePlus.RenameRecipe("ORANGEGEM", "彩色宝石。")
_G.ChinesePlus.RenameRecipe("YELLOWGEM", "彩色宝石。")
_G.ChinesePlus.RenameRecipe("WALRUSHAT", "漂亮的帽子。")
_G.ChinesePlus.RenameRecipe("KRAMPUS_SACK", "一个大背包。")
_G.ChinesePlus.RenameRecipe("GEARS", "很实用的东西。")
_G.ChinesePlus.RenameRecipe("STEELWOOL", "恶心的动物，很少看到。")
_G.ChinesePlus.RenameRecipe("MOONROCKNUGGET", "来自天空的石头。")
_G.ChinesePlus.RenameRecipe("THULECITE_PIECES", "使用它来建造更多科技。")
_G.ChinesePlus.RenameRecipe("MINOTAURHORN", "貌似没什么用。")
_G.ChinesePlus.RenameRecipe("ARMORSNURTLESHELL", "感觉很奇怪。")
_G.ChinesePlus.RenameRecipe("SLURTLEHAT", "酷。")
_G.ChinesePlus.RenameRecipe("WORMLIGHT", "光。")
_G.ChinesePlus.RenameRecipe("GOOSE_FEATHER", "柔软的羽毛。")
_G.ChinesePlus.RenameRecipe("FURTUFT", "毛簇 * 30")
_G.ChinesePlus.RenameRecipe("DRAGON_SCALES", "看上去很难。")
_G.ChinesePlus.RenameRecipe("DEERCLOPS_EYEBALL", "一个生物的一部分")
_G.ChinesePlus.RenameRecipe("BLUEPRINT", "随机。")
_G.ChinesePlus.RenameRecipe("LIVINGLOG", "活木。")
_G.ChinesePlus.RenameRecipe("MANDRAKESOUP", "美味啊。")
_G.ChinesePlus.RenameRecipe("PETALS_EVIL", "恶作剧。")
_G.ChinesePlus.RenameRecipe("MARBLE", "坚硬的石头。")
_G.ChinesePlus.RenameRecipe("MANDRAKE_PLANTED", "奇怪的草，尝起来很美味。")
_G.ChinesePlus.RenameRecipe("CATCOONDEN", "一只猫的家。")
_G.ChinesePlus.RenameRecipe("WALRUS_CAMP", "有一只海象和他的儿子。")
_G.ChinesePlus.RenameRecipe("ANCIENT_ALTAR_BROKEN", "解锁更多的技术。")
_G.ChinesePlus.RenameRecipe("ANCIENT_ALTAR", "解锁更多的技术。")
_G.ChinesePlus.RenameRecipe("POND", "只是一个池塘")
_G.ChinesePlus.RenameRecipe("POND_MOS", "只是一个池塘")
_G.ChinesePlus.RenameRecipe("LAVA_POND", "热的池塘")
_G.ChinesePlus.RenameRecipe("POND_CAVE", "只是一个池塘")
_G.ChinesePlus.RenameRecipe("MERMHOUSE", "鱼的房子。")
_G.ChinesePlus.RenameRecipe("RESURRECTIONSTONE", "复活.")
_G.ChinesePlus.RenameRecipe("BEEHIVE", "蜜蜂群。")
_G.ChinesePlus.RenameRecipe("WASPHIVE", "一群杀人蜂。")
_G.ChinesePlus.RenameRecipe("SPIDERHOLE", "地下蜘蛛巢。")
_G.ChinesePlus.RenameRecipe("SLURTLEHOLE", "蜗牛窝。")
_G.ChinesePlus.RenameRecipe("BATCAVE", "讨厌的家伙。")
_G.ChinesePlus.RenameRecipe("MONKEYBARREL", "猴子呆在这里。")
_G.ChinesePlus.RenameRecipe("TALLBIRDNEST", "长腿鸟的巢。")
_G.ChinesePlus.RenameRecipe("HOUNDMOUND", "猎犬的出生地。")
_G.ChinesePlus.RenameRecipe("CAVE_BANANA_TREE", "香蕉。")
_G.ChinesePlus.RenameRecipe("STATUEGLOMMER", "奇怪的雕像。")
_G.ChinesePlus.RenameRecipe("BABYBEEFALO", "小牛。")
_G.ChinesePlus.RenameRecipe("CAVE_ENTRANCE_OPEN", "洞穴的入口")
_G.ChinesePlus.RenameRecipe("CAVE_EXIT", "退出洞穴。")
_G.ChinesePlus.RenameRecipe("WORMLIGHT_PLANT", "奇怪的植物。")
