_G.ChinesePlus.SetConfigTranslation(mod_to_c,
	"扮演寵物 / 怪物！",
	{

	},
	{
		{
			name = "Hound",
			label = "獵犬",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},

		{
			name = "Hell Hound",
			label = "火獵犬",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},

		{
			name = "Ice Hound",
			label = "冰獵犬",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},

		{
			name = "Catcoon",
			label = "浣貓",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},

		{
			name = "Nightmare Hound",
			label = "噩夢獵犬",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},

		{
			name = "<PERSON>",
			label = "蜘蛛",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},

		{
			name = "Spider Warrior",
			label = "蜘蛛勇士",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},

		{
			name = "Depth Dweller",
			label = "白蜘蛛",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},

		{
			name = "Spider Queen",
			label = "蜘蛛女王",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},

		{
			name = "Dweller Queen",
			label = "白蜘蛛女王",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},



		{
			name = "Lavae",
			label = "熔岩蟲",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},

		{
			name = "Dragonfly",
			label = "龍蠅",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},

		{
			name = "Bearger",
			label = "比格爾",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},

		{
			name = "Mossling",
			label = "莫斯林",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},

		{
			name = "Moose/Goose",
			label = "鹿角鵝",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},

		{
			name = "Deerclops",
			label = "鉅鹿",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},

		{
			name = "Ghost",
			label = "鬼魂",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},

		{
			name = "Pig",
			label = "豬人",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},

		{
			name = "Merm",
			label = "魚人",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},

		{
			name = "McTusk",
			label = "海象",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},

		{
			name = "Smallbird",
			label = "小高腳鳥",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},

		{
			name = "Tallbird",
			label = "高腳鳥",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},

		{
			name = "Tree Guard",
			label = "樹精守衛",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},

		{
			name = "Beefalo",
			label = "皮弗婁牛",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},

		{
			name = "Warg",
			label = "座狼",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},

		{
			name = "Knight",
			label = "發條騎士",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},

		{
			name = "Bishop",
			label = "發條主教",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},

		{
			name = "Rook",
			label = "機械戰車",
			options =
			{
				{description = "開", data = "Enable"},
				{description = "關", data = "Disable"},
			},

		},


		--[[{
			name = "Mobghosts",
			label = "Enable Mob Ghosts?",
			options =
			{
				{description = "是", data = "Yes"},
				{description = "否", data = "No"},
			},

		},]]

		{
			name = "Mobchange",
			label = "允許更換角色？",
			options =
			{
				{description = "是", data = "Enable"},
				--{description = "只有怪物", data = "Enable1"},
				{description = "否", data = "Disable"},
			},

		},

		{
			name = "Hunger",
			label = "會飢餓嗎?",
			options =
			{
				{description = "是", data = "Enable"},
				{description = "否", data = "Disable"},
			},

		},

		{
			name = "MobSanity",
			label = "應該有精神值嗎?",
			options =
			{
				{description = "是", data = "Enable", hover = "怪物將失去精神和疲倦。"},
				{description = "只有寵物", data = "Disable1", hover = "豬人類的怪物失去精神和疲倦。"},
				{description = "否", data = "Disable", hover = "所有怪物失去精神"},
			},

		},

		{
			name = "MobCraft",
			label = "怪物可以做任何事嗎?",
			options =
			{
				{description = "是", data = "Enable", hover = "像人類一樣做任何事情"},
				{description = "否", data = "Disable", hover = "怪物做怪物的動作"},
			},

		},
		--[[
		{
			name = "MobHouseMethod",
			label = "房屋的可達性",
			options =
			{
				{description = "On Spawn", data = "Enable1", hover = "Mobs will spawn with their homes"},
				{description = "Crafting", data = "Enable2", hover = "Homes will need to be crafted"},
				{description = "No Homes", data = "Disable", hover = "Homes cannot be obtained through normal means"},


			},


		},
		]]
	}
)
