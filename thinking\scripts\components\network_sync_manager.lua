-- 网络同步管理器
-- 统一管理所有网络变量的同步和错误处理

local _G = GLOBAL
local Class = _G.Class
local TheWorld = _G.TheWorld
local net_bool = _G.net_bool
local net_string = _G.net_string
local net_float = _G.net_float
local net_tinybyte = _G.net_tinybyte

local NetworkSyncManager = Class(function(self, inst)
    self.inst = inst
    self.network_vars = {}
    self.sync_callbacks = {}
    self.error_count = 0
    self.max_errors = 10 -- 最大错误次数
    self.throttle_intervals = {} -- 节流间隔配置
    self.validation_rules = {} -- 数据验证规则
    self.sync_stats = { -- 同步统计
        total_syncs = 0,
        throttled_syncs = 0,
        failed_validations = 0,
        last_sync_time = 0
    }
end)

-- 注册网络变量
function NetworkSyncManager:RegisterNetVar(name, var_type, event_name, initial_value)
    -- 增强参数验证
    if not name or type(name) ~= "string" or name == "" then
        print("[SeasonWorkshop] Error: Invalid name for RegisterNetVar: " .. tostring(name))
        return false
    end

    if not var_type or type(var_type) ~= "string" then
        print("[SeasonWorkshop] Error: Invalid var_type for RegisterNetVar: " .. tostring(var_type))
        return false
    end

    if not event_name or type(event_name) ~= "string" or event_name == "" then
        print("[SeasonWorkshop] Error: Invalid event_name for RegisterNetVar: " .. tostring(event_name))
        return false
    end

    -- 检查变量类型是否支持
    local supported_types = {bool = true, string = true, float = true, tinybyte = true}
    if not supported_types[var_type] then
        print("[SeasonWorkshop] Error: Unsupported variable type: " .. var_type)
        return false
    end

    if self.network_vars[name] then
        print("[SeasonWorkshop] Warning: Network variable already registered: " .. name)
        return false
    end

    -- 检查实例有效性
    if not self.inst or not self.inst:IsValid() then
        print("[SeasonWorkshop] Error: Invalid instance for RegisterNetVar")
        return false
    end
    
    local net_var = nil
    
    -- 根据类型创建网络变量
    if var_type == "bool" then
        net_var = net_bool(self.inst.GUID, name, event_name)
        if initial_value ~= nil then
            net_var:set(initial_value)
        end
    elseif var_type == "string" then
        net_var = net_string(self.inst.GUID, name, event_name)
        if initial_value ~= nil then
            net_var:set(initial_value)
        end
    elseif var_type == "float" then
        net_var = net_float(self.inst.GUID, name, event_name)
        if initial_value ~= nil then
            net_var:set(initial_value)
        end
    elseif var_type == "tinybyte" then
        net_var = net_tinybyte(self.inst.GUID, name, event_name)
        if initial_value ~= nil then
            net_var:set(initial_value)
        end
    else
        print("[SeasonWorkshop] Error: Unknown network variable type: " .. tostring(var_type))
        return false
    end
    
    if not net_var then
        print("[SeasonWorkshop] Error: Failed to create network variable: " .. name)
        return false
    end
    
    self.network_vars[name] = {
        var = net_var,
        type = var_type,
        event = event_name,
        last_sync_time = 0
    }
    
    print(string.format("[SeasonWorkshop] Network variable registered: %s (%s)", name, var_type))
    return true
end

-- 添加数据验证规则
function NetworkSyncManager:AddValidationRule(name, rule_func)
    if not name or not rule_func or type(rule_func) ~= "function" then
        print("[SeasonWorkshop] Error: Invalid validation rule parameters")
        return false
    end

    self.validation_rules[name] = rule_func
    return true
end

-- 设置节流间隔
function NetworkSyncManager:SetThrottleInterval(name, interval)
    if not name or not interval or interval < 0 then
        print("[SeasonWorkshop] Error: Invalid throttle parameters")
        return false
    end

    self.throttle_intervals[name] = interval
    return true
end

-- 验证网络数据
function NetworkSyncManager:ValidateNetworkData(name, value)
    -- 基础类型检查
    local var_info = self.network_vars[name]
    if not var_info then
        return false, "Variable not found"
    end

    local expected_type = var_info.type
    local actual_type = type(value)

    if expected_type == "bool" and actual_type ~= "boolean" then
        return false, "Type mismatch: expected boolean, got " .. actual_type
    elseif expected_type == "string" and actual_type ~= "string" then
        return false, "Type mismatch: expected string, got " .. actual_type
    elseif (expected_type == "float" or expected_type == "tinybyte") and actual_type ~= "number" then
        return false, "Type mismatch: expected number, got " .. actual_type
    end

    -- 范围检查
    if expected_type == "tinybyte" and (value < 0 or value > 255) then
        return false, "Value out of range for tinybyte: " .. tostring(value)
    end

    -- 季节验证
    if name:find("season") and expected_type == "string" and value ~= "" then
        local valid_seasons = {spring=true, summer=true, autumn=true, winter=true}
        if not valid_seasons[value] then
            return false, "Invalid season value: " .. tostring(value)
        end
    end

    -- 自定义验证规则
    local custom_rule = self.validation_rules[name]
    if custom_rule then
        local valid, error_msg = custom_rule(value)
        if not valid then
            return false, error_msg or "Custom validation failed"
        end
    end

    return true, nil
end

-- 检查是否应该节流
function NetworkSyncManager:ShouldThrottle(name)
    local interval = self.throttle_intervals[name]
    if not interval then
        return false -- 没有设置节流
    end

    local var_info = self.network_vars[name]
    if not var_info then
        return false
    end

    local current_time = GetTime() or 0
    local time_since_last = current_time - (var_info.last_sync_time or 0)

    return time_since_last < interval
end

-- 设置网络变量值（带错误处理、验证和节流）
function NetworkSyncManager:SetNetVar(name, value)
    if not TheWorld.ismastersim then
        print("[SeasonWorkshop] Warning: Attempted to set network variable on client: " .. tostring(name))
        return false
    end

    local var_info = self.network_vars[name]
    if not var_info then
        print("[SeasonWorkshop] Error: Network variable not found: " .. tostring(name))
        self.error_count = self.error_count + 1
        return false
    end

    -- 节流检查
    if self:ShouldThrottle(name) then
        self.sync_stats.throttled_syncs = self.sync_stats.throttled_syncs + 1
        return false -- 被节流，但不算错误
    end

    -- 数据验证
    local valid, error_msg = self:ValidateNetworkData(name, value)
    if not valid then
        print("[SeasonWorkshop] Error: Validation failed for " .. name .. ": " .. (error_msg or "unknown"))
        self.error_count = self.error_count + 1
        self.sync_stats.failed_validations = self.sync_stats.failed_validations + 1
        return false
    end

    -- 设置值
    local success = pcall(function()
        var_info.var:set(value)
        var_info.last_sync_time = GetTime() or 0
        self.sync_stats.total_syncs = self.sync_stats.total_syncs + 1
        self.sync_stats.last_sync_time = var_info.last_sync_time
    end)

    if not success then
        print("[SeasonWorkshop] Error: Failed to set network variable " .. name)
        self.error_count = self.error_count + 1
        return false
    end

    return true
end

-- 获取网络变量值
function NetworkSyncManager:GetNetVar(name)
    local var_info = self.network_vars[name]
    if not var_info then
        print("[SeasonWorkshop] Error: Network variable not found: " .. tostring(name))
        return nil
    end
    
    local success, value = pcall(function()
        return var_info.var:value()
    end)
    
    if not success then
        print("[SeasonWorkshop] Error: Failed to get network variable " .. name)
        self.error_count = self.error_count + 1
        return nil
    end
    
    return value
end

-- 注册同步回调
function NetworkSyncManager:RegisterSyncCallback(event_name, callback)
    if not event_name or not callback then
        print("[SeasonWorkshop] Error: Invalid parameters for RegisterSyncCallback")
        return false
    end
    
    if not self.sync_callbacks[event_name] then
        self.sync_callbacks[event_name] = {}
    end
    
    table.insert(self.sync_callbacks[event_name], callback)
    
    -- 监听事件
    self.inst:ListenForEvent(event_name, function(inst, data)
        -- 错误处理包装
        if not self.inst or not self.inst:IsValid() then
            print("[SeasonWorkshop] Warning: Sync manager instance invalid during callback")
            return
        end
        
        for _, cb in ipairs(self.sync_callbacks[event_name] or {}) do
            local success = pcall(cb, inst, data)
            if not success then
                print("[SeasonWorkshop] Error: Sync callback failed for event: " .. event_name)
                self.error_count = self.error_count + 1
            end
        end
    end)
    
    return true
end

-- 获取详细的同步状态
function NetworkSyncManager:GetSyncStatus()
    local status = {
        total_vars = 0,
        synced_vars = 0,
        error_count = self.error_count,
        last_sync_times = {},
        sync_stats = self.sync_stats,
        throttle_info = {},
        validation_info = {}
    }

    for name, var_info in pairs(self.network_vars) do
        status.total_vars = status.total_vars + 1
        status.last_sync_times[name] = var_info.last_sync_time

        -- 检查是否最近同步过
        local current_time = GetTime() or 0
        if current_time - var_info.last_sync_time < 60 then -- 1分钟内
            status.synced_vars = status.synced_vars + 1
        end

        -- 节流信息
        local throttle_interval = self.throttle_intervals[name]
        if throttle_interval then
            status.throttle_info[name] = {
                interval = throttle_interval,
                time_since_last = current_time - (var_info.last_sync_time or 0)
            }
        end

        -- 验证规则信息
        if self.validation_rules[name] then
            status.validation_info[name] = "Custom rule active"
        end
    end

    return status
end

-- 获取同步统计
function NetworkSyncManager:GetSyncStats()
    return {
        total_syncs = self.sync_stats.total_syncs,
        throttled_syncs = self.sync_stats.throttled_syncs,
        failed_validations = self.sync_stats.failed_validations,
        last_sync_time = self.sync_stats.last_sync_time,
        error_count = self.error_count,
        throttle_rate = self.sync_stats.total_syncs > 0 and
                       (self.sync_stats.throttled_syncs / self.sync_stats.total_syncs) or 0,
        validation_failure_rate = self.sync_stats.total_syncs > 0 and
                                 (self.sync_stats.failed_validations / self.sync_stats.total_syncs) or 0
    }
end

-- 重置错误计数
function NetworkSyncManager:ResetErrorCount()
    self.error_count = 0
    print("[SeasonWorkshop] Network sync error count reset")
end

-- 检查是否需要错误恢复
function NetworkSyncManager:NeedsErrorRecovery()
    return self.error_count >= self.max_errors
end

-- 执行错误恢复
function NetworkSyncManager:PerformErrorRecovery()
    print("[SeasonWorkshop] Performing network sync error recovery...")
    
    -- 重新初始化所有网络变量
    for name, var_info in pairs(self.network_vars) do
        local success = pcall(function()
            -- 尝试重新设置为默认值
            if var_info.type == "bool" then
                var_info.var:set(false)
            elseif var_info.type == "string" then
                var_info.var:set("")
            elseif var_info.type == "float" then
                var_info.var:set(0.0)
            elseif var_info.type == "tinybyte" then
                var_info.var:set(0)
            end
        end)
        
        if success then
            print("[SeasonWorkshop] Recovered network variable: " .. name)
        else
            print("[SeasonWorkshop] Failed to recover network variable: " .. name)
        end
    end
    
    self:ResetErrorCount()
end

-- 重置同步统计
function NetworkSyncManager:ResetSyncStats()
    self.sync_stats = {
        total_syncs = 0,
        throttled_syncs = 0,
        failed_validations = 0,
        last_sync_time = 0
    }
    print("[SeasonWorkshop] Network sync statistics reset")
end

-- 数据持久化：保存网络同步管理器状态
function NetworkSyncManager:OnSave()
    return {
        error_count = self.error_count,
        sync_stats = self.sync_stats,
        throttle_intervals = self.throttle_intervals
        -- 注意：不保存网络变量和回调，这些需要重新初始化
    }
end

-- 数据持久化：加载网络同步管理器状态
function NetworkSyncManager:OnLoad(data)
    if data then
        self.error_count = data.error_count or 0
        self.sync_stats = data.sync_stats or {
            total_syncs = 0,
            throttled_syncs = 0,
            failed_validations = 0,
            last_sync_time = 0
        }
        self.throttle_intervals = data.throttle_intervals or {}

        print(string.format("[SeasonWorkshop] Network sync manager loaded: %d errors, %d total syncs",
              self.error_count, self.sync_stats.total_syncs))
    end
end

-- 清理资源
function NetworkSyncManager:OnRemoveFromEntity()
    -- 清理所有回调
    for event_name, callbacks in pairs(self.sync_callbacks) do
        if self.inst and self.inst:IsValid() then
            -- 移除事件监听器
            for _, callback in ipairs(callbacks) do
                self.inst:RemoveEventCallback(event_name, callback)
            end
        end
    end

    -- 清理网络变量引用
    for name, var_info in pairs(self.network_vars) do
        var_info.var = nil
        var_info.last_sync_time = nil
    end

    -- 清理所有数据结构
    self.sync_callbacks = {}
    self.network_vars = {}
    self.throttle_intervals = {}
    self.validation_rules = {}
    self.sync_stats = {
        total_syncs = 0,
        throttled_syncs = 0,
        failed_validations = 0,
        last_sync_time = 0
    }

    print("[SeasonWorkshop] Network sync manager cleaned up")
end

return NetworkSyncManager
