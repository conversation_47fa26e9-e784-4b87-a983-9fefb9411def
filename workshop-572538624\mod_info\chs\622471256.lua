_G.ChinesePlus.SetConfigTranslation(mod_to_c,
	"扮演宠物 / 怪物！",
	{

	},
	{
		{
			name = "Hound",
			label = "猎犬",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},

		{
			name = "Hell Hound",
			label = "火猎犬",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},

		{
			name = "Ice Hound",
			label = "冰猎犬",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},

		{
			name = "Catcoon",
			label = "浣猫",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},

		{
			name = "Nightmare Hound",
			label = "噩梦猎犬",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},

		{
			name = "<PERSON>",
			label = "蜘蛛",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},

		{
			name = "Spider Warrior",
			label = "蜘蛛勇士",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},

		{
			name = "Depth Dweller",
			label = "白蜘蛛",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},

		{
			name = "Spider Queen",
			label = "蜘蛛女王",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},

		{
			name = "Dweller Queen",
			label = "白蜘蛛女王",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},



		{
			name = "Lavae",
			label = "熔岩虫",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},

		{
			name = "Dragonfly",
			label = "龙蝇",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},

		{
			name = "Bearger",
			label = "比格尔",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},

		{
			name = "Mossling",
			label = "莫斯林",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},

		{
			name = "Moose/Goose",
			label = "鹿角鹅",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},

		{
			name = "Deerclops",
			label = "巨鹿",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},

		{
			name = "Ghost",
			label = "鬼魂",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},

		{
			name = "Pig",
			label = "猪人",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},

		{
			name = "Merm",
			label = "鱼人",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},

		{
			name = "McTusk",
			label = "海象",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},

		{
			name = "Smallbird",
			label = "小高脚鸟",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},

		{
			name = "Tallbird",
			label = "高脚鸟",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},

		{
			name = "Tree Guard",
			label = "树精守卫",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},

		{
			name = "Beefalo",
			label = "皮弗娄牛",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},

		{
			name = "Warg",
			label = "座狼",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},

		{
			name = "Knight",
			label = "发条骑士",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},

		{
			name = "Bishop",
			label = "发条主教",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},

		{
			name = "Rook",
			label = "机械战车",
			options =
			{
				{description = "开", data = "Enable"},
				{description = "关", data = "Disable"},
			},

		},


		--[[{
			name = "Mobghosts",
			label = "Enable Mob Ghosts?",
			options =
			{
				{description = "是", data = "Yes"},
				{description = "否", data = "No"},
			},

		},]]

		{
			name = "Mobchange",
			label = "允许更换角色？",
			options =
			{
				{description = "是", data = "Enable"},
				--{description = "只有怪物", data = "Enable1"},
				{description = "否", data = "Disable"},
			},

		},

		{
			name = "Hunger",
			label = "会饥饿吗?",
			options =
			{
				{description = "是", data = "Enable"},
				{description = "否", data = "Disable"},
			},

		},

		{
			name = "MobSanity",
			label = "应该有精神值吗?",
			options =
			{
				{description = "是", data = "Enable", hover = "怪物将失去精神和疲倦。"},
				{description = "只有宠物", data = "Disable1", hover = "猪人类的怪物失去精神和疲倦。"},
				{description = "否", data = "Disable", hover = "所有怪物失去精神"},
			},

		},

		{
			name = "MobCraft",
			label = "怪物可以做任何事吗?",
			options =
			{
				{description = "是", data = "Enable", hover = "像人类一样做任何事情"},
				{description = "否", data = "Disable", hover = "怪物做怪物的动作"},
			},

		},
		--[[
		{
			name = "MobHouseMethod",
			label = "房屋的可达性",
			options =
			{
				{description = "On Spawn", data = "Enable1", hover = "Mobs will spawn with their homes"},
				{description = "Crafting", data = "Enable2", hover = "Homes will need to be crafted"},
				{description = "No Homes", data = "Disable", hover = "Homes cannot be obtained through normal means"},


			},


		},
		]]
	}
)
