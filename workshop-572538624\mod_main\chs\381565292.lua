-- NAMES
STRINGS.NAMES.ACK_MUFFIN = "方便松饼"
-- Breads (x3)
STRINGS.NAMES.NANA_BREAD = "香蕉面包"
STRINGS.NAMES.FRUIT_MUFFIN = "水果松饼"
STRINGS.NAMES.HARDTACK = "硬饼干"
-- Cakes & Pies (x4)
STRINGS.NAMES.CACTUS_CAKE = "仙人掌蛋糕" -- requires cactus
STRINGS.NAMES.MERINGUE = "酥皮馅饼"
STRINGS.NAMES.STICKY_BUN = "粘面包" -- Honeyed
STRINGS.NAMES.TARTE_TATIN = "水果蛋挞"
STRINGS.NAMES.SHAMCAKES = "蓝莓煎饼"
-- Candies & Sugars (x4 +)
STRINGS.NAMES.CANDIED_BACON = "焦糖培根" -- Honeyed
STRINGS.NAMES.CANDIED_FRUIT = "蜜饯" -- Honeyed
STRINGS.NAMES.CANDIED_NUT = "糖渍坚果" -- Honeyed
STRINGS.NAMES.MUSH_MELON = "甜瓜" -- Honeyed
STRINGS.NAMES.MUSH_MELON_COOKED = "烧甜瓜" -- Honeyed
-- Eggs (x4)
STRINGS.NAMES.OMELETTE = "蓬松蛋卷"
STRINGS.NAMES.MUSHY_EGGS = "鸡蛋羹"
STRINGS.NAMES.NOPALITO = "诺帕利托蛋" -- requires cactus
STRINGS.NAMES.SALAD_EGG = "鸡蛋沙拉"
-- Fruits (x4)
STRINGS.NAMES.FRUIT_LEATHER = "果皮"
STRINGS.NAMES.FRUIT_TRUFFLE = "水果松露" -- requires dairy (not butter/oleo)
STRINGS.NAMES.LIMONADE = "柠檬水" -- COLD - Requires Ice
STRINGS.NAMES.LIMONGELO = "明胶"
-- Meats (Beef) (x4)
STRINGS.NAMES.BRACIOLE = "牛肉卷"
STRINGS.NAMES.COLDCUTS = "冷盘" -- COLD - Requires Ice
STRINGS.NAMES.SAUSAGE_GRAVY = "肉汁香肠"
STRINGS.NAMES.SWEET_N_SOUR = "咕噜肉"
-- Meats (Fish) (x3)
STRINGS.NAMES.STUFFED_TOMANGO = "海鲜酿蛋挞"
STRINGS.NAMES.SURF_N_TURF = "冲浪拼盘"
STRINGS.NAMES.FISH_N_CHIPS = "炸鱼薯条"
STRINGS.NAMES.LOBSTER_FAKED = "烤龙虾"
-- Meats (Poultry) (x3)
STRINGS.NAMES.BEEFALO_WINGS = "牛翅" -- HOT
STRINGS.NAMES.PICATTA = "柠檬鸡肉"
STRINGS.NAMES.ROLLATINI = "鸡肉卷"
-- Mushrooms (x4 +)
STRINGS.NAMES.CITRUS_MUSHROOM = "柠檬烤蘑菇"
STRINGS.NAMES.MUSHROOM_BURGER = "蘑菇汉堡"
STRINGS.NAMES.MUSHROOM_MALODY = "臭蘑菇"
STRINGS.NAMES.MUSHROOM_MEDLEY = "蘑菇串"
STRINGS.NAMES.MUSHROOM_STEW = "蘑菇汤"
-- Salads (x4)
STRINGS.NAMES.SALAD_GRAPE = "葡萄沙拉"
STRINGS.NAMES.SALAD_CARROT = "柠檬胡萝卜沙拉"
STRINGS.NAMES.SALAD_WALDORF = "华道夫沙拉" -- requires nut
STRINGS.NAMES.SALAD_YAMION = "洋葱沙拉"
STRINGS.NAMES.SALAD_BLOOM = "野花沙拉"
-- Soups (x5)
STRINGS.NAMES.CACTUS_SOUP = "仙人掌汤" -- requires cactus
STRINGS.NAMES.CHOWDER = "海鲜杂烩"
STRINGS.NAMES.GAZPACHO = "凉菜汤" -- Cold - requires Ice
STRINGS.NAMES.GUMBO = "香辣浓汤" -- HOT
STRINGS.NAMES.SQUASH = "南瓜汤" -- requires nut
-- Snacks (x7 +)
STRINGS.NAMES.ALOO_TIKI = "番茄肉丸"
STRINGS.NAMES.CHEESE_LOG = "坚果奶酪" -- requires dairy (not butter/oleo)
STRINGS.NAMES.GRUEL = "剩粥"
STRINGS.NAMES.LIQUOR = "白酒"
STRINGS.NAMES.NUT_BUTTER = "果仁奶油" -- requires nut
STRINGS.NAMES.PORRIDGE = "粥"
STRINGS.NAMES.YAMION_RINGS = "洋葱圈"
STRINGS.NAMES.TWICE_BAKED = "精制烤薯"
-- Liquors (x5)
STRINGS.NAMES.LIQUOR = "白酒"
STRINGS.NAMES.DAIQUIRI = "鸡尾酒"
STRINGS.NAMES.MARGARITA = "玛格丽塔酒"
STRINGS.NAMES.VINO = "葡萄酒"
STRINGS.NAMES.PINA_COLADA = "冰镇果汁朗姆酒"
-- Cookables (x4)
STRINGS.NAMES.CASSEROLE = "剩菜煲" -- Ingredient: Meat, Veggie, Precook
STRINGS.NAMES.FRUIT_SYRUP = "水果糖浆" -- Honeyed, Ingredient: Sweetener
STRINGS.NAMES.MOLASSES = "糖蜜" -- Honeyed, Ingredient: Sweetener
STRINGS.NAMES.OLEO = "油" -- Ingredient: Dairy, Fat
STRINGS.NAMES.COCONUT_MILK = "椰奶" -- Ingredient: Dairy
-- Crops & Seeds
STRINGS.NAMES.GRAPRICOT = "葡萄"
STRINGS.NAMES.GRAPRICOT_COOKED = "烤葡萄"
STRINGS.NAMES.GRAPRICOT_DRIED = "葡萄干"
STRINGS.NAMES.GRAPRICOT_SEEDS = "葡萄种子"
STRINGS.NAMES.LIMON = "柠檬"
STRINGS.NAMES.LIMON_COOKED = "烤柠檬"
STRINGS.NAMES.LIMON_SEEDS = "柠檬种子"
STRINGS.NAMES.TOMANGO = "番茄芒果"
STRINGS.NAMES.TOMANGO_DRIED = "番茄芒果干"
STRINGS.NAMES.TOMANGO_COOKED = "烧番茄芒果"
STRINGS.NAMES.TOMANGO_SEEDS = "番茄芒果种子"
STRINGS.NAMES.YAMION = "洋葱"
STRINGS.NAMES.YAMION_COOKED = "烤洋葱"
STRINGS.NAMES.YAMION_SEEDS = "洋葱种子"
STRINGS.NAMES.GARLEEK = "大蒜"
STRINGS.NAMES.GARLEEK_COOKED = "熟蒜"
STRINGS.NAMES.TENTACLE_MEAT = "生触手"
STRINGS.NAMES.TENTACLE_MEAT_COOKED = "鱿鱼"
-- Shipwrecked Crops
STRINGS.NAMES.PAPAYANAPPLE = "木瓜苹果"
STRINGS.NAMES.PAPAYANAPPLE_COOKED = "烤木瓜苹果"
STRINGS.NAMES.ZUCCHILLI = "辣椒"
STRINGS.NAMES.ZUCCHILLI_COOKED = "辣椒丝"

-- DESCRIPTIONS.GENERIC
STRINGS.CHARACTERS.GENERIC.DESCRIBE.ACK_MUFFIN = "松饼麦高芬。"
-- Breads (x3)
STRINGS.CHARACTERS.GENERIC.DESCRIBE.NANA_BREAD = "比水果蛋糕好吃多了。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.FRUIT_MUFFIN = "它的味道比水果蛋糕好。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.HARDTACK = "不是很开胃，但保存完好。"
-- Cakes & Pies (x4)
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CACTUS_CAKE = "甜蜜的刺。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MERINGUE = "又轻又清新的甜点。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.STICKY_BUN = "额外的粘性。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TARTE_TATIN = "格子呢样的水果蛋挞。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SHAMCAKES = "这些都是好的。"
-- Candies & Sugars (x4 +)
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CANDIED_BACON = "奶油甜、熏肉咸。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CANDIED_FRUIT = "蜂蜜涂抹的甜蜜。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CANDIED_NUT = "他们都涂有结晶的蜂蜜。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MUSH_MELON = "它们不是我记忆力的样子。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MUSH_MELON_COOKED = "我想再要一些。"
-- Eggs (x4)
STRINGS.CHARACTERS.GENERIC.DESCRIBE.OMELETTE = "打破几个蛋的结果。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MUSHY_EGGS = "可食用的蛋。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.NOPALITO = "美味但多刺的一对。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SALAD_EGG = "一种野餐的主食。"
-- Fruits (x4)
STRINGS.CHARACTERS.GENERIC.DESCRIBE.FRUIT_LEATHER = "干燥的水果片。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.FRUIT_TRUFFLE = "一个微不足道的甜点。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.LIMONADE = "酸甜的冷饮料。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.LIMONGELO = "总是有水果明胶的地方。"
-- Meats (Beef) (x4)
STRINGS.CHARACTERS.GENERIC.DESCRIBE.BRACIOLE = "烤牛肉片。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.COLDCUTS = "完美的冷冻。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SAUSAGE_GRAVY = "油腻的香肠。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SWEET_N_SOUR = "这让我再次想起了湖南。"
-- Meats (Fish) (x3)
STRINGS.CHARACTERS.GENERIC.DESCRIBE.STUFFED_TOMANGO = "一些可疑的东西。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SURF_N_TURF = "美味的多重口味。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.FISH_N_CHIPS = "美味的多重口味。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.LOBSTER_FAKED = "它作为一个完整的晚餐是不好的，但只有奶油。"
-- Meats (Poultry) (x3)
STRINGS.CHARACTERS.GENERIC.DESCRIBE.ROLLATINI = "切成薄片，然后卷起来烤。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.BEEFALO_WINGS = "超级麻辣。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.PICATTA = "一种禽类柠檬味菜肴。"
-- Mushrooms (x4 +)
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CITRUS_MUSHROOM = "它需要大蒜。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MUSHROOM_BURGER = "蘑菇之间的热肉。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MUSHROOM_MALODY = "它的气味像腐烂了。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MUSHROOM_MEDLEY = "诱人的五颜六色的真菌。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MUSHROOM_STEW = "奶油蘑菇汤。"
-- Salads (x4)
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SALAD_CARROT = "比只烘烤它们好。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SALAD_GRAPE = "不是很正确，但它会有用。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SALAD_WALDORF = "我向厨师致意。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SALAD_YAMION = "它具有强烈的味道。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SALAD_BLOOM = "这只是装饰。"
-- Soups (x5)
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CACTUS_SOUP = "一桶的味道。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CHOWDER = "厚厚的奶油。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.GAZPACHO = "最好凉着吃。" -- Cold - requires Ice
STRINGS.CHARACTERS.GENERIC.DESCRIBE.GUMBO = "额外的麻辣味治疗。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SQUASH = "一般的完美。"
-- Snacks (x7 +)
STRINGS.CHARACTERS.GENERIC.DESCRIBE.ALOO_TIKI = "有点脆脆的。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.LIQUOR = "安神的东西。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CHEESE_LOG = "比木质的容易食用。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.GRUEL = "清淡的口味完美的粥。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.NUT_BUTTER = "膳食科学的顶峰。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.PORRIDGE = "温和的口味完美的玉米粥。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.YAMION_RINGS = "一种常见的安慰性治疗。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TWICE_BAKED = "装有肉、往下滴着油"
-- Liquors (x5)
STRINGS.CHARACTERS.GENERIC.DESCRIBE.LIQUOR = "一点补品一点酒。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.DAIQUIRI = "丰盛的鸡尾酒。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MARGARITA = "但我丢失了我的盐筛。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.PINA_COLADA = "是的，我喜欢冰镇果汁朗姆酒。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.VINO = "一点东西来镇定神经。"
-- Cookables (x4)
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CASSEROLE = "剩菜剩饭。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.FRUIT_SYRUP = "高果糖糖浆。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MOLASSES = "鼹鼠的甜蜜。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.OLEO = "不错，但我尝过黄油。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.COCONUT_MILK = "树的牛奶吗？疯了。"
-- Crops & Seeds
STRINGS.CHARACTERS.GENERIC.DESCRIBE.GRAPRICOT = "葡萄树的果实。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.GRAPRICOT_COOKED = "香甜美味。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.GRAPRICOT_DRIED = "干了，但是依旧很甜。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.GRAPRICOT_SEEDS = "这些种子上面都是坑。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.LIMON = "酸的柑橘类水果。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.LIMON_COOKED = "现在不是很吸引人。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.LIMON_SEEDS = "这些将长成柠檬树。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TOMANGO = "有些人争论这是水果还是蔬菜。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TOMANGO_DRIED = "美味的菜干或水果干。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TOMANGO_COOKED = "有些人更喜欢在绿色时烹饪。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TOMANGO_SEEDS = "这是一种水果种子还是一种蔬菜种子？"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.YAMION = "一种可食用的根状茎。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.YAMION_COOKED = "它的果皮分层。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.YAMION_SEEDS = "我不知道它们甚至有种子。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.GARLEEK = "刺鼻的味道。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.GARLEEK_COOKED = "要命的味道。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TENTACLE_MEAT = "鱿鱼的形状。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TENTACLE_MEAT_COOKED = "美味的触手。"
-- Shipwrecked Crops
STRINGS.CHARACTERS.GENERIC.DESCRIBE.PAPAYANAPPLE = "一种奇怪的水果。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.PAPAYANAPPLE_COOKED = "新鲜烤水果。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.ZUCCHILLI = "辣的小南瓜。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.ZUCCHILLI_COOKED = "一些辣的切片"

-- DESCRIPTIONS.WX78
STRINGS.CHARACTERS.WX78.DESCRIBE.ACK_MUFFIN = nil
-- Breads (x3)
STRINGS.CHARACTERS.WX78.DESCRIBE.NANA_BREAD = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.FRUIT_MUFFIN = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.HARDTACK = nil
-- Cakes & Pies (x4)
STRINGS.CHARACTERS.WX78.DESCRIBE.CACTUS_CAKE = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.MERINGUE = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.STICKY_BUN = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.TARTE_TATIN = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.SHAMCAKES = nil
-- Candies & Sugars (x4 +)
STRINGS.CHARACTERS.WX78.DESCRIBE.CANDIED_BACON = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.CANDIED_FRUIT = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.CANDIED_NUT = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.MUSH_MELON = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.MUSH_MELON_COOKED = nil
-- Eggs (x4)
STRINGS.CHARACTERS.WX78.DESCRIBE.OMELETTE = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.MUSHY_EGGS = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.NOPALITO = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.SALAD_EGG = nil
-- Fruits (x4)
STRINGS.CHARACTERS.WX78.DESCRIBE.FRUIT_LEATHER = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.FRUIT_TRUFFLE = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.LIMONADE = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.LIMONGELO = nil
-- Meats (Beef) (x4)
STRINGS.CHARACTERS.WX78.DESCRIBE.BRACIOLE = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.COLDCUTS = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.SAUSAGE_GRAVY = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.SWEET_N_SOUR = nil
-- Meats (Fish) (x3)
STRINGS.CHARACTERS.WX78.DESCRIBE.STUFFED_TOMANGO = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.SURF_N_TURF = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.FISH_N_CHIPS = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.LOBSTER_FAKED = nil
-- Meats (Poultry) (x3)
STRINGS.CHARACTERS.WX78.DESCRIBE.ROLLATINI = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.BEEFALO_WINGS = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.PICATTA = nil
-- Mushrooms (x4 +)
STRINGS.CHARACTERS.WX78.DESCRIBE.CITRUS_MUSHROOM = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.MUSHROOM_BURGER = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.MUSHROOM_MALODY = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.MUSHROOM_MEDLEY = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.MUSHROOM_STEW = nil
-- Salads (x4)
STRINGS.CHARACTERS.WX78.DESCRIBE.SALAD_CARROT = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.SALAD_GRAPE = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.SALAD_WALDORF = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.SALAD_YAMION = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.SALAD_BLOOM = nil
-- Soups (x5)
STRINGS.CHARACTERS.WX78.DESCRIBE.CACTUS_SOUP = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.CHOWDER = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.GAZPACHO = nil -- Cold - requires Ice
STRINGS.CHARACTERS.WX78.DESCRIBE.GUMBO = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.SQUASH = nil
-- Snacks (x7 +)
STRINGS.CHARACTERS.WX78.DESCRIBE.ALOO_TIKI = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.LIQUOR = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.CHEESE_LOG = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.GRUEL = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.NUT_BUTTER = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.PORRIDGE = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.YAMION_RINGS = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.TWICE_BAKED = nil
-- Liquors (x5)
STRINGS.CHARACTERS.WX78.DESCRIBE.LIQUOR = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.DAIQUIRI = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.MARGARITA = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.PINA_COLADA = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.VINO = nil
-- Cookables (x4)
STRINGS.CHARACTERS.WX78.DESCRIBE.CASSEROLE = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.FRUIT_SYRUP = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.MOLASSES = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.OLEO = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.COCONUT_MILK = nil
-- Crops & Seeds
STRINGS.CHARACTERS.WX78.DESCRIBE.GRAPRICOT = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.GRAPRICOT_COOKED = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.GRAPRICOT_DRIED = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.GRAPRICOT_SEEDS = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.LIMON = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.LIMON_COOKED = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.LIMON_SEEDS = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.TOMANGO = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.TOMANGO_DRIED = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.TOMANGO_COOKED = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.TOMANGO_SEEDS = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.YAMION = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.YAMION_COOKED = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.YAMION_SEEDS = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.GARLEEK = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.GARLEEK_COOKED = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.TENTACLE_MEAT = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.TENTACLE_MEAT_COOKED = nil
-- Shipwrecked Crops
STRINGS.CHARACTERS.WX78.DESCRIBE.PAPAYANAPPLE = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.PAPAYANAPPLE_COOKED = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.ZUCCHILLI = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.ZUCCHILLI_COOKED = nil

-- DESCRIPTIONS.WOODIE
STRINGS.CHARACTERS.WOODIE.DESCRIBE.ACK_MUFFIN = nil
-- Breads (x3)
STRINGS.CHARACTERS.WOODIE.DESCRIBE.NANA_BREAD = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.FRUIT_MUFFIN = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.HARDTACK = nil
-- Cakes & Pies (x4)
STRINGS.CHARACTERS.WOODIE.DESCRIBE.CACTUS_CAKE = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.MERINGUE = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.STICKY_BUN = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.TARTE_TATIN = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.SHAMCAKES = nil
-- Candies & Sugars (x4 +)
STRINGS.CHARACTERS.WOODIE.DESCRIBE.CANDIED_BACON = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.CANDIED_FRUIT = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.CANDIED_NUT = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.MUSH_MELON = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.MUSH_MELON_COOKED = nil
-- Eggs (x4)
STRINGS.CHARACTERS.WOODIE.DESCRIBE.OMELETTE = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.MUSHY_EGGS = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.NOPALITO = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.SALAD_EGG = nil
-- Fruits (x4)
STRINGS.CHARACTERS.WOODIE.DESCRIBE.FRUIT_LEATHER = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.FRUIT_TRUFFLE = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.LIMONADE = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.LIMONGELO = nil
-- Meats (Beef) (x4)
STRINGS.CHARACTERS.WOODIE.DESCRIBE.BRACIOLE = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.COLDCUTS = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.SAUSAGE_GRAVY = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.SWEET_N_SOUR = nil
-- Meats (Fish) (x3)
STRINGS.CHARACTERS.WOODIE.DESCRIBE.STUFFED_TOMANGO = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.SURF_N_TURF = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.FISH_N_CHIPS = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.LOBSTER_FAKED = nil
-- Meats (Poultry) (x3)
STRINGS.CHARACTERS.WOODIE.DESCRIBE.ROLLATINI = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.BEEFALO_WINGS = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.PICATTA = nil
-- Mushrooms (x4 +)
STRINGS.CHARACTERS.WOODIE.DESCRIBE.CITRUS_MUSHROOM = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.MUSHROOM_BURGER = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.MUSHROOM_MALODY = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.MUSHROOM_MEDLEY = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.MUSHROOM_STEW = nil
-- Salads (x4)
STRINGS.CHARACTERS.WOODIE.DESCRIBE.SALAD_CARROT = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.SALAD_GRAPE = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.SALAD_WALDORF = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.SALAD_YAMION = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.SALAD_BLOOM = nil
-- Soups (x5)
STRINGS.CHARACTERS.WOODIE.DESCRIBE.CACTUS_SOUP = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.CHOWDER = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.GAZPACHO = nil -- Cold - requires Ice
STRINGS.CHARACTERS.WOODIE.DESCRIBE.GUMBO = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.SQUASH = nil
-- Snacks (x7 +)
STRINGS.CHARACTERS.WOODIE.DESCRIBE.ALOO_TIKI = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.LIQUOR = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.CHEESE_LOG = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.GRUEL = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.NUT_BUTTER = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.PORRIDGE = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.YAMION_RINGS = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.TWICE_BAKED = nil
-- Liquors (x5)
STRINGS.CHARACTERS.WOODIE.DESCRIBE.LIQUOR = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.DAIQUIRI = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.MARGARITA = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.PINA_COLADA = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.VINO = nil
-- Cookables (x4)
STRINGS.CHARACTERS.WOODIE.DESCRIBE.CASSEROLE = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.FRUIT_SYRUP = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.MOLASSES = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.OLEO = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.COCONUT_MILK = nil
-- Crops & Seeds
STRINGS.CHARACTERS.WOODIE.DESCRIBE.GRAPRICOT = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.GRAPRICOT_COOKED = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.GRAPRICOT_DRIED = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.GRAPRICOT_SEEDS = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.LIMON = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.LIMON_COOKED = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.LIMON_SEEDS = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.TOMANGO = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.TOMANGO_DRIED = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.TOMANGO_COOKED = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.TOMANGO_SEEDS = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.YAMION = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.YAMION_COOKED = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.YAMION_SEEDS = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.GARLEEK = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.GARLEEK_COOKED = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.TENTACLE_MEAT = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.TENTACLE_MEAT_COOKED = nil
-- Shipwrecked Crops
STRINGS.CHARACTERS.WOODIE.DESCRIBE.PAPAYANAPPLE = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.PAPAYANAPPLE_COOKED = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.ZUCCHILLI = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.ZUCCHILLI_COOKED = nil

-- DESCRIPTIONS.WILLOW
STRINGS.CHARACTERS.WILLOW.DESCRIBE.ACK_MUFFIN = nil
-- Breads (x3)
STRINGS.CHARACTERS.WILLOW.DESCRIBE.NANA_BREAD = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.FRUIT_MUFFIN = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.HARDTACK = nil
-- Cakes & Pies (x4)
STRINGS.CHARACTERS.WILLOW.DESCRIBE.CACTUS_CAKE = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.MERINGUE = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.STICKY_BUN = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.TARTE_TATIN = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.SHAMCAKES = nil
-- Candies & Sugars (x4 +)
STRINGS.CHARACTERS.WILLOW.DESCRIBE.CANDIED_BACON = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.CANDIED_FRUIT = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.CANDIED_NUT = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.MUSH_MELON = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.MUSH_MELON_COOKED = nil
-- Eggs (x4)
STRINGS.CHARACTERS.WILLOW.DESCRIBE.OMELETTE = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.MUSHY_EGGS = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.NOPALITO = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.SALAD_EGG = nil
-- Fruits (x4)
STRINGS.CHARACTERS.WILLOW.DESCRIBE.FRUIT_LEATHER = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.FRUIT_TRUFFLE = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.LIMONADE = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.LIMONGELO = nil
-- Meats (Beef) (x4)
STRINGS.CHARACTERS.WILLOW.DESCRIBE.BRACIOLE = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.COLDCUTS = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.SAUSAGE_GRAVY = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.SWEET_N_SOUR = nil
-- Meats (Fish) (x3)
STRINGS.CHARACTERS.WILLOW.DESCRIBE.STUFFED_TOMANGO = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.SURF_N_TURF = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.FISH_N_CHIPS = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.LOBSTER_FAKED = nil
-- Meats (Poultry) (x3)
STRINGS.CHARACTERS.WILLOW.DESCRIBE.ROLLATINI = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.BEEFALO_WINGS = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.PICATTA = nil
-- Mushrooms (x4 +)
STRINGS.CHARACTERS.WILLOW.DESCRIBE.CITRUS_MUSHROOM = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.MUSHROOM_BURGER = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.MUSHROOM_MALODY = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.MUSHROOM_MEDLEY = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.MUSHROOM_STEW = nil
-- Salads (x4)
STRINGS.CHARACTERS.WILLOW.DESCRIBE.SALAD_CARROT = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.SALAD_GRAPE = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.SALAD_WALDORF = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.SALAD_YAMION = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.SALAD_BLOOM = nil
-- Soups (x5)
STRINGS.CHARACTERS.WILLOW.DESCRIBE.CACTUS_SOUP = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.CHOWDER = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.GAZPACHO = nil -- Cold - requires Ice
STRINGS.CHARACTERS.WILLOW.DESCRIBE.GUMBO = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.SQUASH = nil
-- Snacks (x7 +)
STRINGS.CHARACTERS.WILLOW.DESCRIBE.ALOO_TIKI = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.LIQUOR = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.CHEESE_LOG = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.GRUEL = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.NUT_BUTTER = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.PORRIDGE = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.YAMION_RINGS = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.TWICE_BAKED = nil
-- Liquors (x5)
STRINGS.CHARACTERS.WILLOW.DESCRIBE.LIQUOR = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.DAIQUIRI = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.MARGARITA = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.PINA_COLADA = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.VINO = nil
-- Cookables (x4)
STRINGS.CHARACTERS.WILLOW.DESCRIBE.CASSEROLE = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.FRUIT_SYRUP = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.MOLASSES = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.OLEO = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.COCONUT_MILK = nil
-- Crops & Seeds
STRINGS.CHARACTERS.WILLOW.DESCRIBE.GRAPRICOT = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.GRAPRICOT_COOKED = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.GRAPRICOT_DRIED = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.GRAPRICOT_SEEDS = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.LIMON = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.LIMON_COOKED = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.LIMON_SEEDS = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.TOMANGO = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.TOMANGO_DRIED = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.TOMANGO_COOKED = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.TOMANGO_SEEDS = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.YAMION = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.YAMION_COOKED = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.YAMION_SEEDS = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.GARLEEK = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.GARLEEK_COOKED = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.TENTACLE_MEAT = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.TENTACLE_MEAT_COOKED = nil
-- Shipwrecked Crops
STRINGS.CHARACTERS.WILLOW.DESCRIBE.PAPAYANAPPLE = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.PAPAYANAPPLE_COOKED = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.ZUCCHILLI = nil
STRINGS.CHARACTERS.WILLOW.DESCRIBE.ZUCCHILLI_COOKED = nil

-- DESCRIPTIONS.WOLFGANG
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.ACK_MUFFIN = nil
-- Breads (x3)
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.NANA_BREAD = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.FRUIT_MUFFIN = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.HARDTACK = nil
-- Cakes & Pies (x4)
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.CACTUS_CAKE = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.MERINGUE = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.STICKY_BUN = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.TARTE_TATIN = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.SHAMCAKES = nil
-- Candies & Sugars (x4 +)
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.CANDIED_BACON = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.CANDIED_FRUIT = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.CANDIED_NUT = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.MUSH_MELON = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.MUSH_MELON_COOKED = nil
-- Eggs (x4)
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.OMELETTE = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.MUSHY_EGGS = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.NOPALITO = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.SALAD_EGG = nil
-- Fruits (x4)
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.FRUIT_LEATHER = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.FRUIT_TRUFFLE = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.LIMONADE = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.LIMONGELO = nil
-- Meats (Beef) (x4)
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.BRACIOLE = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.COLDCUTS = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.SAUSAGE_GRAVY = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.SWEET_N_SOUR = nil
-- Meats (Fish) (x3)
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.STUFFED_TOMANGO = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.SURF_N_TURF = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.FISH_N_CHIPS = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.LOBSTER_FAKED = nil
-- Meats (Poultry) (x3)
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.ROLLATINI = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.BEEFALO_WINGS = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.PICATTA = nil
-- Mushrooms (x4 +)
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.CITRUS_MUSHROOM = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.MUSHROOM_BURGER = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.MUSHROOM_MALODY = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.MUSHROOM_MEDLEY = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.MUSHROOM_STEW = nil
-- Salads (x4)
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.SALAD_CARROT = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.SALAD_GRAPE = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.SALAD_WALDORF = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.SALAD_YAMION = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.SALAD_BLOOM = nil
-- Soups (x5)
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.CACTUS_SOUP = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.CHOWDER = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.GAZPACHO = nil -- Cold - requires Ice
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.GUMBO = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.SQUASH = nil
-- Snacks (x7 +)
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.ALOO_TIKI = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.LIQUOR = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.CHEESE_LOG = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.GRUEL = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.NUT_BUTTER = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.PORRIDGE = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.YAMION_RINGS = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.TWICE_BAKED = nil
-- Liquors (x5)
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.LIQUOR = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.DAIQUIRI = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.MARGARITA = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.PINA_COLADA = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.VINO = nil
-- Cookables (x4)
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.CASSEROLE = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.FRUIT_SYRUP = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.MOLASSES = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.OLEO = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.COCONUT_MILK = nil
-- Crops & Seeds
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.GRAPRICOT = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.GRAPRICOT_COOKED = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.GRAPRICOT_DRIED = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.GRAPRICOT_SEEDS = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.LIMON = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.LIMON_COOKED = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.LIMON_SEEDS = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.TOMANGO = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.TOMANGO_DRIED = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.TOMANGO_COOKED = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.TOMANGO_SEEDS = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.YAMION = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.YAMION_COOKED = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.YAMION_SEEDS = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.GARLEEK = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.GARLEEK_COOKED = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.TENTACLE_MEAT = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.TENTACLE_MEAT_COOKED = nil
-- Shipwrecked Crops
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.PAPAYANAPPLE = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.PAPAYANAPPLE_COOKED = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.ZUCCHILLI = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.ZUCCHILLI_COOKED = nil

-- DESCRIPTIONS.WENDY
STRINGS.CHARACTERS.WENDY.DESCRIBE.ACK_MUFFIN = nil
-- Breads (x3)
STRINGS.CHARACTERS.WENDY.DESCRIBE.NANA_BREAD = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.FRUIT_MUFFIN = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.HARDTACK = nil
-- Cakes & Pies (x4)
STRINGS.CHARACTERS.WENDY.DESCRIBE.CACTUS_CAKE = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.MERINGUE = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.STICKY_BUN = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.TARTE_TATIN = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.SHAMCAKES = nil
-- Candies & Sugars (x4 +)
STRINGS.CHARACTERS.WENDY.DESCRIBE.CANDIED_BACON = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.CANDIED_FRUIT = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.CANDIED_NUT = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.MUSH_MELON = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.MUSH_MELON_COOKED = nil
-- Eggs (x4)
STRINGS.CHARACTERS.WENDY.DESCRIBE.OMELETTE = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.MUSHY_EGGS = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.NOPALITO = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.SALAD_EGG = nil
-- Fruits (x4)
STRINGS.CHARACTERS.WENDY.DESCRIBE.FRUIT_LEATHER = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.FRUIT_TRUFFLE = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.LIMONADE = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.LIMONGELO = nil
-- Meats (Beef) (x4)
STRINGS.CHARACTERS.WENDY.DESCRIBE.BRACIOLE = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.COLDCUTS = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.SAUSAGE_GRAVY = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.SWEET_N_SOUR = nil
-- Meats (Fish) (x3)
STRINGS.CHARACTERS.WENDY.DESCRIBE.STUFFED_TOMANGO = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.SURF_N_TURF = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.FISH_N_CHIPS = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.LOBSTER_FAKED = nil
-- Meats (Poultry) (x3)
STRINGS.CHARACTERS.WENDY.DESCRIBE.ROLLATINI = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.BEEFALO_WINGS = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.PICATTA = nil
-- Mushrooms (x4 +)
STRINGS.CHARACTERS.WENDY.DESCRIBE.CITRUS_MUSHROOM = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.MUSHROOM_BURGER = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.MUSHROOM_MALODY = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.MUSHROOM_MEDLEY = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.MUSHROOM_STEW = nil
-- Salads (x4)
STRINGS.CHARACTERS.WENDY.DESCRIBE.SALAD_CARROT = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.SALAD_GRAPE = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.SALAD_WALDORF = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.SALAD_YAMION = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.SALAD_BLOOM = nil
-- Soups (x5)
STRINGS.CHARACTERS.WENDY.DESCRIBE.CACTUS_SOUP = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.CHOWDER = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.GAZPACHO = nil -- Cold - requires Ice
STRINGS.CHARACTERS.WENDY.DESCRIBE.GUMBO = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.SQUASH = nil
-- Snacks (x7 +)
STRINGS.CHARACTERS.WENDY.DESCRIBE.ALOO_TIKI = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.LIQUOR = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.CHEESE_LOG = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.GRUEL = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.NUT_BUTTER = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.PORRIDGE = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.YAMION_RINGS = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.TWICE_BAKED = nil
-- Liquors (x5)
STRINGS.CHARACTERS.WENDY.DESCRIBE.LIQUOR = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.DAIQUIRI = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.MARGARITA = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.PINA_COLADA = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.VINO = nil
-- Cookables (x4)
STRINGS.CHARACTERS.WENDY.DESCRIBE.CASSEROLE = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.FRUIT_SYRUP = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.MOLASSES = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.OLEO = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.COCONUT_MILK = nil
-- Crops & Seeds
STRINGS.CHARACTERS.WENDY.DESCRIBE.GRAPRICOT = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.GRAPRICOT_COOKED = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.GRAPRICOT_DRIED = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.GRAPRICOT_SEEDS = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.LIMON = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.LIMON_COOKED = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.LIMON_SEEDS = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.TOMANGO = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.TOMANGO_DRIED = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.TOMANGO_COOKED = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.TOMANGO_SEEDS = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.YAMION = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.YAMION_COOKED = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.YAMION_SEEDS = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.GARLEEK = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.GARLEEK_COOKED = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.TENTACLE_MEAT = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.TENTACLE_MEAT_COOKED = nil
-- Shipwrecked Crops
STRINGS.CHARACTERS.WENDY.DESCRIBE.PAPAYANAPPLE = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.PAPAYANAPPLE_COOKED = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.ZUCCHILLI = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.ZUCCHILLI_COOKED = nil

-- DESCRIPTIONS.WICKERBOTTOM
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.ACK_MUFFIN = nil
-- Breads (x3)
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.NANA_BREAD = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.FRUIT_MUFFIN = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.HARDTACK = nil
-- Cakes & Pies (x4)
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.CACTUS_CAKE = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.MERINGUE = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.STICKY_BUN = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.TARTE_TATIN = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.SHAMCAKES = nil
-- Candies & Sugars (x4 +)
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.CANDIED_BACON = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.CANDIED_FRUIT = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.CANDIED_NUT = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.MUSH_MELON = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.MUSH_MELON_COOKED = nil
-- Eggs (x4)
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.OMELETTE = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.MUSHY_EGGS = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.NOPALITO = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.SALAD_EGG = nil
-- Fruits (x4)
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.FRUIT_LEATHER = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.FRUIT_TRUFFLE = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.LIMONADE = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.LIMONGELO = nil
-- Meats (Beef) (x4)
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.BRACIOLE = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.COLDCUTS = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.SAUSAGE_GRAVY = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.SWEET_N_SOUR = nil
-- Meats (Fish) (x3)
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.STUFFED_TOMANGO = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.SURF_N_TURF = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.FISH_N_CHIPS = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.LOBSTER_FAKED = nil
-- Meats (Poultry) (x3)
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.ROLLATINI = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.BEEFALO_WINGS = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.PICATTA = nil
-- Mushrooms (x4 +)
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.CITRUS_MUSHROOM = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.MUSHROOM_BURGER = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.MUSHROOM_MALODY = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.MUSHROOM_MEDLEY = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.MUSHROOM_STEW = nil
-- Salads (x4)
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.SALAD_CARROT = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.SALAD_GRAPE = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.SALAD_WALDORF = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.SALAD_YAMION = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.SALAD_BLOOM = nil
-- Soups (x5)
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.CACTUS_SOUP = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.CHOWDER = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.GAZPACHO = nil -- Cold - requires Ice
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.GUMBO = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.SQUASH = nil
-- Snacks (x7 +)
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.ALOO_TIKI = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.LIQUOR = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.CHEESE_LOG = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.GRUEL = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.NUT_BUTTER = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.PORRIDGE = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.YAMION_RINGS = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.TWICE_BAKED = nil
-- Liquors (x5)
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.LIQUOR = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.DAIQUIRI = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.MARGARITA = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.PINA_COLADA = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.VINO = nil
-- Cookables (x4)
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.CASSEROLE = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.FRUIT_SYRUP = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.MOLASSES = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.OLEO = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.COCONUT_MILK = nil
-- Crops & Seeds
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.GRAPRICOT = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.GRAPRICOT_COOKED = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.GRAPRICOT_DRIED = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.GRAPRICOT_SEEDS = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.LIMON = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.LIMON_COOKED = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.LIMON_SEEDS = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.TOMANGO = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.TOMANGO_DRIED = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.TOMANGO_COOKED = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.TOMANGO_SEEDS = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.YAMION = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.YAMION_COOKED = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.YAMION_SEEDS = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.GARLEEK = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.GARLEEK_COOKED = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.TENTACLE_MEAT = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.TENTACLE_MEAT_COOKED = nil
-- Shipwrecked Crops
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.PAPAYANAPPLE = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.PAPAYANAPPLE_COOKED = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.ZUCCHILLI = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.ZUCCHILLI_COOKED = nil

-- DESCRIPTIONS.WAXWELL
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.ACK_MUFFIN = nil
-- Breads (x3)
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.NANA_BREAD = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.FRUIT_MUFFIN = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.HARDTACK = nil
-- Cakes & Pies (x4)
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.CACTUS_CAKE = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.MERINGUE = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.STICKY_BUN = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.TARTE_TATIN = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.SHAMCAKES = nil
-- Candies & Sugars (x4 +)
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.CANDIED_BACON = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.CANDIED_FRUIT = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.CANDIED_NUT = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.MUSH_MELON = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.MUSH_MELON_COOKED = nil
-- Eggs (x4)
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.OMELETTE = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.MUSHY_EGGS = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.NOPALITO = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.SALAD_EGG = nil
-- Fruits (x4)
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.FRUIT_LEATHER = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.FRUIT_TRUFFLE = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.LIMONADE = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.LIMONGELO = nil
-- Meats (Beef) (x4)
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.BRACIOLE = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.COLDCUTS = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.SAUSAGE_GRAVY = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.SWEET_N_SOUR = nil
-- Meats (Fish) (x3)
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.STUFFED_TOMANGO = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.SURF_N_TURF = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.FISH_N_CHIPS = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.LOBSTER_FAKED = nil
-- Meats (Poultry) (x3)
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.ROLLATINI = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.BEEFALO_WINGS = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.PICATTA = nil
-- Mushrooms (x4 +)
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.CITRUS_MUSHROOM = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.MUSHROOM_BURGER = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.MUSHROOM_MALODY = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.MUSHROOM_MEDLEY = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.MUSHROOM_STEW = nil
-- Salads (x4)
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.SALAD_CARROT = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.SALAD_GRAPE = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.SALAD_WALDORF = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.SALAD_YAMION = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.SALAD_BLOOM = nil
-- Soups (x5)
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.CACTUS_SOUP = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.CHOWDER = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.GAZPACHO = nil -- Cold - requires Ice
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.GUMBO = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.SQUASH = nil
-- Snacks (x7 +)
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.ALOO_TIKI = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.LIQUOR = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.CHEESE_LOG = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.GRUEL = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.NUT_BUTTER = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.PORRIDGE = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.YAMION_RINGS = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.TWICE_BAKED = nil
-- Liquors (x5)
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.LIQUOR = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.DAIQUIRI = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.MARGARITA = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.PINA_COLADA = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.VINO = nil
-- Cookables (x4)
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.CASSEROLE = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.FRUIT_SYRUP = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.MOLASSES = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.OLEO = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.COCONUT_MILK = nil
-- Crops & Seeds
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.GRAPRICOT = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.GRAPRICOT_COOKED = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.GRAPRICOT_DRIED = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.GRAPRICOT_SEEDS = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.LIMON = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.LIMON_COOKED = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.LIMON_SEEDS = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.TOMANGO = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.TOMANGO_DRIED = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.TOMANGO_COOKED = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.TOMANGO_SEEDS = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.YAMION = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.YAMION_COOKED = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.YAMION_SEEDS = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.GARLEEK = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.GARLEEK_COOKED = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.TENTACLE_MEAT = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.TENTACLE_MEAT_COOKED = nil
-- Shipwrecked Crops
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.PAPAYANAPPLE = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.PAPAYANAPPLE_COOKED = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.ZUCCHILLI = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.ZUCCHILLI_COOKED = nil

-- DESCRIPTIONS.WATHGRITHR
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.ACK_MUFFIN = nil
-- Breads (x3)
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.NANA_BREAD = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.FRUIT_MUFFIN = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.HARDTACK = nil
-- Cakes & Pies (x4)
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.CACTUS_CAKE = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.MERINGUE = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.STICKY_BUN = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.TARTE_TATIN = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.SHAMCAKES = nil
-- Candies & Sugars (x4 +)
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.CANDIED_BACON = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.CANDIED_FRUIT = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.CANDIED_NUT = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.MUSH_MELON = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.MUSH_MELON_COOKED = nil
-- Eggs (x4)
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.OMELETTE = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.MUSHY_EGGS = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.NOPALITO = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.SALAD_EGG = nil
-- Fruits (x4)
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.FRUIT_LEATHER = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.FRUIT_TRUFFLE = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.LIMONADE = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.LIMONGELO = nil
-- Meats (Beef) (x4)
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.BRACIOLE = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.COLDCUTS = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.SAUSAGE_GRAVY = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.SWEET_N_SOUR = nil
-- Meats (Fish) (x3)
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.STUFFED_TOMANGO = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.SURF_N_TURF = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.FISH_N_CHIPS = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.LOBSTER_FAKED = nil
-- Meats (Poultry) (x3)
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.ROLLATINI = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.BEEFALO_WINGS = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.PICATTA = nil
-- Mushrooms (x4 +)
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.CITRUS_MUSHROOM = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.MUSHROOM_BURGER = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.MUSHROOM_MALODY = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.MUSHROOM_MEDLEY = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.MUSHROOM_STEW = nil
-- Salads (x4)
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.SALAD_CARROT = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.SALAD_GRAPE = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.SALAD_WALDORF = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.SALAD_YAMION = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.SALAD_BLOOM = nil
-- Soups (x5)
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.CACTUS_SOUP = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.CHOWDER = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.GAZPACHO = nil -- Cold - requires Ice
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.GUMBO = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.SQUASH = nil
-- Snacks (x7 +)
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.ALOO_TIKI = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.LIQUOR = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.CHEESE_LOG = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.GRUEL = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.NUT_BUTTER = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.PORRIDGE = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.YAMION_RINGS = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.TWICE_BAKED = nil
-- Liquors (x5)
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.LIQUOR = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.DAIQUIRI = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.MARGARITA = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.PINA_COLADA = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.VINO = nil
-- Cookables (x4)
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.CASSEROLE = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.FRUIT_SYRUP = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.MOLASSES = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.OLEO = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.COCONUT_MILK = nil
-- Crops & Seeds
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.GRAPRICOT = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.GRAPRICOT_COOKED = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.GRAPRICOT_DRIED = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.GRAPRICOT_SEEDS = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.LIMON = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.LIMON_COOKED = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.LIMON_SEEDS = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.TOMANGO = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.TOMANGO_DRIED = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.TOMANGO_COOKED = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.TOMANGO_SEEDS = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.YAMION = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.YAMION_COOKED = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.YAMION_SEEDS = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.GARLEEK = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.GARLEEK_COOKED = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.TENTACLE_MEAT = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.TENTACLE_MEAT_COOKED = nil
-- Shipwrecked Crops
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.PAPAYANAPPLE = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.PAPAYANAPPLE_COOKED = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.ZUCCHILLI = nil
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.ZUCCHILLI_COOKED = nil

-- DESCRIPTIONS.WEBBER
STRINGS.CHARACTERS.WEBBER.DESCRIBE.ACK_MUFFIN = nil
-- Breads (x3)
STRINGS.CHARACTERS.WEBBER.DESCRIBE.NANA_BREAD = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.FRUIT_MUFFIN = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.HARDTACK = nil
-- Cakes & Pies (x4)
STRINGS.CHARACTERS.WEBBER.DESCRIBE.CACTUS_CAKE = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.MERINGUE = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.STICKY_BUN = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.TARTE_TATIN = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.SHAMCAKES = nil
-- Candies & Sugars (x4 +)
STRINGS.CHARACTERS.WEBBER.DESCRIBE.CANDIED_BACON = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.CANDIED_FRUIT = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.CANDIED_NUT = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.MUSH_MELON = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.MUSH_MELON_COOKED = nil
-- Eggs (x4)
STRINGS.CHARACTERS.WEBBER.DESCRIBE.OMELETTE = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.MUSHY_EGGS = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.NOPALITO = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.SALAD_EGG = nil
-- Fruits (x4)
STRINGS.CHARACTERS.WEBBER.DESCRIBE.FRUIT_LEATHER = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.FRUIT_TRUFFLE = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.LIMONADE = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.LIMONGELO = nil
-- Meats (Beef) (x4)
STRINGS.CHARACTERS.WEBBER.DESCRIBE.BRACIOLE = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.COLDCUTS = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.SAUSAGE_GRAVY = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.SWEET_N_SOUR = nil
-- Meats (Fish) (x3)
STRINGS.CHARACTERS.WEBBER.DESCRIBE.STUFFED_TOMANGO = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.SURF_N_TURF = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.FISH_N_CHIPS = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.LOBSTER_FAKED = nil
-- Meats (Poultry) (x3)
STRINGS.CHARACTERS.WEBBER.DESCRIBE.ROLLATINI = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.BEEFALO_WINGS = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.PICATTA = nil
-- Mushrooms (x4 +)
STRINGS.CHARACTERS.WEBBER.DESCRIBE.CITRUS_MUSHROOM = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.MUSHROOM_BURGER = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.MUSHROOM_MALODY = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.MUSHROOM_MEDLEY = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.MUSHROOM_STEW = nil
-- Salads (x4)
STRINGS.CHARACTERS.WEBBER.DESCRIBE.SALAD_CARROT = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.SALAD_GRAPE = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.SALAD_WALDORF = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.SALAD_YAMION = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.SALAD_BLOOM = nil
-- Soups (x5)
STRINGS.CHARACTERS.WEBBER.DESCRIBE.CACTUS_SOUP = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.CHOWDER = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.GAZPACHO = nil -- Cold - requires Ice
STRINGS.CHARACTERS.WEBBER.DESCRIBE.GUMBO = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.SQUASH = nil
-- Snacks (x7 +)
STRINGS.CHARACTERS.WEBBER.DESCRIBE.ALOO_TIKI = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.LIQUOR = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.CHEESE_LOG = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.GRUEL = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.NUT_BUTTER = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.PORRIDGE = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.YAMION_RINGS = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.TWICE_BAKED = nil
-- Liquors (x5)
STRINGS.CHARACTERS.WEBBER.DESCRIBE.LIQUOR = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.DAIQUIRI = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.MARGARITA = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.PINA_COLADA = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.VINO = nil
-- Cookables (x4)
STRINGS.CHARACTERS.WEBBER.DESCRIBE.CASSEROLE = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.FRUIT_SYRUP = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.MOLASSES = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.OLEO = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.COCONUT_MILK = nil
-- Crops & Seeds
STRINGS.CHARACTERS.WEBBER.DESCRIBE.GRAPRICOT = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.GRAPRICOT_COOKED = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.GRAPRICOT_DRIED = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.GRAPRICOT_SEEDS = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.LIMON = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.LIMON_COOKED = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.LIMON_SEEDS = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.TOMANGO = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.TOMANGO_DRIED = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.TOMANGO_COOKED = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.TOMANGO_SEEDS = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.YAMION = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.YAMION_COOKED = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.YAMION_SEEDS = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.GARLEEK = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.GARLEEK_COOKED = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.TENTACLE_MEAT = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.TENTACLE_MEAT_COOKED = nil
-- Shipwrecked Crops
STRINGS.CHARACTERS.WEBBER.DESCRIBE.PAPAYANAPPLE = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.PAPAYANAPPLE_COOKED = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.ZUCCHILLI = nil
STRINGS.CHARACTERS.WEBBER.DESCRIBE.ZUCCHILLI_COOKED = nil
