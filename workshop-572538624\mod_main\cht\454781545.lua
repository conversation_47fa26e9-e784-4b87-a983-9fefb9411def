STRINGS.NAMES.SANJIHAT = "香菸"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SANJIHAT = "不健康但可以放鬆一下。"
_G.ChinesePlus.RenameRecipe("SANJIHAT","標準的香菸。")

-- The character select screen lines
STRINGS.CHARACTER_TITLES.sanji = "廚師長"
STRINGS.CHARACTER_NAMES.sanji = "山治"
STRINGS.CHARACTER_DESCRIPTIONS.sanji = "*出色的廚師\n*喜歡女人\n*支柱"
STRINGS.CHARACTER_QUOTES.sanji = "\"我需要女士！！！！\""

-- Custom speech strings
--STRINGS.CHARACTERS.SANJI = require(chinesefolder.."/Sanji/speech_sanji")
STRINGS.CHARACTERS.SANJI = nil

-- The character's name as appears in-game
STRINGS.NAMES.SANJI = "山治"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SANJI =
{
  GENERIC = "這是山治！",
  ATTACKER = "山治看起來很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "山治, 鬼魂朋友！",
  GHOST = "山治可以使用一顆心。",
}
