local function MakeMarker(name)
    local assets = {}
    local function fn()
        local inst = CreateEntity()
        inst.entity:AddTransform()
        inst.entity:AddNetwork()

        inst:AddTag("FX")
        inst.persists = false

        if not TheWorld.ismastersim then
            return inst
        end

        inst:DoTaskInTime(60, inst.Remove)
        return inst
    end
    return Prefab(name, fn, assets)
end

return MakeMarker("fx_map_marker")
