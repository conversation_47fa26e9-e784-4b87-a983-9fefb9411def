STRINGS.CHARACTER_NAMES.warly = "沃利"
STRINGS.CHARACTER_DESCRIPTIONS.warly = "*杰出的味道\n*用定制的炊具做饭\n*有一个时尚厨师口袋"
STRINGS.CHARACTER_QUOTES.warly = "\"祝你有个好胃口！\""
STRINGS.CHARACTER_TITLES.warly = "大厨"

--STRINGS.CHARACTERS.WARLY = require(chinesefolder.."/Warly/speech_warly")
STRINGS.CHARACTERS.WARLY = nil

STRINGS.NAMES.WARLY = "沃利"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.WARLY =
{
  GENERIC = "这是沃利",
  ATTACKER = "沃利看起来很狡猾...",
  MURDERER = "沃利！",
  REVIVER = "沃利, 鬼魂朋友！",
  GHOST = "沃利可以使用一颗心。",
}

STRINGS.NAMES.PORTABLECOOKPOT = "便携式锅"
_G.ChinesePlus.RenameRecipe("PORTABLECOOKPOT","沃利的便携式锅")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.PORTABLECOOKPOT = "我们要进行什么新的烹饪冒险，老朋友。"

STRINGS.NAMES.PORTABLECOOKPOT_ITEM = "便携式锅"
_G.ChinesePlus.RenameRecipe("PORTABLECOOKPOT_ITEM","沃利的便携式锅")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.PORTABLECOOKPOT_ITEM = "我们要进行什么新的烹饪冒险，老朋友。"

STRINGS.NAMES.SPICEPACK = "厨师袋"
_G.ChinesePlus.RenameRecipe("SPICEPACK","沃利的厨师袋...")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SPICEPACK = "里面有一包厨师技巧！"

STRINGS.NAMES.SWEETPOTATOSOUFFLE = "甘薯蛋奶酥"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SWEETPOTATOSOUFFLE = "甘薯蛋奶酥"

STRINGS.NAMES.MONSTERTARTARE = "怪物鞑靼"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MONSTERTARTARE = "怪物鞑靼"

STRINGS.NAMES.FRESHFRUITCREPES = "水果薄饼"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.FRESHFRUITCREPES = "水果薄饼"

STRINGS.NAMES.MUSSELBOUILLABAISE = "贻贝浓汤"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MUSSELBOUILLABAISE = "贻贝浓汤"

