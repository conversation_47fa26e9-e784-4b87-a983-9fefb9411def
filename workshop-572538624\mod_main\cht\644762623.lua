--STRINGS.CHARACTERS.Miku = require(chinesefolder.."/Miku/speech_miku")
STRINGS.CHARACTERS.Miku = nil

STRINGS.NAMES.LEEK = "初音的韭菜"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.LEEK = "我問這韭菜怎麼長這麼大；—；"

STRINGS.NAMES.CANDY = "糖果^^"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CANDY = "這是一個糖果？"

-- The character select screen lines
STRINGS.CHARACTER_TITLES.miku = "說謊的女孩"
STRINGS.CHARACTER_NAMES.miku = "初音"
STRINGS.CHARACTER_DESCRIPTIONS.miku = "*寒冷免疫\n*自己就是個冰箱\n*有個韭菜"
STRINGS.CHARACTER_QUOTES.miku = "\" 一個螺絲…或螺栓？所以沒關係！\""

_G.ChinesePlus.RenameRecipe("CANDY","也許我能做點糖果？我喜歡糖果 ^^")

STRINGS.NAMES.MIKU = "初音"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.MIKU =
{
  GENERIC = "它存在嗎？",
  ATTACKER = "初音看起來很狡猾...",
  MURDERER = "嗯..",
  REVIVER = "她似乎很好。",
  GHOST = "我最好不要問你是怎麼死的。",
}

