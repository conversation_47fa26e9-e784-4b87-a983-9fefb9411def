STRINGS.NAMES.ZOROSWORDMOUTH = "和道一文字"
STRINGS.NAMES.SANDAI = "三代鬼彻"
STRINGS.NAMES.SHUSUI = "秋水"
STRINGS.NAMES.ZOROSHEATH = "索隆的剑鞘"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.ZOROSWORDMOUTH= "一把剑在剑客有很多的情感价值。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SHUSUI= "只有传说中的人可以持有这样的剑。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SANDAI= "只有少数人战胜诅咒生活了下去。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.ZOROSHEATH= "它拥有三种特殊的剑，每一个都有自己的特点。"
-- The character select screen lines
STRINGS.CHARACTER_TITLES.zoro = "世界上最伟大的剑客"
STRINGS.CHARACTER_NAMES.zoro = "索隆"
STRINGS.CHARACTER_DESCRIPTIONS.zoro = "*强大的攻击\n*剑有多种用途\n*没有方向感"
STRINGS.CHARACTER_QUOTES.zoro = "\"背上的伤痕是剑士的耻辱\""

-- Custom speech strings
--STRINGS.CHARACTERS.ZORO = require(chinesefolder.."/Zoro/speech_zoro")
STRINGS.CHARACTERS.ZORO = nil

-- The character's name as appears in-game
STRINGS.NAMES.ZORO = "索隆"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.ZORO =
{
  GENERIC = "这是索隆！",
  ATTACKER = "索隆看起来很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "索隆, 鬼魂朋友！",
  GHOST = "索隆可以使用一颗心。",
}

