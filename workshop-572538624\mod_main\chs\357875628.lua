if STRINGS and STRINGS.CHARACTERS ~= nil then
  for k,v in pairs(STRINGS.CHARACTERS) do
    v.DESCRIBE.MECH_HAY_ITEM = nil
    v.DESCRIBE.MECH_HAY = nil
    v.DESCRIBE.MECH_WOOD_ITEM = nil
    v.DESCRIBE.MECH_WOOD = nil
    v.DESCRIBE.MECH_STONE_ITEM = nil
    v.DESCRIBE.MECH_STONE = nil
    v.DESCRIBE.MECH_RUINS_ITEM = nil
    v.DESCRIBE.MECH_RUINS = nil
    v.DESCRIBE.MECH_MOONROCK_ITEM = nil
    v.DESCRIBE.MECH_MOONROCK = nil
    v.DESCRIBE.LOCKED_MECH_STONE_ITEM = nil
    v.DESCRIBE.LOCKED_MECH_STONE = nil
    v.DESCRIBE.LOCKED_MECH_RUINS_ITEM = nil
    v.DESCRIBE.LOCKED_MECH_RUINS = nil
    v.DESCRIBE.LOCKED_MECH_MOONROCK_ITEM = nil
    v.DESCRIBE.LOCKED_MECH_MOONROCK = nil
  end
end

STRINGS.NAMES.MECH_HAY_ITEM = "干草门"
_G.ChinesePlus.RenameRecipe("MECH_HAY_ITEM", "干草做的门.")

STRINGS.NAMES.MECH_WOOD_ITEM = "木门"
_G.ChinesePlus.RenameRecipe("MECH_WOOD_ITEM", "木头做的门.")

STRINGS.NAMES.MECH_STONE_ITEM = "石门"
_G.ChinesePlus.RenameRecipe("MECH_STONE_ITEM", "石头做的门.")

STRINGS.NAMES.MECH_RUINS_ITEM = "铥矿门"
_G.ChinesePlus.RenameRecipe("MECH_RUINS_ITEM", "铥矿做的门.")

STRINGS.NAMES.MECH_MOONROCK_ITEM = "月岩门"
_G.ChinesePlus.RenameRecipe("MECH_MOONROCK_ITEM", "月岩做的门.")

STRINGS.NAMES.MECH_HAY = "干草门"
STRINGS.NAMES.MECH_WOOD = "木门"
STRINGS.NAMES.MECH_STONE = "石门"
STRINGS.NAMES.MECH_RUINS = "铥矿门"
STRINGS.NAMES.MECH_MOONROCK = "月岩门"

STRINGS.NAMES.LOCKED_MECH_STONE_ITEM = "上锁的石门"
_G.ChinesePlus.RenameRecipe("LOCKED_MECH_STONE_ITEM", "上锁的石头做的门")

STRINGS.NAMES.LOCKED_MECH_RUINS_ITEM = "上锁的铥矿门"
_G.ChinesePlus.RenameRecipe("LOCKED_MECH_RUINS_ITEM", "上锁的铥矿做的门")

STRINGS.NAMES.LOCKED_MECH_MOONROCK_ITEM = "上锁的月岩门"
_G.ChinesePlus.RenameRecipe("LOCKED_MECH_MOONROCK_ITEM", "上锁的月岩做的门")

STRINGS.NAMES.LOCKED_MECH_STONE = "上锁的石门"
STRINGS.NAMES.LOCKED_MECH_RUINS = "上锁的铥矿门"
STRINGS.NAMES.LOCKED_MECH_MOONROCK = "上锁的月岩门"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.MECH_HAY_ITEM = "手工仔细编制."
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MECH_HAY = "看起来还是不怎么牢靠."
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MECH_WOOD_ITEM = "绳索加木板!"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MECH_WOOD = "移动的板子!"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MECH_STONE_ITEM = "这能让我快进快出."
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MECH_STONE = "不是很隐蔽."
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MECH_RUINS_ITEM = "比看起来结实."
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MECH_RUINS = "不知道还有什么改进的余地."
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MECH_MOONROCK_ITEM = "不可思议. 不是很重的岩石."
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MECH_MOONROCK = "如果这都不能阻挡它们那就没什么可以了."
STRINGS.CHARACTERS.GENERIC.DESCRIBE.LOCKED_MECH_STONE_ITEM = "这能让我快进快出"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.LOCKED_MECH_STONE = "不是很隐蔽."
STRINGS.CHARACTERS.GENERIC.DESCRIBE.LOCKED_MECH_RUINS_ITEM = "比看起来结实."
STRINGS.CHARACTERS.GENERIC.DESCRIBE.LOCKED_MECH_RUINS = "不知道还有什么改进的余地."
STRINGS.CHARACTERS.GENERIC.DESCRIBE.LOCKED_MECH_MOONROCK_ITEM = "不可思议. 不是很重的岩石."
STRINGS.CHARACTERS.GENERIC.DESCRIBE.LOCKED_MECH_MOONROCK = "不可思议. 不是很重的岩石."

_G.ChinesePlus.RenameAction("CLOSE","关门")
_G.ChinesePlus.RenameAction("OPEN","开门")
_G.ChinesePlus.RenameAction("UNLOCK","开锁")
_G.ChinesePlus.RenameAction("LOCK","上锁")
