STRINGS.CHARACTERS.BUNNYP = require(chinesefolder.."/PlayablePets/speech_bunnyp")

-- The character select screen lines
----------------------------------------------------------
STRINGS.CHARACTER_TITLES.shadow2player = "尖喙恐懼化身"
STRINGS.CHARACTER_NAMES.shadow2player = "尖喙恐懼化身"
STRINGS.CHARACTER_DESCRIPTIONS.shadow2player = "*對正常人和怪物是看不見的。\n* 總是看著你..."
STRINGS.CHARACTER_QUOTES.shadow2player = "它們"

STRINGS.CHARACTER_TITLES.wormp = "深淵蠕蟲"
STRINGS.CHARACTER_NAMES.wormp = "深淵蠕蟲"
STRINGS.CHARACTER_DESCRIPTIONS.wormp = "*挖掘地下。\n* 有一種誘惑。"
STRINGS.CHARACTER_QUOTES.wormp = "*隆 隆*"

STRINGS.CHARACTER_TITLES.batp = "黑蝙蝠"
STRINGS.CHARACTER_NAMES.batp = "黑蝙蝠"
STRINGS.CHARACTER_DESCRIPTIONS.batp = "*飛行！\n* 拉屎。 \n* 不是很強..."
STRINGS.CHARACTER_QUOTES.batp = "會飛的老鼠"

STRINGS.CHARACTER_TITLES.cspiderp = "噴吐蟲"
STRINGS.CHARACTER_NAMES.cspiderp = "噴吐蟲"
STRINGS.CHARACTER_DESCRIPTIONS.cspiderp = "*吐唾沫。 \n*是一隻蜘蛛。 \n*比一般的蜘蛛更強一點。"
STRINGS.CHARACTER_QUOTES.cspiderp = "呸~呸~呸~"

STRINGS.CHARACTER_TITLES.cspider2p = "洞穴蜘蛛"
STRINGS.CHARACTER_NAMES.cspider2p = "洞穴蜘蛛"
STRINGS.CHARACTER_DESCRIPTIONS.cspider2p = "*可以隱藏。 \n*掉落石頭。 \n*一隻龐大的蜘蛛.."
STRINGS.CHARACTER_QUOTES.cspider2p = "仍然討厭捉迷藏"

STRINGS.CHARACTER_TITLES.monkeyp = "暴躁猴"
STRINGS.CHARACTER_NAMES.monkeyp = "暴躁猴"
STRINGS.CHARACTER_DESCRIPTIONS.monkeyp = "*便便武器 \n* 有手。 \n*只吃蔬菜 "
STRINGS.CHARACTER_QUOTES.monkeyp = "我的猴子的便桶在哪裡？"

STRINGS.CHARACTER_TITLES.slurperp = "綴食者"
STRINGS.CHARACTER_NAMES.slurperp = "綴食者"
STRINGS.CHARACTER_DESCRIPTIONS.slurperp = "*是個貪吃的人。 \n* 給出光亮。 \n*便便燈泡。"
STRINGS.CHARACTER_QUOTES.slurperp = "吃"

STRINGS.CHARACTER_TITLES.bunnyp = "兔男"
STRINGS.CHARACTER_NAMES.bunnyp = "兔男"
STRINGS.CHARACTER_DESCRIPTIONS.bunnyp = "*是素食主義者。 \n*在黑暗中能看見。 \n*有一個可怕的祕密。"
STRINGS.CHARACTER_QUOTES.bunnyp = "吃肉就是謀殺！"

STRINGS.CHARACTER_TITLES.rocklobsterp = "岩石大龍蝦"
STRINGS.CHARACTER_NAMES.rocklobsterp = "岩石大龍蝦"
STRINGS.CHARACTER_DESCRIPTIONS.rocklobsterp = "*超笨重和緩慢。 \n* 可以隱藏。 \n*吃石頭。"
STRINGS.CHARACTER_QUOTES.rocklobsterp = "..."

STRINGS.CHARACTER_TITLES.guardianp = "遠古守護者"
STRINGS.CHARACTER_NAMES.guardianp = "遠古守護者"
STRINGS.CHARACTER_DESCRIPTIONS.guardianp = "*大BOSS \n* 極具破壞性。 \n*很好的防護。"
STRINGS.CHARACTER_QUOTES.guardianp = "..."

-- The character's name as appears in-game
STRINGS.NAMES.MONKEYBARREL_P = "暴躁猴群"
STRINGS.NAMES.MONKEYHOUSE = "暴躁猴群"

STRINGS.NAMES.RABBITHOUSE_PLAYER = "兔子窩"
STRINGS.NAMES.CMONSTER_WPN = "神祕的力量"
-- The default responses of examining the prefab

STRINGS.CHARACTERS.GENERIC.DESCRIBE.RABBITHOUSE_PLAYER =
{
  GENERIC = "聞起來像胡蘿蔔。",
  BURNT = "聞起來像燒焦的胡蘿蔔...和兔子。",
}

STRINGS.CHARACTERS.GENERIC.DESCRIBE.CMONSTER_WPN =
{
  GENERIC = "我不應該有這個...",
}

