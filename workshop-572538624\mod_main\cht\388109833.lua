STRINGS.NAMES.ACEHAT = "艾斯的帽子"
STRINGS.NAMES.ACEFIRE = "艾斯的火球"

-- The character select screen lines
STRINGS.CHARACTER_TITLES.ace = "第二師司令"
STRINGS.CHARACTER_NAMES.ace = "艾斯"
STRINGS.CHARACTER_DESCRIPTIONS.ace = "*火焰的輝光\n*火焰免疫\n*燃燒一切"
STRINGS.CHARACTER_QUOTES.ace = "\"我們必須過一種沒有遺憾的生活。\""

-- Custom speech strings
--STRINGS.CHARACTERS.ACE = require(chinesefolder.."/Ace/speech_ace")
STRINGS.CHARACTERS.ACE = nil

-- The character's name as appears in-game
STRINGS.NAMES.ACE = "艾斯"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.ACE =
{
  GENERIC = "這是艾斯！",
  ATTACKER = "艾斯看起來很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "艾斯, 鬼魂朋友！",
  GHOST = "艾斯可以使用一顆心。",
}

