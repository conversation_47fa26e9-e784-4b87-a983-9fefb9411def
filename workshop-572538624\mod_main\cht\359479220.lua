-- The character select screen lines
STRINGS.CHARACTER_TITLES.sollyz = "貓的巴掌"
STRINGS.CHARACTER_NAMES.sollyz = "Sollyz"
STRINGS.CHARACTER_DESCRIPTIONS.sollyz = "*吃魚升級 (最大30級)\n*有釣魚杆。\n*早晨會跑得快！\n"
STRINGS.CHARACTER_QUOTES.sollyz = "\"我愛 Haruz 就像我愛那些魚一樣！\""

-- Custom speech strings
--STRINGS.CHARACTERS.sollyz = require(chinesefolder.."/Sollyz/speech_sollyz")
STRINGS.CHARACTERS.sollyz = nil

-- The character's name as appears in-game
STRINGS.NAMES.sollyz = "Sollyz"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.sollyz =
{
  GENERIC = "這是 Sollyz！",
  ATTACKER = "Sollyz 看起來很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "Sollyz, 鬼魂朋友！",
  GHOST = "Sollyz 可以使用一顆心。",
}

