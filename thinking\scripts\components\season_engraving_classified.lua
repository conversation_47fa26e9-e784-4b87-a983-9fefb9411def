-- 季节刻印Classified组件
-- 根据DST官方规范实现的网络同步分类实体组件
-- 用于在服务端和客户端之间同步季节刻印数据

local _G = GLOBAL
local Class = _G.Class
local net_string = _G.net_string

local SeasonEngravingClassified = Class(function(self, inst)
    self.inst = inst
    
    -- 验证这是一个classified实体
    if inst.prefab ~= "season_engraving_classified" then
        print("[SeasonWorkshop] Warning: Season engraving classified component on non-classified entity")
    end
    
    -- 网络变量（在classified实体上创建）
    self._net_current = net_string(inst.GUID, "season_engraving.current", "season_current_dirty")
    self._net_manual = net_string(inst.GUID, "season_engraving.manual", "season_manual_dirty")
    
    -- 服务端数据
    self._current_season = ""
    self._manual_season = ""
    
    -- 关联的玩家实体
    self._player = nil
    
    -- 初始化网络变量
    if _G.TheWorld.ismastersim then
        self._net_current:set("")
        self._net_manual:set("")
    else
        -- 客户端：设置replica的网络变量引用
        self:SetupReplica()
    end
end)

-- 设置关联的玩家实体
function SeasonEngravingClassified:SetPlayer(player)
    self._player = player
    
    -- 如果玩家有replica组件，设置网络变量引用
    if player and player.replica and player.replica.season_engraving then
        player.replica.season_engraving:SetNetworkVariables(self._net_current, self._net_manual)
    end
end

-- 客户端：设置replica
function SeasonEngravingClassified:SetupReplica()
    if _G.TheWorld.ismastersim then return end
    
    -- 等待玩家实体和replica组件准备就绪
    self.inst:DoTaskInTime(0, function()
        if self._player and self._player.replica and self._player.replica.season_engraving then
            self._player.replica.season_engraving:SetNetworkVariables(self._net_current, self._net_manual)
        end
    end)
end

-- 服务端：设置当前季节
function SeasonEngravingClassified:SetCurrentSeason(season)
    if not _G.TheWorld.ismastersim then return end
    
    if not season or season == "" then
        season = ""
    end
    
    if season ~= self._current_season then
        self._current_season = season
        self._net_current:set(season)
        
        print(string.format("[SeasonWorkshop] Classified: Current season set to '%s'", season))
    end
end

-- 服务端：设置手动季节
function SeasonEngravingClassified:SetManualSeason(season)
    if not _G.TheWorld.ismastersim then return end
    
    if not season or season == "" then
        season = ""
    end
    
    if season ~= self._manual_season then
        self._manual_season = season
        self._net_manual:set(season)
        
        print(string.format("[SeasonWorkshop] Classified: Manual season set to '%s'", season))
    end
end

-- 服务端：清除手动季节
function SeasonEngravingClassified:ClearManualSeason()
    if not _G.TheWorld.ismastersim then return end
    
    self:SetManualSeason("")
end

-- 获取当前季节（服务端）
function SeasonEngravingClassified:GetCurrentSeason()
    return self._current_season
end

-- 获取手动季节（服务端）
function SeasonEngravingClassified:GetManualSeason()
    return self._manual_season
end

-- 是否为手动设置（服务端）
function SeasonEngravingClassified:IsManual()
    return self._manual_season and self._manual_season ~= ""
end

-- 获取有效季节（服务端）
function SeasonEngravingClassified:GetEffectiveSeason()
    return self:IsManual() and self._manual_season or self._current_season
end

-- 同步数据到客户端
function SeasonEngravingClassified:SyncToClient()
    if not _G.TheWorld.ismastersim then return end
    
    -- 强制同步当前数据
    self._net_current:set(self._current_season)
    self._net_manual:set(self._manual_season)
end

-- 数据持久化
function SeasonEngravingClassified:OnSave()
    if not _G.TheWorld.ismastersim then return end
    
    return {
        current_season = self._current_season,
        manual_season = self._manual_season
    }
end

function SeasonEngravingClassified:OnLoad(data)
    if not _G.TheWorld.ismastersim then return end
    
    if data then
        if data.current_season then
            self:SetCurrentSeason(data.current_season)
        end
        
        if data.manual_season then
            self:SetManualSeason(data.manual_season)
        end
    end
end

-- 组件清理
function SeasonEngravingClassified:OnRemoveFromEntity()
    -- 清理玩家引用
    self._player = nil
    
    -- 清理网络变量引用
    self._net_current = nil
    self._net_manual = nil
    
    print("[SeasonWorkshop] Season engraving classified cleaned up")
end

-- 调试信息
function SeasonEngravingClassified:GetDebugString()
    return string.format("Classified - Current: %s, Manual: %s, Player: %s", 
        self._current_season, 
        self._manual_season or "none",
        self._player and self._player.prefab or "none")
end

-- 验证网络同步状态
function SeasonEngravingClassified:ValidateNetworkSync()
    local issues = {}
    
    if not self._net_current then
        table.insert(issues, "Missing current season network variable")
    end
    
    if not self._net_manual then
        table.insert(issues, "Missing manual season network variable")
    end
    
    if _G.TheWorld.ismastersim then
        -- 服务端验证
        if self._net_current and self._net_current:value() ~= self._current_season then
            table.insert(issues, "Current season network variable out of sync")
        end
        
        if self._net_manual and self._net_manual:value() ~= self._manual_season then
            table.insert(issues, "Manual season network variable out of sync")
        end
    end
    
    return #issues == 0, issues
end

return SeasonEngravingClassified
