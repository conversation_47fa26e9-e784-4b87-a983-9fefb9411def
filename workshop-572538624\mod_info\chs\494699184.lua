_G.ChinesePlus.SetConfigTranslation(mod_to_c,
  "允许调整游戏的配置。",
  {
    ["Enabled"] = "开",
    ["Disabled"] = "关",
    ["Default"] = "默认",
    ["Infinite"] = "无限",
    [""] = "",
    [""] = "",
    [""] = "",
  },
  {
    -- Giants Configuration
    {
      name = "ModifyBeargerHP",
      label = "比尔格 HP",
      hover = "修改比尔格的HP",
    },
    {
      name = "ModifyBeargerDMG",
      label = "比尔格 伤害",
      hover = "修改比尔格的伤害",
    },
    {
      name = "ModifyDragonflyHP",
      label = "蜻蜓 HP",
      hover = "修改蜻蜓的HP",
    },
    {
      name = "ModifyDragonflyDMG",
      label = "蜻蜓 伤害",
      hover = "修改蜻蜓的伤害",
    },
    {
      name = "ModifyDeerclopsHP",
      label = "独眼巨鹿 HP",
      hover = "修改独眼巨鹿的HP",
    },
    {
      name = "ModifyDeerclopsDMG",
      label = "独眼巨鹿 伤害",
      hover = "修改独眼巨鹿的伤害",
    },
    {
      name = "ModifyMooseHP",
      label = "鹿角鹅 HP",
      hover = "修改鹿角鹅的HP",
    },
    {
      name = "ModifyMooseDMG",
      label = "鹿角鹅 伤害",
      hover = "修改鹿角鹅的伤害",
    },
    {
      name = "ModifyAncientGuardianHP",
      label = "远古守护者 HP",
      hover = "修改远古守护者的HP",
    },
    {
      name = "ModifyAncientGuardianDMG",
      label = "远古守护者 伤害",
      hover = "远古守护者的伤害",
    },
    -- Monsters Configuration
    {
      name = "SmallBirdGrowTime",
      label = "小鸟 生长时间",
      hover = "修改小鸟的生长时间",
    },
    {
      name = "TeenBirdGrowTime",
      label = "青年鸟 生长时间",
      hover = "修改青年鸟的生长时间",
    },
    {
      name = "TreeGuardHP",
      label = "树精守卫 HP",
      hover = "修改HP",
    },
    {
      name = "SpiderQueenHP",
      label = "蜘蛛女王 HP",
      hover = "修改HP",
    },
    {
      name = "WargHP",
      label = "座狼 HP",
      hover = "修改HP",
    },
    {
      name = "EwecusHP",
      label = "钢羊 HP",
      hover = "修改HP",
    },
    {
      name = "PigHP",
      label = "猪 HP",
      hover = "修改HP",
    },
    {
      name = "MermHP",
      label = "鱼人 HP",
      hover = "修改HP",
    },
    {
      name = "WalrusHP",
      label = "海象 HP",
      hover = "修改HP",
    },
    {
      name = "LittleWalrusHP",
      label = "小海象 HP",
      hover = "修改HP",
    },
    {
      name = "PenguinHP",
      label = "企鹅 HP",
      hover = "修改HP",
    },
    {
      name = "KnightHP",
      label = "发条骑士 HP",
      hover = "修改HP",
    },
    {
      name = "BishopHP",
      label = "发条主教 HP",
      hover = "修改HP",
    },
    {
      name = "RookHP",
      label = "机械战车 HP",
      hover = "修改HP",
    },
    {
      name = "SlurtleHP",
      label = "含糊虫 HP",
      hover = "修改HP",
    },
    {
      name = "SnurtleHP",
      label = "黏糊虫 HP",
      hover = "修改HP",
    },
    {
      name = "BuzzardHP",
      label = "秃鹰 HP",
      hover = "修改HP",
    },
    {
      name = "GhostHP",
      label = "鬼魂 HP",
      hover = "修改HP",
    },
    {
      name = "BeefaloHP",
      label = "皮弗娄牛 HP",
      hover = "修改HP",
    },
    {
      name = "BatHP",
      label = "蝙蝠 HP",
      hover = "修改HP",
    },
    {
      name = "SpiderHP",
      label = "蜘蛛 HP",
      hover = "修改HP",
    },
    {
      name = "SpiderWarriorHP",
      label = "蜘蛛战士 HP",
      hover = "修改HP",
    },
    {
      name = "CaveSpiderHP",
      label = "洞穴蜘蛛 HP",
      hover = "修改HP",
    },
    {
      name = "CaveSpiderSpitterHP",
      label = "喷吐虫 HP",
      hover = "修改HP",
    },
    {
      name = "BeeHP",
      label = "蜜蜂 HP",
      hover = "修改HP",
    },
    {
      name = "WormHP",
      label = "蠕虫 HP",
      hover = "修改HP",
    },
    {
      name = "TentacleHP",
      label = "触手 HP",
      hover = "修改HP",
    },
    {
      name = "FrogHP",
      label = "青蛙 HP",
      hover = "修改HP",
    },
    {
      name = "HoundsHP",
      label = "猎犬 HP",
      hover = "修改HP",
    },
    {
      name = "KrampusHP",
      label = "坎普斯 HP",
      hover = "修改HP",
    },
    {
      name = "TerrorbeakHP",
      label = "尖喙恐惧化身 HP",
      hover = "修改HP",
    },
    {
      name = "CrawlingHorrorHP",
      label = "爬行恐惧化身 HP",
      hover = "修改HP",
    },
    {
      name = "BunnymanHP",
      label = "兔人 HP",
      hover = "修改HP",
    },
    {
      name = "RockLobsterHP",
      label = "岩石大龙虾 HP",
      hover = "修改HP",
    },
    {
      name = "MonkeyHP",
      label = "猴子 HP",
      hover = "修改HP",
    },
    {
      name = "MosslingHP",
      label = "莫斯林 HP",
      hover = "修改HP",
    },
    -- 护甲Configuration
    {
      name = "GrassArmorDurability",
      label = "草 护甲耐久",
      hover = "修改护甲耐久",
    },
    {
      name = "GrassArmorAbsorption",
      label = "草 护甲伤害吸收",
      hover = "修改护甲伤害吸收",
    },
    {
      name = "LogArmorDurability",
      label = "木 护甲耐久",
      hover = "修改护甲耐久",
    },
    {
      name = "LogArmorAbsorption",
      label = "木 护甲伤害吸收",
      hover = "修改护甲伤害吸收",
    },
    {
      name = "MarbleArmorDurability",
      label = "大理石 护甲耐久",
      hover = "修改护甲耐久",
    },
    {
      name = "MarbleArmorAbsorption",
      label = "大理石 护甲伤害吸收",
      hover = "修改护甲伤害吸收",
    },
    {
      name = "MarbleArmorSlowdown",
      label = "大理石 护甲移动速度",
      hover = "修改护甲移动速度降低",
    },
    {
      name = "SnurtleShellArmorDurability",
      label = "黏糊虫壳甲耐久",
      hover = "修改护甲耐久",
    },
    {
      name = "SnurtleShellArmorAbsorption",
      label = "黏糊虫壳甲伤害吸收",
      hover = "修改护甲伤害吸收",
    },
    {
      name = "ThuleciteSuitArmorDurability",
      label = "铥矿甲胄耐久",
      hover = "修改护甲耐久",
    },
    {
      name = "ThuleciteSuitArmorAbsorption",
      label = "铥矿甲胄伤害吸收",
      hover = "修改护甲伤害吸收",
    },
    {
      name = "ThuleciteCrownArmorDurability",
      label = "铥矿石皇冠耐久",
      hover = "修改护甲耐久",
    },
    {
      name = "ThuleciteCrownArmorAbsorption",
      label = "铥矿石皇冠伤害吸收",
      hover = "修改护甲伤害吸收",
    },
    {
      name = "ThuleciteCrownProcChance",
      label = "铥矿石皇冠力场触发几率",
      hover = "修改力场触发几率",
    },
    {
      name = "ThuleciteCrownForcefieldCooldown",
      label = "铥矿石皇冠力场冷却",
      hover = "修改力场冷却",
    },
    {
      name = "ThuleciteCrownForcefieldDuration",
      label = "铥矿石皇冠力场时间",
      hover = "修改力场时间",
    },
    {
      name = "ThuleciteCrownForcefieldSanityDmgReduction",
      label = "铥矿石皇冠力场精神伤害减少",
      hover = "修改力场精神伤害减少",
    },
    {
      name = "FootballHatArmorDurability",
      label = "猪皮足球头盔耐久",
      hover = "修改护甲耐久",
    },
    {
      name = "FootballHatArmorAbsorption",
      label = "猪皮足球头盔伤害吸收",
      hover = "修改护甲伤害吸收",
    },
    {
      name = "ScalemailArmorDurability",
      label = "鳞甲耐久",
      hover = "修改护甲耐久",
    },
    {
      name = "ScalemailArmorAbsorption",
      label = "鳞甲伤害吸收",
      hover = "修改护甲伤害吸收",
    },
    {
      name = "BattleHelmArmorDurability",
      label = "战斗头盔耐久",
      hover = "修改护甲耐久",
    },
    {
      name = "BattleHelmArmorAbsorption",
      label = "战斗头盔伤害吸收",
      hover = "修改护甲伤害吸收",
    },
    {
      name = "ShelmetArmorDurability",
      label = "背壳头盔耐久",
      hover = "修改护甲耐久",
    },
    {
      name = "ShelmetArmorAbsorption",
      label = "背壳头盔伤害吸收",
      hover = "修改护甲伤害吸收",
    },
    {
      name = "BeeHatArmorDurability",
      label = "养蜂人的帽子耐久",
      hover = "修改护甲耐久",
    },
    {
      name = "BeeHatArmorAbsorption",
      label = "养蜂人的帽子伤害吸收",
      hover = "修改护甲伤害吸收",
    },
    {
      name = "NightArmorDurability",
      label = "夜魔盔甲护甲耐久",
      hover = "修改护甲耐久",
    },
    {
      name = "NightArmorAbsorption",
      label = "夜魔盔甲护甲伤害吸收",
      hover = "修改护甲伤害吸收",
    },
    {
      name = "NightArmorSanityDamage",
      label = "夜魔盔甲精神伤害",
      hover = "修改精神伤害",
    },
    -- Clothes Configuration
    {
      name = "StrawHatDurability",
      label = "草帽 耐久",
      hover = "修改耐久度",
    },
    {
      name = "RabbitEarmuffDurability",
      label = "兔毛耳套 耐久",
      hover = "修改耐久度",
    },
    {
      name = "WinterHatDurability",
      label = "寒冬帽 耐久",
      hover = "修改耐久度",
    },
    {
      name = "BeefaloHatDurability",
      label = "牛帽 耐久",
      hover = "修改耐久度",
    },
    {
      name = "PuffyVestDurability",
      label = "寒冬背心 耐久",
      hover = "修改耐久度",
    },
    {
      name = "SummerFrestDurability",
      label = "夏季背心 耐久",
      hover = "修改耐久度",
    },
    {
      name = "FloralShirtDurability",
      label = "花纹衬衫 耐久",
      hover = "修改耐久度",
    },
    {
      name = "DapperVestDurability",
      label = "小巧背心 耐久",
      hover = "修改耐久度",
    },
    {
      name = "BeltOfHungerDurability",
      label = "饥饿腰带 耐久",
      hover = "修改耐久度",
    },
    {
      name = "HibearnationVestDurability",
      label = "熊皮背心 耐久",
      hover = "修改耐久度",
    },
    {
      name = "RainCoatDurability",
      label = "雨衣 耐久",
      hover = "修改耐久度",
    },
    {
      name = "TamOShanterDurability",
      label = "贝雷帽 耐久",
      hover = "修改耐久度",
    },
    {
      name = "FeatherHatDurability",
      label = "羽毛帽 耐久",
      hover = "修改耐久度",
    },
    {
      name = "TopHatDurability",
      label = "高礼帽 耐久",
      hover = "修改耐久度",
    },
    {
      name = "IceHatDurability",
      label = "冰块帽 耐久",
      hover = "修改耐久度",
    },
    {
      name = "RainHatDurability",
      label = "雨帽 耐久",
      hover = "修改耐久度",
    },
    {
      name = "CatCapDurability",
      label = "猫帽 耐久",
      hover = "修改耐久度",
    },
    -- Items Configuration
    {
      name = "PanFluteSleepTime",
      label = "排箫催眠时间",
      hover = "修改排箫催眠时间",
    },
    {
      name = "PanFluteSleepRange",
      label = "排箫催眠范围",
      hover = "修改排箫催眠范围",
    },
    {
      name = "PanFluteUses",
      label = "排箫使用次数",
      hover = "修改排箫使用次数",
    },
    {
      name = "MandrakeSleepTime",
      label = "曼德拉草 昏迷时间",
      hover = "修改 曼德拉草 昏迷时间",
    },
    {
      name = "MandrakeSleepRange",
      label = "曼德拉草 昏迷范围",
      hover = "修改 曼德拉草 昏迷范围",
    },
    {
      name = "MandrakeSleepRangeCooked",
      label = "熟曼德拉草 昏迷范围",
      hover = "修改 熟曼德拉草 昏迷范围",
    },

    {
      name = "FishingRodUses",
      label = "钓竿使用次数",
      hover = "修改钓竿使用次数",
    },
    {
      name = "TeleStaffUses",
      label = "传送权杖使用次数",
      hover = "修改传送权杖使用次数",
    },
    {
      name = "RedAmuletUses",
      label = "红色护符使用次数",
      hover = "修改红色护符使用次数",
    },
    {
      name = "RedAmuletHPRestore",
      label = "红色护符 HP 恢复",
      hover = "修改红色护符 HP 恢复数值",
    },
    {
      name = "SewingKitUses",
      label = "缝纫工具包使用次数",
      hover = "修改缝纫工具包使用次数",
    },
    {
      name = "WeatherPainUses",
      label = "旋风使用次数",
      hover = "修改旋风使用次数",
    },
    {
      name = "WeatherPainTornadoDuration",
      label = "龙卷风时间",
      hover = "修改龙卷风持续时间",
    },
    {
      name = "WeatherPainDamage",
      label = "旋风伤害",
      hover = "修改旋风伤害",
    },
    -- Other Values
    {
      name = "PlayerHungerRate",
      label = "饥饿速率",
      hover = "修改玩家饥饿速率",
      options = {
        {description = "最慢", data = 0.33},
        {description = "极慢", data = 0.50},
        {description = "很慢", data = 0.66},
        {description = "慢", data = 0.88},
        {description = "默认", data = 1},
        {description = "快", data = 1.12},
        {description = "很快", data = 1.25},
        {description = "极快", data = 1.50},
        {description = "最快", data = 1.77},
      },

    },
    {
      name = "TentUses",
      label = "帐篷使用次数",
      hover = "修改帐篷使用次数",
    },
    {
      name = "SiestaLeanToUses",
      label = "简易小木棚使用次数",
      hover = "修改简易小木棚使用次数",
    },
    {
      name = "StackSizeLargeItem",
      label = "大物品堆叠数量",
      hover = "修改大物品堆叠数量",
    },
    {
      name = "StackSizeMediumItem",
      label = "中物品堆叠数量",
      hover = "修改中物品堆叠数量",
    },
    {
      name = "StackSizeSmallItem",
      label = "小物品堆叠数量",
      hover = "修改小物品堆叠数量",
    },
    {
      name = "FreezingKillTime",
      label = "寒冷杀人时间",
      hover = "修改寒冷杀死玩家所需的时间",
    },
    {
      name = "StarvationKillTime",
      label = "饥饿杀人时间",
      hover = "修改饥饿杀死玩家所需的时间",
    },
    {
      name = "FridgePerishTime",
      label = "冰箱食物腐烂时间",
      hover = "修改冰箱里面食物腐烂的时间",
    },
    -- Custom Modifications
    {
      name = "DisableHayWallRecipe",
      label = "草墙",
      hover = "启用或禁用配方",
    },
    {
      name = "DisableWoodWallRecipe",
      label = "木墙",
      hover = "启用或禁用配方",
    },
    {
      name = "DisableStoneWallRecipe",
      label = "石墙",
      hover = "启用或禁用配方",
    },
    {
      name = "DisableMoonrockWallRecipe",
      label = "月岩墙",
      hover = "启用或禁用配方",
    },
    {
      name = "DisableRuinsWallRecipe",
      label = "远古城墙",
      hover = "启用或禁用配方",
    },
    {
      name = "ServerPlayerSlots",
      label = "服务器最大玩家数",
      hover = "修改 服务器最大玩家数",
    },
  }
)
