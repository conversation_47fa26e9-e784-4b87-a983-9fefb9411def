_G.ChinesePlus.RenameTab("Turf","地毯")

--TURFED TURFS -yes its fun to say
STRINGS.NAMES.TURF_TEST = "测试用地毯。"

--Carpet
STRINGS.NAMES.TURF_CARPETBLACKFUR = "熊毛地毯"
_G.ChinesePlus.RenameRecipe("TURF_CARPETBLACKFUR", "创建一卷熊毛地毯。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_CARPETBLACKFUR = "从怪物毛皮制作的温暖和舒适地毯。"

STRINGS.NAMES.TURF_CARPETBLUE = "蓝色地毯"
_G.ChinesePlus.RenameRecipe("TURF_CARPETBLUE", "创建一卷蓝色地毯。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_CARPETBLUE = "As blue as you."

STRINGS.NAMES.TURF_CARPETCAMO = "迷彩地毯"
_G.ChinesePlus.RenameRecipe("TURF_CARPETCAMO", "创建一卷迷彩地毯。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_CARPETCAMO = "我敢打赌你看不到这地毯。"

STRINGS.NAMES.TURF_CARPETFUR = "牛毛地毯"
_G.ChinesePlus.RenameRecipe("TURF_CARPETFUR", "创建一卷牛毛地毯。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_CARPETFUR = "温暖，舒服，臭。"

STRINGS.NAMES.TURF_CARPETPINK = "粉红地毯"
_G.ChinesePlus.RenameRecipe("TURF_CARPETPINK", "创建一卷粉红地毯。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_CARPETPINK = "粉红色的地毯？ 仿佛！"

STRINGS.NAMES.TURF_CARPETPURPLE = "紫色地毯"
_G.ChinesePlus.RenameRecipe("TURF_CARPETPURPLE", "创建一卷紫色地毯。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_CARPETPURPLE = "紫色是皇室的颜色，也是该地毯的颜色。"

STRINGS.NAMES.TURF_CARPETRED = "红色地毯"
_G.ChinesePlus.RenameRecipe("TURF_CARPETRED", "创建一卷红色地毯。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_CARPETRED = "他们告诉我把地毯洗的像浆果一样白点会比较好..."

STRINGS.NAMES.TURF_CARPETRED2 = "钻石地毯"
_G.ChinesePlus.RenameRecipe("TURF_CARPETRED2", "创建一卷钻石地毯。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_CARPETRED2 = "像血钻一样光泽亮丽。"

STRINGS.NAMES.TURF_CARPETTD = "扎染地毯"
_G.ChinesePlus.RenameRecipe("TURF_CARPETTD", "创建一卷扎染地毯。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_CARPETTD = "这种地毯很男人，它像...哇..."

STRINGS.NAMES.TURF_CARPETWIFI = "无线地毯"
_G.ChinesePlus.RenameRecipe("TURF_CARPETWIFI", "创建一卷无线地毯。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_CARPETWIFI = "现在你有信号了吗？ 好吧。"
--Nature
STRINGS.NAMES.TURF_NATUREASTROTURF = "天文草坪"
_G.ChinesePlus.RenameRecipe("TURF_NATUREASTROTURF", "创建一卷天文草坪。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_NATUREASTROTURF = "不麻烦的草！"

STRINGS.NAMES.TURF_NATUREDESERT = "沙漠地皮"
_G.ChinesePlus.RenameRecipe("TURF_NATUREDESERT", "创建一些沙漠地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_NATUREDESERT = "你有一片干裂的不毛之地。"
--Rock
STRINGS.NAMES.TURF_ROCKBLACKTOP = "柏油地皮"
_G.ChinesePlus.RenameRecipe("TURF_ROCKBLACKTOP", "创建一些柏油地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_ROCKBLACKTOP = "柏油，无尽，毫无意义。"

STRINGS.NAMES.TURF_ROCKGIRAFFE = "长颈鹿石地皮"
_G.ChinesePlus.RenameRecipe("TURF_ROCKGIRAFFE", "创建一些长颈鹿石地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_ROCKGIRAFFE = "从新鲜的长颈鹿制作。"

STRINGS.NAMES.TURF_ROCKMOON = "月岩地皮"
_G.ChinesePlus.RenameRecipe("TURF_ROCKMOON", "创建一些月岩地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_ROCKMOON = "月岩地皮，呼叫总部。"

STRINGS.NAMES.TURF_ROCKYELLOWBRICK = "黄砖地皮"
_G.ChinesePlus.RenameRecipe("TURF_ROCKYELLOWBRICK", "创建一些黄砖地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_ROCKYELLOWBRICK = "跟着它。"
--Tile
STRINGS.NAMES.TURF_TILECHECKERBOARD = "棋盘瓷砖"
_G.ChinesePlus.RenameRecipe("TURF_TILECHECKERBOARD", "创建一些棋盘瓷砖。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_TILECHECKERBOARD = "将军。"

STRINGS.NAMES.TURF_TILEFROSTY = "雾瓷砖"
_G.ChinesePlus.RenameRecipe("TURF_TILEFROSTY", "创建一些雾瓷砖。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_TILEFROSTY = "你想创造一些瓷砖？"

STRINGS.NAMES.TURF_TILESQUARES = "广场瓷砖"
_G.ChinesePlus.RenameRecipe("TURF_TILESQUARES", "创建一些广场瓷砖。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_TILESQUARES = "这样的格调，更加广场。"
--Wood
STRINGS.NAMES.TURF_WOODCHERRY = "樱花木地板"
_G.ChinesePlus.RenameRecipe("TURF_WOODCHERRY", "创建一些樱花木地板。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_WOODCHERRY = "你在哪里找到的樱桃？"

STRINGS.NAMES.TURF_WOODDARK = "暗木地板"
_G.ChinesePlus.RenameRecipe("TURF_WOODDARK", "创建一些暗木地板。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_WOODDARK = "查理最喜欢的颜色。"

STRINGS.NAMES.TURF_WOODPINE = "松木地板"
_G.ChinesePlus.RenameRecipe("TURF_WOODPINE", "创建一些松木地板。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_WOODPINE = "让松果有用的地板。"

--DST TURFS
STRINGS.NAMES.TURF_FOREST = "森林地皮"
_G.ChinesePlus.RenameRecipe("TURF_FOREST", "创建一块森林地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_FOREST = "一块森林地皮。"

STRINGS.NAMES.TURF_DECIDUOUS = "季节性地皮"
_G.ChinesePlus.RenameRecipe("TURF_DECIDUOUS", "创建一块季节性地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_DECIDUOUS = "一块季节性地皮。"

STRINGS.NAMES.TURF_GRASS = "青草地皮"
_G.ChinesePlus.RenameRecipe("TURF_GRASS", "创建一块青草地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_GRASS = "一块青草地皮。"

STRINGS.NAMES.TURF_SAVANNA = "热带草原地皮"
_G.ChinesePlus.RenameRecipe("TURF_SAVANNA", "创建一块热带草原地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_SAVANNA = "一块热带草原地皮。"

STRINGS.NAMES.TURF_DESERTDIRT = "沙漠地皮"
_G.ChinesePlus.RenameRecipe("TURF_DESERTDIRT", "创建一块沙漠地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_DESERTDIRT = "一块沙漠地皮。"

STRINGS.NAMES.TURF_MARSH = "沼泽地皮"
_G.ChinesePlus.RenameRecipe("TURF_MARSH", "创建一块沼泽地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_MARSH = "一块沼泽地皮。"

STRINGS.NAMES.TURF_FUNGUS = "蓝菌地皮"
_G.ChinesePlus.RenameRecipe("TURF_FUNGUS", "创建一块蓝菌地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_FUNGUS = "一块蓝菌地皮。"

STRINGS.NAMES.TURF_FUNGUS_RED = "红菌地皮"
_G.ChinesePlus.RenameRecipe("TURF_FUNGUS_RED", "创建一块红菌地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_FUNGUS_RED = "一块红菌地皮。"

STRINGS.NAMES.TURF_FUNGUS_GREEN = "绿菌地皮"
_G.ChinesePlus.RenameRecipe("TURF_FUNGUS_GREEN", "创建一块绿菌地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_FUNGUS_GREEN = "一块绿菌地皮。"

STRINGS.NAMES.TURF_MUD = "泥泞地皮"
_G.ChinesePlus.RenameRecipe("TURF_MUD", "创建一块泥泞地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_MUD = "一块泥泞地皮。"

STRINGS.NAMES.TURF_SINKHOLE = "污水地皮"
_G.ChinesePlus.RenameRecipe("TURF_SINKHOLE", "创建一块污水地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_SINKHOLE = "一块污水地皮。"

STRINGS.NAMES.TURF_ROCKY = "岩石地皮"
_G.ChinesePlus.RenameRecipe("TURF_ROCKY", "创建一块岩石地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_ROCKY = "一块岩石地皮。"

STRINGS.NAMES.TURF_CAVE = "鸟粪地皮"
_G.ChinesePlus.RenameRecipe("TURF_CAVE", "创建一块鸟粪地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_CAVE = "一块鸟粪地皮。"

STRINGS.NAMES.TURF_UNDERROCK = "洞穴岩石地皮"
_G.ChinesePlus.RenameRecipe("TURF_UNDERROCK", "创建一块洞穴岩石地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_UNDERROCK = "一块洞穴岩石地皮。"

