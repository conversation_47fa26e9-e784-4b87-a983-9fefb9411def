-- The character select screen lines
STRINGS.CHARACTER_TITLES.tong = "士官長"
STRINGS.CHARACTER_NAMES.tong = "童"
STRINGS.CHARACTER_DESCRIPTIONS.tong = "*跑得快\n*隨時隨地可以做菜\n*擁有寒冰揹包"
STRINGS.CHARACTER_QUOTES.tong = "\"不，我要自己做！\""

-- Custom speech strings
--STRINGS.CHARACTERS.TONG = require(chinesefolder.."/Tong/speech_tong")
STRINGS.CHARACTERS.TONG = nil

-- The character's name as appears in-game
STRINGS.NAMES.TONG = "童"

STRINGS.NAMES.TONG_COOKINGPOT = "烹飪鍋"
STRINGS.NAMES.TONG_COOKINGPOT_ITEM = "烹飪鍋"
_G.ChinesePlus.RenameRecipe("TONG_COOKINGPOT_ITEM", "行動式烹飪鍋")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.PORTABLECOOKPOT_ITEM = "烹飪鍋"

STRINGS.NAMES.TONG_ICEBACKPACK0 = "寒冰揹包"
_G.ChinesePlus.RenameRecipe("TONG_ICEBACKPACK0", "冰冷的揹包")

STRINGS.NAMES.TONG_ICEBACKPACK1 = "寒冰揹包 1000"
_G.ChinesePlus.RenameRecipe("TONG_ICEBACKPACK1", "冰冷的揹包")

STRINGS.NAMES.TONG_ICEBACKPACK2 = "寒冰揹包 2000"
_G.ChinesePlus.RenameRecipe("TONG_ICEBACKPACK2", "冰冷的揹包")

STRINGS.NAMES.TONG_ICEBACKPACK3 = "寒冰揹包 3000"
_G.ChinesePlus.RenameRecipe("TONG_ICEBACKPACK3", "冰冷的揹包")

STRINGS.NAMES.TONG_LEO_WHISTLE = "利奧的哨子"
_G.ChinesePlus.RenameRecipe("TONG_LEO_WHISTLE", "召喚利奧")

STRINGS.NAMES.TONG_LEO = "利奧"
