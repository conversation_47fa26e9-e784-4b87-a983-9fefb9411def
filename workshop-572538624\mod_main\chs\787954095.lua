STRINGS.NAMES.MAGICLANTERN_WHITE = "发光瓶"
_G.ChinesePlus.RenameRecipe("MAGICLANTERN_WHITE", "照亮你的路。白色的光。")

STRINGS.NAMES.MAGICLANTERN_RED = "险恶瓶"
_G.ChinesePlus.RenameRecipe("MAGICLANTERN_RED", "古老的力量。红色的光。")

STRINGS.NAMES.MAGICLANTERN_BLUE = "冰冷瓶"
_G.ChinesePlus.RenameRecipe("MAGICLANTERN_BLUE", "平静的光。蓝色的光。")

STRINGS.NAMES.MAGICLANTERN_PINK = "平安瓶"
_G.ChinesePlus.RenameRecipe("MAGICLANTERN_PINK", "瓶子里的日落。粉色的光。")

STRINGS.NAMES.MAGICLANTERN_PURPLE = "药剂瓶"
_G.ChinesePlus.RenameRecipe("MAGICLANTERN_PURPLE", "神秘的光芒。紫色的光。")

STRINGS.NAMES.MAGICLANTERN_ORANGE = "灰烬瓶"
_G.ChinesePlus.RenameRecipe("MAGICLANTERN_ORANGE", "像快要灭了的火。橙色的光。")

STRINGS.NAMES.MAGICLANTERN_YELLOW = "阳光瓶"
_G.ChinesePlus.RenameRecipe("MAGICLANTERN_YELLOW", "照亮黑夜。黄色的光。")

STRINGS.NAMES.MAGICLANTERN_GREEN = "毒药瓶"
_G.ChinesePlus.RenameRecipe("MAGICLANTERN_GREEN", "病态的辉光。绿色的光。")

--STRINGS
--Wilson/generic
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MAGICLANTERN_WHITE = "它会发光。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MAGICLANTERN_RED = "红色意味着危险。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MAGICLANTERN_BLUE = "真奇怪…"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MAGICLANTERN_PINK = "它很漂亮。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MAGICLANTERN_PURPLE = "这里有力量。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MAGICLANTERN_ORANGE = "温暖的。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MAGICLANTERN_YELLOW = "黑暗中的光明。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MAGICLANTERN_GREEN = "它看起来病怏怏的…"

--Woodie
STRINGS.CHARACTERS.WOODIE.DESCRIBE.MAGICLANTERN_WHITE = nil --"It lights the way."
STRINGS.CHARACTERS.WOODIE.DESCRIBE.MAGICLANTERN_RED = nil --"I don't trust it."
STRINGS.CHARACTERS.WOODIE.DESCRIBE.MAGICLANTERN_BLUE = nil --"A bit nippy, eh?"
STRINGS.CHARACTERS.WOODIE.DESCRIBE.MAGICLANTERN_PINK = nil --"That'll stop the kerfuffle."
STRINGS.CHARACTERS.WOODIE.DESCRIBE.MAGICLANTERN_PURPLE = nil --"It'll get me well again."
STRINGS.CHARACTERS.WOODIE.DESCRIBE.MAGICLANTERN_ORANGE = nil --"That's toastie for sure!"
STRINGS.CHARACTERS.WOODIE.DESCRIBE.MAGICLANTERN_YELLOW = nil --"As nice as a summer day."
STRINGS.CHARACTERS.WOODIE.DESCRIBE.MAGICLANTERN_GREEN = nil --"That'll teach 'em, eh?"

--Waxwell/Maxwell
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.MAGICLANTERN_WHITE = nil --"This should keep her at bay."
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.MAGICLANTERN_RED = nil --"A dangerous thing."
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.MAGICLANTERN_BLUE = nil --"A piece of winter."
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.MAGICLANTERN_PINK = nil --"Keep your enemies close..."
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.MAGICLANTERN_PURPLE = nil --"It gives me life, at what cost?"
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.MAGICLANTERN_ORANGE = nil --"A fire inside."
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.MAGICLANTERN_YELLOW = nil --"Quite a dapper light."
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.MAGICLANTERN_GREEN = nil --"It's toxic."

--Wolfgang
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.MAGICLANTERN_WHITE = nil --"Shiny!"
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.MAGICLANTERN_RED = nil --"It scary."
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.MAGICLANTERN_BLUE = nil --"Brr, chilly!"
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.MAGICLANTERN_PINK = nil --"It make a friend."
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.MAGICLANTERN_PURPLE = nil --"I strong!"
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.MAGICLANTERN_ORANGE = nil --"It warm."
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.MAGICLANTERN_YELLOW = nil --"Wolfgang is not afraid!"
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.MAGICLANTERN_GREEN = nil --"I should not drink."

--WX78
STRINGS.CHARACTERS.WX78.DESCRIBE.MAGICLANTERN_WHITE = nil --"IT GLOWS IN THE DARK"
STRINGS.CHARACTERS.WX78.DESCRIBE.MAGICLANTERN_RED = nil --"PROXIMITY WARNING: DANGER"
STRINGS.CHARACTERS.WX78.DESCRIBE.MAGICLANTERN_BLUE = nil --"IT COOLS MY CIRCUITS"
STRINGS.CHARACTERS.WX78.DESCRIBE.MAGICLANTERN_PINK = nil --"SUBJECT CREATES A COMBAT ERROR"
STRINGS.CHARACTERS.WX78.DESCRIBE.MAGICLANTERN_PURPLE = nil --"POSITIVE NOCTURAL FEEDBACK LOOP"
STRINGS.CHARACTERS.WX78.DESCRIBE.MAGICLANTERN_ORANGE = nil --"MONITORING SYSTEM TEMPERATURE"
STRINGS.CHARACTERS.WX78.DESCRIBE.MAGICLANTERN_YELLOW = nil --"SYSTEM OPTIMIZATION IN PROGRESS"
STRINGS.CHARACTERS.WX78.DESCRIBE.MAGICLANTERN_GREEN = nil --"IT CORRODES ORGANIC LIFEFORMS"

--Willow
STRINGS.CHARACTERS.WILLOW.DESCRIBE.MAGICLANTERN_WHITE = nil --"I like my lighter better."
STRINGS.CHARACTERS.WILLOW.DESCRIBE.MAGICLANTERN_RED = nil --"I don't like it."
STRINGS.CHARACTERS.WILLOW.DESCRIBE.MAGICLANTERN_BLUE = nil --"A frosted light."
STRINGS.CHARACTERS.WILLOW.DESCRIBE.MAGICLANTERN_PINK = nil --"It banks the fires of combat."
STRINGS.CHARACTERS.WILLOW.DESCRIBE.MAGICLANTERN_PURPLE = nil --"It heals the hurts."
STRINGS.CHARACTERS.WILLOW.DESCRIBE.MAGICLANTERN_ORANGE = nil --"The fire should be free!"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.MAGICLANTERN_YELLOW = nil --"Keeps the nightmares away."
STRINGS.CHARACTERS.WILLOW.DESCRIBE.MAGICLANTERN_GREEN = nil --"It's sick, like my dreams."

--Wendy
STRINGS.CHARACTERS.WENDY.DESCRIBE.MAGICLANTERN_WHITE = nil --"Like a candle in the night."
STRINGS.CHARACTERS.WENDY.DESCRIBE.MAGICLANTERN_RED = nil --"It's cursed, stay back."
STRINGS.CHARACTERS.WENDY.DESCRIBE.MAGICLANTERN_BLUE = nil --"I feel an icy grip."
STRINGS.CHARACTERS.WENDY.DESCRIBE.MAGICLANTERN_PINK = nil --"It makes me want to play!"
STRINGS.CHARACTERS.WENDY.DESCRIBE.MAGICLANTERN_PURPLE = nil --"I can feel my strength returning."
STRINGS.CHARACTERS.WENDY.DESCRIBE.MAGICLANTERN_ORANGE = nil --"Warm and cosy."
STRINGS.CHARACTERS.WENDY.DESCRIBE.MAGICLANTERN_YELLOW = nil --"There goes my melancholia."
STRINGS.CHARACTERS.WENDY.DESCRIBE.MAGICLANTERN_GREEN = nil --"It festers the blood."

--Wickerbottom
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.MAGICLANTERN_WHITE = nil --"A simple light."
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.MAGICLANTERN_RED = nil --"Magic is merely science we can't yet explain."
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.MAGICLANTERN_BLUE = nil --"An inverse use of thermal energy."
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.MAGICLANTERN_PINK = nil --"I wonder if this has any recreational application?"
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.MAGICLANTERN_PURPLE = nil --"I should study its regenerative properties."
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.MAGICLANTERN_ORANGE = nil --"An interesting use of thermal energy."
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.MAGICLANTERN_YELLOW = nil --"Did this treatment pass clinical studies?"
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.MAGICLANTERN_GREEN = nil --"It shows signs of toxicity."

--Webber
STRINGS.CHARACTERS.WEBBER.DESCRIBE.MAGICLANTERN_WHITE = nil --"A night light."
STRINGS.CHARACTERS.WEBBER.DESCRIBE.MAGICLANTERN_RED = nil --"We don't like it!"
STRINGS.CHARACTERS.WEBBER.DESCRIBE.MAGICLANTERN_BLUE = nil --"Our fingers and toes are cold."
STRINGS.CHARACTERS.WEBBER.DESCRIBE.MAGICLANTERN_PINK = nil --"Maybe they want to play?"
STRINGS.CHARACTERS.WEBBER.DESCRIBE.MAGICLANTERN_PURPLE = nil --"Better than eating vegetables!"
STRINGS.CHARACTERS.WEBBER.DESCRIBE.MAGICLANTERN_ORANGE = nil --"Warm and snuggly!"
STRINGS.CHARACTERS.WEBBER.DESCRIBE.MAGICLANTERN_YELLOW = nil --"It makes us feel better."
STRINGS.CHARACTERS.WEBBER.DESCRIBE.MAGICLANTERN_GREEN = nil --"Yuck!"

--Wathgrithr/Wigfrid
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.MAGICLANTERN_WHITE = nil --"To better see my night time foes!"
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.MAGICLANTERN_RED = nil --"I must see the evil to slay it."
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.MAGICLANTERN_BLUE = nil --"I shall call it Skadi's brew."
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.MAGICLANTERN_PINK = nil --"A coward maker!"
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.MAGICLANTERN_PURPLE = nil --"Eir protect me."
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.MAGICLANTERN_ORANGE = nil --"A blessing from Sol herself."
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.MAGICLANTERN_YELLOW = nil --"A soft summer light."
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.MAGICLANTERN_GREEN = nil --"A slow death to my foes!"

