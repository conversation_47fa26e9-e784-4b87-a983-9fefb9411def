_G.ChinesePlus.RenameTab("<PERSON><PERSON>'s Tab","蛛侍")

STRINGS.NAMES.KATANAS = "蛛侍的刀"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.KATANAS = "一套锋利的刀"

STRINGS.NAMES.SHIELD = "美国队长盾牌"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SHIELD = "自动收的回旋镖"

_G.ChinesePlus.RenameRecipe("KATANAS","一套锋利的刀")
_G.ChinesePlus.RenameRecipe("SILK","蛛侍产好丝。")

-- The character select screen lines
STRINGS.CHARACTER_TITLES.spidey = "蛛侍"
STRINGS.CHARACTER_NAMES.spidey = "蛛侍"
STRINGS.CHARACTER_DESCRIPTIONS.spidey = "*生命自动恢复\n*快速 (移动, 收获, 砍树)\n*夜视能力"
STRINGS.CHARACTER_QUOTES.spidey = "\"嘿！大家好！\""

-- Custom speech strings
--STRINGS.CHARACTERS.SPIDEY = require(chinesefolder.."/Spidey/speech_spidey")
STRINGS.CHARACTERS.SPIDEY = nil

-- The character's name as appears in-game
STRINGS.NAMES.SPIDEY = "蛛侍"

