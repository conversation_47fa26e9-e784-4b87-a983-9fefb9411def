_G.ChinesePlus.RenameTab("Sleight", "魔術")

-- The character select screen lines
STRINGS.CHARACTER_TITLES.nelke = "斬首兔子"
STRINGS.CHARACTER_NAMES.nelke = "內爾克"
STRINGS.CHARACTER_DESCRIPTIONS.nelke = "*更瘋狂、更凶猛\n*不能使用魔法工具, 但可以製造\n*什麼也不做時會感到沮喪"
STRINGS.CHARACTER_QUOTES.nelke = "\"這是一個絕妙的主意！不是嗎？\""

-- Custom speech strings
STRINGS.CHARACTERS.ESCTEMPLATE = nil -- require "speech_nelke"

-- The character's name as appears in-game
STRINGS.NAMES.ESCTEMPLATE = "內爾克"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.ESCTEMPLATE =
{
	GENERIC = "這是內爾克!",
	ATTACKER = "內爾克看起來狡猾的...",
	MURDERER = "凶手!",
	REVIVER = "內爾克, 鬼魂之友.",
	GHOST = "內爾克可以使用救贖之心.",
}

GLOBAL.STRINGS.NAMES.AXE_NELKE = "萬能鑰匙"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.AXE_NELKE = "換言之，這是\"火斧\"。"
_G.ChinesePlus.RenameRecipe("AXE_NELKE", "破壞一切！！")

GLOBAL.STRINGS.NAMES.SAW_THROWER = "旋轉鋸機"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.SAW_THROWER = "這很危險！！"
_G.ChinesePlus.RenameRecipe("SAW_THROWER", "讓我們玩切割魔術！！")

GLOBAL.STRINGS.NAMES.BOMB_TELEPORTER = "煙霧彈"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.BOMB_TELEPORTER = "完美的平凡！！"
_G.ChinesePlus.RenameRecipe("BOMB_TELEPORTER", "讓我們玩炸彈魔術！！")

GLOBAL.STRINGS.NAMES.BOMB_MAGIC = "炸彈魔箱"

GLOBAL.STRINGS.NAMES.HAT_NELKE = "小禮帽"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.HAT_NELKE = "完美的平凡！！"
_G.ChinesePlus.RenameRecipe("HAT_NELKE", "讓我們施展魔術！！")

