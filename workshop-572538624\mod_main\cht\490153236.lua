STRINGS.NAMES.CHOPPERHAT = "喬巴的帽子"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.CHOPPERHAT = "一頂被小馴鹿珍惜的帽子"

_G.ChinesePlus.RenameRecipe("REDMEDICINE", "一種緩解疼痛的紅色藥物。")
STRINGS.NAMES.REDMEDICINE = "紅藥"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.REDMEDICINE = "這能很好的緩解疼痛。"

_G.ChinesePlus.RenameRecipe("GREENMEDICINE", "一種緩解壓力的綠色藥物。")
STRINGS.NAMES.GREENMEDICINE = "綠藥"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.GREENMEDICINE = "我認為這對大腦有好處。"

_G.ChinesePlus.RenameRecipe("BLUEMEDICINE", "一種對一切都有作用的藥物。")
STRINGS.NAMES.BLUEMEDICINE = "藍藥"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.BLUEMEDICINE = "當你有這個時還有誰需要阿司匹林？"

-- The character select screen lines
STRINGS.CHARACTER_TITLES.chopper = "棉花糖的愛好者"
STRINGS.CHARACTER_NAMES.chopper = "喬巴"
STRINGS.CHARACTER_DESCRIPTIONS.chopper = "*可以變形（只是還沒有）\n*可以製作幾種藥品\n*是一隻馴鹿，不是浣熊，駝鹿，或狐狸。"
STRINGS.CHARACTER_QUOTES.chopper = "\"別擔心！我是醫生！\""

-- Custom speech strings
--STRINGS.CHARACTERS.CHOPPER = require(chinesefolder.."/Chopper/speech_chopper")
STRINGS.CHARACTERS.CHOPPER = nil

-- The character's name as appears in-game
STRINGS.NAMES.CHOPPER = "喬巴"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CHOPPER =
{
  GENERIC = "這是喬巴！",
  ATTACKER = "喬巴看起來很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "喬巴, 鬼魂朋友！",
  GHOST = "喬巴可以使用一顆心。",
}

