-- The character select screen lines
STRINGS.CHARACTER_TITLES.sollyz = "猫的巴掌"
STRINGS.CHARACTER_NAMES.sollyz = "Sollyz"
STRINGS.CHARACTER_DESCRIPTIONS.sollyz = "*吃鱼升级 (最大30级)\n*有钓鱼杆。\n*早晨会跑得快！\n"
STRINGS.CHARACTER_QUOTES.sollyz = "\"我爱 Haruz 就像我爱那些鱼一样！\""

-- Custom speech strings
--STRINGS.CHARACTERS.sollyz = require(chinesefolder.."/Sollyz/speech_sollyz")
STRINGS.CHARACTERS.sollyz = nil

-- The character's name as appears in-game
STRINGS.NAMES.sollyz = "Sollyz"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.sollyz =
{
  GENERIC = "这是 Sollyz！",
  ATTACKER = "Sollyz 看起来很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "Sollyz, 鬼魂朋友！",
  GHOST = "Sollyz 可以使用一颗心。",
}

