-- 网络同步修复工具
-- 用于自动修复DST多人联机的网络同步问题

local _G = GLOBAL
local TheWorld = _G.TheWorld
local TheNet = _G.TheNet

local NetworkRepair = {}

-- 修复forest_network组件挂载问题
function NetworkRepair.RepairForestNetworkComponents()
    if not TheWorld.ismastersim then
        return false, "Can only repair on master sim"
    end

    local forest_network = TheWorld.net
    if not forest_network then
        return false, "forest_network not available"
    end

    local repaired = {}
    local failed = {}

    -- 需要修复的组件列表
    local required_components = {
        "seasonal_gust_manager",
        "season_warden_invasion",
        "network_sync_manager"
    }

    for _, comp_name in ipairs(required_components) do
        if not forest_network.components or not forest_network.components[comp_name] then
            -- 尝试添加缺失的组件
            local success = pcall(function()
                forest_network:AddComponent(comp_name)
            end)
            
            if success and forest_network.components[comp_name] then
                table.insert(repaired, comp_name)
            else
                table.insert(failed, comp_name)
            end
        end
    end

    local success_count = #repaired
    local total_count = #required_components

    return success_count == total_count, {
        repaired = repaired,
        failed = failed,
        success_rate = success_count / total_count
    }
end

-- 修复网络变量初始化问题
function NetworkRepair.RepairNetworkVariables()
    if not TheWorld.ismastersim then
        return false, "Can only repair on master sim"
    end

    local forest_network = TheWorld.net
    if not forest_network then
        return false, "forest_network not available"
    end

    local repaired_components = {}
    local failed_components = {}

    -- 检查并修复季风乱流管理器
    local gust_manager = forest_network.components.seasonal_gust_manager
    if gust_manager and not gust_manager._network_initialized then
        local success = pcall(function()
            -- 尝试重新初始化网络变量
            gust_manager._network_initialized = false
            -- 这里需要调用组件的重新初始化方法
            -- 由于组件已经创建，我们只能标记需要重启
        end)
        
        if success then
            table.insert(repaired_components, "seasonal_gust_manager")
        else
            table.insert(failed_components, "seasonal_gust_manager")
        end
    end

    return #failed_components == 0, {
        repaired = repaired_components,
        failed = failed_components
    }
end

-- 修复网络同步状态不一致问题
function NetworkRepair.RepairSyncInconsistency()
    local issues = {}
    local fixes = {}

    -- 检查服务端/客户端状态一致性
    if TheWorld and TheNet then
        local is_master_sim = TheWorld.ismastersim
        local is_server = TheNet:GetIsServer()

        if is_master_sim ~= is_server then
            -- 这种不一致通常表示严重的系统问题，无法自动修复
            table.insert(issues, "Server state inconsistency detected - requires restart")
        end
    end

    -- 检查forest_network可用性
    if TheWorld and not TheWorld.net then
        table.insert(issues, "forest_network missing - requires game restart")
    end

    return #issues == 0, {
        issues = issues,
        fixes = fixes
    }
end

-- 执行完整的网络修复流程
function NetworkRepair.PerformFullRepair()
    local repair_results = {
        timestamp = os.time(),
        forest_network_repair = {},
        network_variables_repair = {},
        sync_consistency_repair = {},
        overall_success = false
    }

    -- 1. 修复forest_network组件
    local fn_success, fn_result = NetworkRepair.RepairForestNetworkComponents()
    repair_results.forest_network_repair = {
        success = fn_success,
        result = fn_result
    }

    -- 2. 修复网络变量
    local nv_success, nv_result = NetworkRepair.RepairNetworkVariables()
    repair_results.network_variables_repair = {
        success = nv_success,
        result = nv_result
    }

    -- 3. 修复同步一致性
    local sc_success, sc_result = NetworkRepair.RepairSyncInconsistency()
    repair_results.sync_consistency_repair = {
        success = sc_success,
        result = sc_result
    }

    -- 计算总体成功率
    local success_count = 0
    if fn_success then success_count = success_count + 1 end
    if nv_success then success_count = success_count + 1 end
    if sc_success then success_count = success_count + 1 end

    repair_results.overall_success = success_count >= 2 -- 至少2/3成功

    return repair_results
end

-- 打印修复报告
function NetworkRepair.PrintRepairReport(repair_results)
    print("=== Season Workshop 网络修复报告 ===")
    print("时间戳: " .. (repair_results.timestamp or "unknown"))
    
    local overall_status = repair_results.overall_success and "✅ 成功" or "❌ 部分失败"
    print("总体状态: " .. overall_status)
    
    print("\n=== Forest Network 组件修复 ===")
    local fn_repair = repair_results.forest_network_repair
    if fn_repair.success then
        print("✅ Forest Network 组件修复成功")
        if fn_repair.result.repaired and #fn_repair.result.repaired > 0 then
            print("修复的组件: " .. table.concat(fn_repair.result.repaired, ", "))
        end
    else
        print("❌ Forest Network 组件修复失败")
        if fn_repair.result.failed and #fn_repair.result.failed > 0 then
            print("失败的组件: " .. table.concat(fn_repair.result.failed, ", "))
        end
    end
    
    print("\n=== 网络变量修复 ===")
    local nv_repair = repair_results.network_variables_repair
    if nv_repair.success then
        print("✅ 网络变量修复成功")
    else
        print("❌ 网络变量修复失败")
        if nv_repair.result.failed and #nv_repair.result.failed > 0 then
            print("失败的组件: " .. table.concat(nv_repair.result.failed, ", "))
        end
    end
    
    print("\n=== 同步一致性修复 ===")
    local sc_repair = repair_results.sync_consistency_repair
    if sc_repair.success then
        print("✅ 同步一致性正常")
    else
        print("❌ 发现同步一致性问题")
        if sc_repair.result.issues and #sc_repair.result.issues > 0 then
            for _, issue in ipairs(sc_repair.result.issues) do
                print("  - " .. issue)
            end
        end
    end
    
    if not repair_results.overall_success then
        print("\n💡 建议:")
        print("  - 如果问题持续存在，请重启游戏")
        print("  - 检查mod兼容性")
        print("  - 确保所有玩家使用相同的mod版本")
    end
    
    return repair_results
end

-- 自动诊断并修复网络问题
function NetworkRepair.AutoDiagnoseAndRepair()
    print("=== 开始自动诊断和修复 ===")
    
    -- 首先进行诊断
    local NetworkValidator = require("scripts/utils/network_validator")
    local valid, issues, warnings = NetworkValidator.ValidateNetworkSync()
    
    if valid and #warnings == 0 then
        print("✅ 网络状态正常，无需修复")
        return true, "No repair needed"
    end
    
    print("发现问题，开始修复...")
    if #issues > 0 then
        print("严重问题:")
        for _, issue in ipairs(issues) do
            print("  - " .. issue)
        end
    end
    
    if #warnings > 0 then
        print("警告:")
        for _, warning in ipairs(warnings) do
            print("  - " .. warning)
        end
    end
    
    -- 执行修复
    local repair_results = NetworkRepair.PerformFullRepair()
    NetworkRepair.PrintRepairReport(repair_results)
    
    -- 重新验证
    print("\n=== 修复后验证 ===")
    local post_valid, post_issues, post_warnings = NetworkValidator.ValidateNetworkSync()
    
    if post_valid then
        print("✅ 修复成功，网络状态正常")
        return true, repair_results
    else
        print("❌ 修复后仍有问题:")
        for _, issue in ipairs(post_issues) do
            print("  - " .. issue)
        end
        return false, repair_results
    end
end

return NetworkRepair
