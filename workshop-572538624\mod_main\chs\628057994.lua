-- The character select screen lines
STRINGS.CHARACTER_TITLES.tong = "士官长"
STRINGS.CHARACTER_NAMES.tong = "童"
STRINGS.CHARACTER_DESCRIPTIONS.tong = "*跑得快\n*随时随地可以做菜\n*拥有寒冰背包"
STRINGS.CHARACTER_QUOTES.tong = "\"不，我要自己做！\""

-- Custom speech strings
--STRINGS.CHARACTERS.TONG = require(chinesefolder.."/Tong/speech_tong")
STRINGS.CHARACTERS.TONG = nil

-- The character's name as appears in-game
STRINGS.NAMES.TONG = "童"

STRINGS.NAMES.TONG_COOKINGPOT = "烹饪锅"
STRINGS.NAMES.TONG_COOKINGPOT_ITEM = "烹饪锅"
_G.ChinesePlus.RenameRecipe("TONG_COOKINGPOT_ITEM", "便携式烹饪锅")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.PORTABLECOOKPOT_ITEM = "烹饪锅"

STRINGS.NAMES.TONG_ICEBACKPACK0 = "寒冰背包"
_G.ChinesePlus.RenameRecipe("TONG_ICEBACKPACK0", "冰冷的背包")

STRINGS.NAMES.TONG_ICEBACKPACK1 = "寒冰背包 1000"
_G.ChinesePlus.RenameRecipe("TONG_ICEBACKPACK1", "冰冷的背包")

STRINGS.NAMES.TONG_ICEBACKPACK2 = "寒冰背包 2000"
_G.ChinesePlus.RenameRecipe("TONG_ICEBACKPACK2", "冰冷的背包")

STRINGS.NAMES.TONG_ICEBACKPACK3 = "寒冰背包 3000"
_G.ChinesePlus.RenameRecipe("TONG_ICEBACKPACK3", "冰冷的背包")

STRINGS.NAMES.TONG_LEO_WHISTLE = "利奥的哨子"
_G.ChinesePlus.RenameRecipe("TONG_LEO_WHISTLE", "召唤利奥")

STRINGS.NAMES.TONG_LEO = "利奥"
