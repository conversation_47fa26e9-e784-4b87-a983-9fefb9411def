-- The character select screen lines
STRINGS.CHARACTER_TITLES.brs = "黑岩射手（仅妖精）"
STRINGS.CHARACTER_NAMES.brs = "黑岩射手"
STRINGS.CHARACTER_DESCRIPTIONS.brs = "*有时发疯\n*在晚上会到孤独"
STRINGS.CHARACTER_QUOTES.brs = "\"小鸟和色彩\""

-- Custom speech strings
--STRINGS.CHARACTERS.BRS = require(chinesefolder.."/BlackRockShooter/speech_brs")
STRINGS.CHARACTERS.BRS = nil

-- The character's name as appears in-game
STRINGS.NAMES.BRS = "黑岩射手"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.BRS =
{
  GENERIC = "这是黑岩射手",
  ATTACKER = "黑岩射手看起来很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "黑岩射手, 鬼魂朋友！",
  GHOST = "黑岩射手可以使用一颗心。",
}

