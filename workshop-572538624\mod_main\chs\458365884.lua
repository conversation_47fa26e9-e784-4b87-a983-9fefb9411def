-- The character select screen lines
STRINGS.CHARACTER_TITLES.xenomorph = "异形"
STRINGS.CHARACTER_NAMES.xenomorph = "异形"
STRINGS.CHARACTER_DESCRIPTIONS.xenomorph = "*非常快\n*在黑暗中不会失去理智\n*只吃肉类食物"
STRINGS.CHARACTER_QUOTES.xenomorph = "\"咯~~~！\""

-- Custom speech strings
--STRINGS.CHARACTERS.XENOMORPH = require(chinesefolder.."/Xenomorph/speech_xenomorph")
STRINGS.CHARACTERS.XENOMORPH = nil

-- The character's name as appears in-game
STRINGS.NAMES.XENOMORPH = "异形"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.XENOMORPH =
{
  GENERIC = "这是异形",
  ATTACKER = "异形看起来很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "异形, 鬼魂朋友！",
  GHOST = "异形可以使用一颗心。",
}

