local _G = GLOBAL
local Class = _G.Class
local TheWorld = _G.TheWorld
local TUNING = _G.TUNING
local AllPlayers = _G.AllPlayers
local net_bool = _G.net_bool
local net_string = _G.net_string
local net_float = _G.net_float
local net_tinybyte = _G.net_tinybyte

local SeasonalGust = Class(function(self, inst)
    self.inst = inst
    self.enabled = true
    self.events_left = 0
    self.scheduled = {}
    self.active_event = nil -- 当前活跃的事件
    self.original_colour = nil -- 保存原始世界颜色
    self.next_event_time = 0 -- 下次事件时间
    self._network_initialized = false

    -- 网络变量初始化（在forest_network上创建，符合DST规范）
    -- 注意：inst现在是forest_network，不是TheWorld
    -- 重要：验证inst是否具有网络能力
    if not inst.GUID or not inst.entity then
        print("[SeasonWorkshop] Error: Invalid network entity for seasonal gust manager")
        return
    end

    -- 额外验证：确保这是正确的网络实体类型
    local is_forest_network = inst == TheWorld.net
    local is_cave_network = inst.prefab == "cave_network"
    local has_network_component = inst.Network ~= nil

    if not (is_forest_network or is_cave_network or has_network_component) then
        print("[SeasonWorkshop] Warning: Seasonal gust manager attached to unexpected entity type")
    end

    -- 使用更严格的唯一前缀避免网络变量名称冲突
    local unique_prefix = "seasonworkshop_gust_" .. tostring(inst.GUID) .. "_"

    -- 安全创建网络变量的辅助函数
    local function safe_create_netvar(var_type, name, event_name)
        local netvar = nil
        local success = pcall(function()
            if var_type == "bool" then
                netvar = net_bool(inst.GUID, name, event_name)
            elseif var_type == "string" then
                netvar = net_string(inst.GUID, name, event_name)
            elseif var_type == "float" then
                netvar = net_float(inst.GUID, name, event_name)
            elseif var_type == "tinybyte" then
                netvar = net_tinybyte(inst.GUID, name, event_name)
            end
        end)

        if not success or not netvar then
            print("[SeasonWorkshop] Error: Failed to create " .. var_type .. " network variable: " .. name)
            return nil
        end

        return netvar
    end

    -- 创建网络变量
    self._net_active = safe_create_netvar("bool", unique_prefix .. "active", "gust_active_dirty")
    self._net_season = safe_create_netvar("string", unique_prefix .. "season", "gust_season_dirty")
    self._net_remaining = safe_create_netvar("float", unique_prefix .. "remaining", "gust_remaining_dirty")
    self._net_events_left = safe_create_netvar("tinybyte", unique_prefix .. "events_left", "gust_events_left_dirty")
    self._net_next_event_time = safe_create_netvar("float", unique_prefix .. "next_event", "gust_next_event_dirty")

    -- 验证网络变量创建成功
    if not self._net_active or not self._net_season or not self._net_remaining or
       not self._net_events_left or not self._net_next_event_time then
        print("[SeasonWorkshop] Error: Failed to create network variables for seasonal gust manager")
        return
    end

    self._network_initialized = true

    if TheWorld.ismastersim then
        -- 服务端：初始化网络变量值（使用pcall确保安全性）
        local init_success = true

        local function safe_init(var, value, name)
            local success = pcall(function()
                var:set(value)
            end)
            if not success then
                print("[SeasonWorkshop] Error: Failed to initialize " .. name)
                init_success = false
            end
        end

        safe_init(self._net_active, false, "active state")
        safe_init(self._net_season, "", "season")
        safe_init(self._net_remaining, 0, "remaining time")
        safe_init(self._net_events_left, 0, "events left")
        safe_init(self._net_next_event_time, 0, "next event time")

        if not init_success then
            print("[SeasonWorkshop] Warning: Some network variables failed to initialize")
        end

        -- 监听世界季节变化（组件在forest_network上，但仍需监听TheWorld的季节）
        TheWorld:WatchWorldState("season", function()
            if self.inst and self.inst:IsValid() then
                self:OnSeasonChanged()
            end
        end)
        TheWorld:DoTaskInTime(0, function()
            if self.inst and self.inst:IsValid() then
                self:OnSeasonChanged(true)
            end
        end)
    else
        -- 客户端：监听网络同步事件
        -- 客户端监听事件状态变化
        inst:ListenForEvent("gust_active_dirty", function()
            if not self.inst or not self.inst:IsValid() then
                print("[SeasonWorkshop] Warning: Invalid gust manager in active sync")
                return
            end

            -- 添加网络延迟容错
            local is_active = self._net_active and self._net_active:value() or false
            if is_active and not self.active_event then
                -- 事件开始
                local season = self._net_season and self._net_season:value() or ""
                if season and season ~= "" then
                    self:ApplyWorldTint(season)
                    self.active_event = { season = season }
                end
            elseif not is_active and self.active_event then
                -- 事件结束
                self:RestoreWorldTint()
                self.active_event = nil
            end
        end)

        inst:ListenForEvent("gust_events_left_dirty", function()
            if self.inst and self.inst:IsValid() then
                self.events_left = self._net_events_left and self._net_events_left:value() or 0
            end
        end)

        inst:ListenForEvent("gust_next_event_dirty", function()
            if self.inst and self.inst:IsValid() then
                self.next_event_time = self._net_next_event_time and self._net_next_event_time:value() or 0
            end
        end)

        return
    end
end)



local function SpawnOrbsNearPlayer(player)
    if not player or not player:IsValid() then return end
    local season = TheWorld.state.season or "autumn"
    local orb_pref = "season_orb_"..season
    local count = math.random(3,5)

    -- 让玩家说"天降神珠"
    if player.components and player.components.talker then
        player.components.talker:Say("天降神珠！", 2)
    end

    -- 在玩家附近生成宝珠
    for i=1,count do
        local x,y,z = player.Transform:GetWorldPosition()
        local offset = FindWalkableOffset(player:GetPosition(), math.random()*2*PI, math.random(2,5), 8, true)
        if offset ~= nil then
            x = x + offset.x
            z = z + offset.z
        end
        local ent = SpawnPrefab(orb_pref)
        if ent then
            ent.Transform:SetPosition(x, 0, z)
        end
    end
end

-- 应用世界变色效果
function SeasonalGust:ApplyWorldTint(season)
    -- 尽量使用 PostProcessor；若不存在则为每名玩家在脚下生成一枚轻量半透明提示FX，避免无提示
    if not TheWorld then return end

    -- 保存原始状态（仅作标记）
    if not self.original_colour then
        self.original_colour = true
    end

    local tint_values = {
        spring = {r = 0.9, g = 1.1, b = 0.9},
        summer = {r = 1.1, g = 1.0, b = 0.8},
        autumn = {r = 1.0, g = 0.95, b = 0.8},
        winter = {r = 0.9, g = 0.95, b = 1.1}
    }
    local tint = tint_values[season] or tint_values["autumn"]

    if TheWorld.components and TheWorld.components.postprocessor then
        TheWorld.components.postprocessor:SetColourModifier(tint.r, tint.g, tint.b, 1)
    else
        -- 后备：在玩家附近生成淡化 FX，持续 0.6s，不进行大量实体创建
        local strength = (GetModConfigData("vfx_strength") or 1)
        local alpha = strength == 0 and 0.4 or (strength == 2 and 0.9 or 0.7)
        for _, p in ipairs(AllPlayers or {}) do
            if p and p:IsValid() then
                local fx = SpawnPrefab("staff_castinglight")
                if fx then
                    fx.Transform:SetPosition(p.Transform:GetWorldPosition())
                    if fx.AnimState then
                        fx.AnimState:SetMultColour(tint.r, tint.g, tint.b, alpha)
                    end
                    fx:DoTaskInTime(0.6, fx.Remove)
                end
            end
        end
    end

    -- 提示信息
    for _, p in ipairs(AllPlayers or {}) do
        if p and p:IsValid() and p.components and p.components.talker then
            local season_names = { spring = "春季", summer = "夏季", autumn = "秋季", winter = "冬季" }
            p.components.talker:Say("世界被" .. (season_names[season] or "神秘") .. "的气息笼罩...", 2)
        end
    end
end

-- 恢复世界原始颜色
function SeasonalGust:RestoreWorldTint()
    if not TheWorld or not self.original_colour then return end

    -- 恢复默认色调
    if TheWorld.components.postprocessor then
        TheWorld.components.postprocessor:SetColourModifier(1, 1, 1, 1)
    end

    self.original_colour = nil
end

-- 开始季风乱流事件
function SeasonalGust:StartEvent()
    if not TheWorld.ismastersim then return end

    -- 错误处理：检查状态
    if not self.inst or not self.inst:IsValid() then
        print("[SeasonWorkshop] Error: Invalid gust manager in StartEvent")
        return
    end

    if not self.enabled or self.active_event then
        print("[SeasonWorkshop] Info: Gust event skipped - disabled or already active")
        return
    end

    local season = TheWorld.state.season or "autumn"

    -- 验证季节有效性
    local valid_seasons = {spring = true, summer = true, autumn = true, winter = true}
    if not valid_seasons[season] then
        print("[SeasonWorkshop] Error: Invalid season for gust event: " .. tostring(season))
        season = "autumn" -- 回退到默认季节
    end

    -- 事件开始提示
    local player_count = 0
    for _, p in ipairs(AllPlayers or {}) do
        if p and p:IsValid() and p.components and p.components.talker then
            p.components.talker:Say("季风乱流开始了！", 3)
            player_count = player_count + 1
        end
    end

    print(string.format("[SeasonWorkshop] Gust event started for %d players in %s season", player_count, season))

    -- 应用世界变色
    self:ApplyWorldTint(season)

    -- 延迟一秒后在每位玩家附近生成季节宝珠（让开始提示先显示）
    self.inst:DoTaskInTime(1, function()
        -- 确保在联机服务器时为每个在线玩家都刷新宝珠
        local players = AllPlayers or {}
        if #players == 0 and ThePlayer then
            -- 单机模式的后备方案
            players = {ThePlayer}
        end

        for _, p in ipairs(players) do
            if p and p:IsValid() and not p:HasTag("playerghost") then
                SpawnOrbsNearPlayer(p)
            end
        end
    end)

    -- 设置事件持续时间（半天至1天，配置项）
    local duration_config = _G.GetModConfigData("gust_duration") or "medium"
    local duration_hours = 12 -- 默认半天
    if duration_config == "short" then
        duration_hours = 6  -- 短：6小时
    elseif duration_config == "long" then
        duration_hours = 24 -- 长：1天
    end

    local duration_seconds = duration_hours * 60 -- 简化：1小时=60秒（游戏时间）

    -- 标记事件为活跃状态
    self.active_event = {
        season = season,
        start_time = GetTime(),
        duration = duration_seconds
    }

    -- 同步事件状态到客户端
    self._net_active:set(true)
    self._net_season:set(season)
    self._net_remaining:set(duration_seconds)

    -- 设置事件结束任务
    self.inst:DoTaskInTime(duration_seconds, function()
        self:EndEvent()
    end)

    -- 定期更新剩余时间
    self.update_task = self.inst:DoPeriodicTask(10, function()
        if self.active_event then
            local remaining = self.active_event.start_time + self.active_event.duration - GetTime()
            self._net_remaining:set(math.max(0, remaining))
        end
    end)
end

-- 结束季风乱流事件
function SeasonalGust:EndEvent()
    if not TheWorld.ismastersim then return end
    if not self.active_event then return end

    -- 事件结束提示
    for _, p in ipairs(AllPlayers or {}) do
        if p and p:IsValid() and p.components and p.components.talker then
            p.components.talker:Say("季风乱流结束了。", 3)
        end
    end

    -- 恢复世界颜色
    self:RestoreWorldTint()

    -- 发送事件结束通知，让所有宝珠消失
    TheWorld:PushEvent("seasonal_gust_ended")

    -- 同步事件结束状态到客户端
    self._net_active:set(false)
    self._net_season:set("")
    self._net_remaining:set(0)

    -- 清理更新任务
    if self.update_task then
        self.update_task:Cancel()
        self.update_task = nil
    end

    -- 清理活跃事件标记
    self.active_event = nil
end

function SeasonalGust:TriggerEvent()
    self:StartEvent()
end

function SeasonalGust:OnSeasonChanged(initial)
    if not TheWorld.ismastersim then return end

    -- 错误处理：检查实例有效性
    if not self.inst or not self.inst:IsValid() then
        print("[SeasonWorkshop] Error: Invalid gust manager instance in OnSeasonChanged")
        return
    end

    -- 读取配置：每季触发次数
    local per_season = _G.GetModConfigData("gust_frequency") or 2
    self.events_left = per_season

    -- 同步到客户端
    self._net_events_left:set(per_season)

    -- 清理已有计划
    for _, t in ipairs(self.scheduled) do
        if t and t.Cancel then
            t:Cancel()
        end
    end
    self.scheduled = {}

    -- 计划在本季触发事件，随机在当前季内的 [min,max] 天后触发
    local min_days = TUNING.SEASON_EVENT_MIN_DAYS or 8
    local max_days = TUNING.SEASON_EVENT_MAX_DAYS or 12
    local total_time = _G.TUNING.TOTAL_DAY_TIME or 480

    for i=1,per_season do
        local days = math.random(min_days, max_days)
        local delay = days * total_time * 0.25 -- 简化：缩短等待，加快测试节奏（1/4），后续可调为1.0
        local event_time = GetTime() + delay

        -- 记录下次事件时间（用于客户端显示）
        if i == 1 then
            self.next_event_time = event_time
            self._net_next_event_time:set(event_time)
        end

        local task = self.inst:DoTaskInTime(delay, function()
            if self.inst and self.inst:IsValid() then
                self:TriggerEvent()
            else
                print("[SeasonWorkshop] Warning: Gust manager became invalid during scheduled event")
            end
        end)

        if task then
            table.insert(self.scheduled, task)
        else
            print("[SeasonWorkshop] Error: Failed to schedule gust event")
        end
    end
end

-- 数据持久化：保存季风乱流管理器状态
function SeasonalGust:OnSave()
    local scheduled_times = {}
    for i, task in ipairs(self.scheduled) do
        if task and task.GetTimeLeft then
            local time_left = task:GetTimeLeft()
            if time_left > 0 then
                table.insert(scheduled_times, GetTime() + time_left)
            end
        end
    end

    return {
        enabled = self.enabled,
        events_left = self.events_left,
        next_event_time = self.next_event_time,
        scheduled_times = scheduled_times,
        active_event = self.active_event,
        original_colour = self.original_colour
    }
end

-- 数据持久化：加载季风乱流管理器状态
function SeasonalGust:OnLoad(data)
    if not TheWorld.ismastersim then return end

    if data then
        self.enabled = data.enabled ~= false -- 默认启用
        self.events_left = data.events_left or 0
        self.next_event_time = data.next_event_time or 0
        self.active_event = data.active_event
        self.original_colour = data.original_colour

        -- 同步到网络
        self._net_events_left:set(self.events_left)
        self._net_next_event_time:set(self.next_event_time)

        -- 如果有活跃事件，恢复世界着色
        if self.active_event and self.active_event.season then
            self._net_active:set(true)
            self._net_season:set(self.active_event.season)
            self:ApplyWorldTint(self.active_event.season)
        else
            self._net_active:set(false)
            self._net_season:set("")
        end

        -- 重新安排计划的事件
        if data.scheduled_times then
            local current_time = GetTime()
            for _, event_time in ipairs(data.scheduled_times) do
                local delay = event_time - current_time
                if delay > 0 then
                    local task = self.inst:DoTaskInTime(delay, function()
                        if self.inst and self.inst:IsValid() then
                            self:TriggerEvent()
                        end
                    end)
                    if task then
                        table.insert(self.scheduled, task)
                    end
                end
            end
        end

        print(string.format("[SeasonWorkshop] Seasonal gust manager loaded: %d events left, next at %.1f",
              self.events_left, self.next_event_time))
    end
end

-- 组件清理：确保资源正确释放
function SeasonalGust:OnRemoveFromEntity()
    -- 清理所有计划的任务
    for _, task in ipairs(self.scheduled) do
        if task and task.Cancel then
            task:Cancel()
        end
    end
    self.scheduled = {}

    -- 清理更新任务
    if self.update_task then
        self.update_task:Cancel()
        self.update_task = nil
    end

    -- 恢复世界颜色
    if self.active_event then
        self:RestoreWorldTint()
    end

    -- 清理网络变量引用
    self._net_active = nil
    self._net_season = nil
    self._net_remaining = nil
    self._net_events_left = nil
    self._net_next_event_time = nil

    print("[SeasonWorkshop] Seasonal gust manager cleaned up")
end

return SeasonalGust
