_G.ChinesePlus.RenameTab("Sleight", "魔术")

-- The character select screen lines
STRINGS.CHARACTER_TITLES.nelke = "斩首兔子"
STRINGS.CHARACTER_NAMES.nelke = "内尔克"
STRINGS.CHARACTER_DESCRIPTIONS.nelke = "*更疯狂、更凶猛\n*不能使用魔法工具, 但可以制造\n*什么也不做时会感到沮丧"
STRINGS.CHARACTER_QUOTES.nelke = "\"这是一个绝妙的主意！不是吗？\""

-- Custom speech strings
STRINGS.CHARACTERS.ESCTEMPLATE = nil -- require "speech_nelke"

-- The character's name as appears in-game
STRINGS.NAMES.ESCTEMPLATE = "内尔克"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.ESCTEMPLATE =
{
	GENERIC = "这是内尔克!",
	ATTACKER = "内尔克看起来狡猾的...",
	MURDERER = "凶手!",
	REVIVER = "内尔克, 鬼魂之友.",
	GHOST = "内尔克可以使用救赎之心.",
}

GLOBAL.STRINGS.NAMES.AXE_NELKE = "万能钥匙"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.AXE_NELKE = "换言之，这是\"火斧\"。"
_G.ChinesePlus.RenameRecipe("AXE_NELKE", "破坏一切！！")

GLOBAL.STRINGS.NAMES.SAW_THROWER = "旋转锯机"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.SAW_THROWER = "这很危险！！"
_G.ChinesePlus.RenameRecipe("SAW_THROWER", "让我们玩切割魔术！！")

GLOBAL.STRINGS.NAMES.BOMB_TELEPORTER = "烟雾弹"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.BOMB_TELEPORTER = "完美的平凡！！"
_G.ChinesePlus.RenameRecipe("BOMB_TELEPORTER", "让我们玩炸弹魔术！！")

GLOBAL.STRINGS.NAMES.BOMB_MAGIC = "炸弹魔箱"

GLOBAL.STRINGS.NAMES.HAT_NELKE = "小礼帽"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.HAT_NELKE = "完美的平凡！！"
_G.ChinesePlus.RenameRecipe("HAT_NELKE", "让我们施展魔术！！")

