_G.ChinesePlus.SetConfigTranslation(mod_to_c,
  "欢迎来到我的世界！",
  {
    ["On"] = "开",
    ["Off"] = "关",
  },
  {
    {
      name = "nightpen_state",
      label = "黑魅 开关",
      hover = "黑魅(夜视) 开启?",
    },

    {
      name = "nightpen_purplegem",
      label = "黑魅 # 紫宝石",
      hover = "制造 黑魅 需要 紫宝石 的数量。",
    },

    {
      name = "nightpen_durian",
      label = "黑魅 # 榴莲",
      hover = "制造 黑魅 需要 榴莲 的数量。",
    },

    {
      name = "nightpen_stingers",
      label = "黑魅 # 蜂刺",
      hover = "制造 黑魅 需要 蜂刺 的数量。",
    },

    {
      name = "nightpen_duration",
      label = "黑魅 持续时间",
      hover = "黑魅 的持续时间（天）。",
    },

    {
      name = "icepen_state",
      label = "深蓝 开关",
      hover = "深蓝(抵抗火焰) 开启?",
    },

    {
      name = "icepen_bluegem",
      label = "深蓝 # 蓝宝石",
      hover = "制造 黑魅 需要 蓝宝石 的数量。",
    },

    {
      name = "icepen_dforbc",
      label = "深蓝 - 火龙果/蓝菇头",
      hover = "你想用 蓝菇头 还是 火龙果 来制造 深蓝?",
      options =
      {
        {description = "蓝菇头", data = "blue_cap"},
        {description = "火龙果", data = "dragonfruit"},
      },
    },

    {
      name = "icepen_dforbcnum",
      label = "深蓝 # 火龙果/蓝菇头",
      hover = "制造 深蓝 需要 火龙果/蓝菇头 的数量。",
    },

    {
      name = "icepen_stingers",
      label = "深蓝 # 蜂刺",
      hover = "制造 深蓝 需要 蜂刺 的数量。",
    },

    {
      name = "icepen_duration",
      label = "深蓝 持续时间",
      hover = "深蓝 的持续时间（天）。",
    },

    {
      name = "speedpen_state",
      label = "迅捷 开关",
      hover = "迅捷(速度提升) 开启?",
    },

    {
      name = "pcount",
      label = "迅捷 # 莎草纸",
      hover = "制造 迅捷 需要 莎草纸 的数量。",
    },

    {
      name = "hcount",
      label = "迅捷 # 蜂蜜",
      hover = "制造 迅捷 需要 蜂蜜 的数量。",
    },

    {
      name = "scount",
      label = "迅捷 # 蜂刺",
      hover = "制造 迅捷 需要 蜂刺 的数量。",
    },

    {
      name = "speedx",
      label = "速度倍数",
      hover = "使用 迅捷 后的速度倍数。",
    },

    {
      name = "sanitypen_state",
      label = "灵鸽 开关",
      hover = "灵鸽(充满理智) 开启?",
    },

    {
      name = "sanitypen_bormm",
      label = "灵鸽 - 实验药/怪物肉",
      hover = "你想用 实验药 还是 怪物肉 来制造 灵鸽?",
      options =
      {
        {description = "实验药", data = "lifeinjector"},
        {description = "怪物肉", data = "monstermeat"},
      },
    },

    {
      name = "sanitypen_bormmnum",
      label = "灵鸽 # 实验药/怪物肉",
      hover = "制造 灵鸽 需要 实验药/怪物肉 的数量。",
    },

    {
      name = "sanitypen_cactusf",
      label = "灵鸽 # 仙人掌花",
      hover = "制造 灵鸽 需要 仙人掌花 的数量。",
    },

    {
      name = "sanitypen_stingers",
      label = "灵鸽 # 蜂刺",
      hover = "制造 灵鸽 需要 蜂刺 的数量。",
    },

    {
      name = "sanitypen_duration",
      label = "灵鸽 持续时间",
      hover = "灵鸽 的持续时间（天）。",
    },

    {
      name = "sightpen_state",
      label = "维他命R 开关",
      hover = "维他命R(精确提升) 开启?",
    },

    {
      name = "sightpen_redgems",
      label = "维他命R # 红宝石",
      hover = "制造 维他命R 需要 红宝石 的数量。",
    },

    {
      name = "sightpen_carrots",
      label = "维他命R # 胡萝卜",
      hover = "制造 维他命R 需要 胡萝卜 的数量。",
    },

    {
      name = "sightpen_stingers",
      label = "维他命R # 蜂刺",
      hover = "制造 维他命R 需要 蜂刺 的数量。",
    },

    {
      name = "sightpen_duration",
      label = "维他命R 持续时间",
      hover = "维他命R 的持续时间（天）。",
    },

  }
)
