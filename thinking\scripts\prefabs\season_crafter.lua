local MakePlayerCharacter = require "prefabs/player_common"

-- 使用Wickerbottom的资源作为模板
local assets = {
    <PERSON><PERSON>("ANIM", "anim/player_basic.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_idles_shiver.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_actions_axe.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_actions_pickaxe.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_actions_shovel.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_actions_blowdart.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_actions_eat.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_actions_item.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_actions_uniqueitem.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_actions_bugnet.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_actions_fishing.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_actions_boomerang.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_bush_hat.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_attacks.zip"),
    <PERSON><PERSON>("ANIM", "anim/player_idles.zip"),
    Asset("ANIM", "anim/player_rebirth.zip"),
    Asset("ANIM", "anim/player_jump.zip"),
    Asset("ANIM", "anim/player_amulet_resurrect.zip"),
    Asset("ANIM", "anim/player_teleport.zip"),
    Asset("ANIM", "anim/wilson_fx.zip"),
    Asset("ANIM", "anim/player_one_man_band.zip"),
    Asset("ANIM", "anim/shadow_hands.zip"),
    Asset("SOUND", "sound/sfx.fsb"),
    Asset("SOUND", "sound/wilson.fsb"),
    Asset("ANIM", "anim/beard.zip"),

    -- Wickerbottom特有资源
    Asset("ANIM", "anim/wickerbottom.zip"),
    Asset("SOUND", "sound/wickerbottom.fsb"),
}

local prefabs = {}

-- 初始物品：给季匠一些基础的季节相关物品
local start_inv = {
    "season_shard_spring",
    "season_shard_summer",
    "season_shard_autumn",
    "season_shard_winter",
}

-- 引用全局函数
local SpawnPrefab = GLOBAL.SpawnPrefab
local TUNING = GLOBAL.TUNING

local function SeasonFX(inst, data)
    if not data or not data.season then return end

    -- 音效
    if inst.SoundEmitter then
        inst.SoundEmitter:PlaySound("dontstarve/common/staff_coldlight")
    end

    -- 改色staffcastfx视觉效果
    local fx = SpawnPrefab("staff_castinglight")
    if fx then
        fx.Transform:SetPosition(inst.Transform:GetWorldPosition())

        -- 根据季节调整颜色（统一颜色方案）
        local season = data.season
        if season == "spring" then
            fx.AnimState:SetMultColour(0.5, 1.0, 0.5, 0.8) -- 春季绿色
        elseif season == "summer" then
            fx.AnimState:SetMultColour(1.0, 0.6, 0.2, 0.8) -- 夏季橙色
        elseif season == "autumn" then
            fx.AnimState:SetMultColour(0.8, 0.5, 0.2, 0.8) -- 秋季褐色
        elseif season == "winter" then
            fx.AnimState:SetMultColour(0.6, 0.8, 1.0, 0.8) -- 冬季蓝色
        end

        fx:DoTaskInTime(1.0, fx.Remove)
    end

    -- 角色提示文字
    if inst.components and inst.components.talker then
        local season_names = {
            spring = "春之刻印",
            summer = "夏之刻印",
            autumn = "秋之刻印",
            winter = "冬之刻印"
        }
        inst.components.talker:Say(season_names[data.season] or "季节刻印")
    end

    -- 更新持续的季节颜色提示
    UpdateSeasonVisual(inst)
end

-- 更新季节视觉效果
local function UpdateSeasonVisual(inst)
    if not inst.components.season_engraving then return end

    local season = inst.components.season_engraving:GetSeason()
    local is_manual = inst.components.season_engraving:IsManualSeason()

    -- 根据季节设置轻微的颜色调整
    local colors = {
        spring = {0.05, 0.1, 0.05, 0},    -- 轻微绿色调
        summer = {0.1, 0.05, 0, 0},       -- 轻微橙色调
        autumn = {0.08, 0.03, 0, 0},      -- 轻微褐色调
        winter = {0, 0.05, 0.1, 0}        -- 轻微蓝色调
    }

    local color = colors[season]
    if color and inst.AnimState then
        -- 如果是手动设置，颜色稍微更明显
        local intensity = is_manual and 1.5 or 1.0
        inst.AnimState:SetAddColour(
            color[1] * intensity,
            color[2] * intensity,
            color[3] * intensity,
            color[4]
        )
    end
end

local function common_postinit(inst)
    inst:AddTag("seasonal")
    -- 客户端用：最小化网络变量由组件同步
end

local function master_postinit(inst)
    -- 使用Wickerbottom的音效
    inst.soundsname = "wickerbottom"

    -- 基础属性
    inst.components.health:SetMaxHealth(150)
    inst.components.hunger:SetMax(150)
    inst.components.hunger:SetRate(TUNING.WILSON_HUNGER_RATE)
    inst.components.sanity:SetMax(200)

    -- 季节被动组件（完全负责所有季节效果）
    inst:AddComponent("season_engraving")
    inst:ListenForEvent("season_engraving_fx", SeasonFX)

    -- 优化：降低视觉更新频率，减少性能消耗
    inst:DoPeriodicTask(3, function()
        UpdateSeasonVisual(inst)
    end)

    -- 添加季节状态快捷查看功能（按键绑定或命令）
    inst.ShowSeasonStatus = function()
        if inst.components.season_engraving then
            local current_season = inst.components.season_engraving:GetSeason()
            local world_season = TheWorld.state.season
            local is_manual = inst.components.season_engraving:IsManualSeason()

            local season_names = {
                spring = "春季",
                summer = "夏季",
                autumn = "秋季",
                winter = "冬季"
            }

            local status_msg = "季节刻印：" .. (season_names[current_season] or "未知")
            if is_manual then
                status_msg = status_msg .. "（手动）"
            else
                status_msg = status_msg .. "（自动）"
            end

            if world_season ~= current_season then
                status_msg = status_msg .. " | 世界：" .. (season_names[world_season] or "未知")
            end

            if inst.components.talker then
                inst.components.talker:Say(status_msg, 3)
            end

            return status_msg
        end
        return "无季节刻印"
    end

    -- 添加季节状态检查功能
    inst:AddComponent("inspectable")
    inst.components.inspectable.getstatus = function(inst)
        if inst.components.season_engraving then
            local current_season = inst.components.season_engraving:GetSeason()
            local world_season = TheWorld.state.season
            local is_manual = inst.components.season_engraving:IsManualSeason()

            local season_names = {
                spring = "春季",
                summer = "夏季",
                autumn = "秋季",
                winter = "冬季"
            }

            local status = "季节刻印：" .. (season_names[current_season] or "未知")

            if is_manual then
                status = status .. "（手动设置）"
            else
                status = status .. "（跟随世界）"
            end

            if world_season ~= current_season then
                status = status .. "\n世界季节：" .. (season_names[world_season] or "未知")
            end

            return status
        end
        return nil
    end

    -- 冬季近战减速需要的攻击监听
    inst:ListenForEvent("onattackother", function(inst, data)
        if data and data.target and inst.components.season_engraving then
            local season = inst.components.season_engraving:GetSeason()
            if season == "winter" then
                -- 冰缓打击：给目标施加减速
                if not data.target.components.seasonal_debuff then
                    data.target:AddComponent("seasonal_debuff")
                end
                local is_boss = data.target:HasTag("epic")
                local mult = is_boss and (TUNING.SEASON_CRAFTER_WINTER_BOSS_SLOW_MULT or 0.85) or (TUNING.SEASON_CRAFTER_WINTER_SLOW_MULT or 0.8)
                local dur = is_boss and (TUNING.SEASON_CRAFTER_WINTER_BOSS_SLOW_DURATION or 1.5) or (TUNING.SEASON_CRAFTER_WINTER_SLOW_DURATION or 2.0)
                data.target.components.seasonal_debuff:ApplySlow(mult, dur, false)
            end
        end
    end)
end

return MakePlayerCharacter("season_crafter", prefabs, assets, common_postinit, master_postinit, start_inv)
