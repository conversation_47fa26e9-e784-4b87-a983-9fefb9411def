GLOBAL.STRINGS.CHARACTER_TITLES.medic = "日耳曼医生"
GLOBAL.STRINGS.CHARACTER_NAMES.medic = "医疗兵"
GLOBAL.STRINGS.CHARACTER_DESCRIPTIONS.medic = "*有被动的生命回复\n*可以制作各种锯子\n*有他自己的治疗方法"
GLOBAL.STRINGS.CHARACTER_QUOTES.medic = "\"准备好接受诊疗吧\""
GLOBAL.STRINGS.CHARACTERS.MEDIC = nil

GLOBAL.STRINGS.NAMES.MEDIC = "医疗兵"

--Bonesaw
--GLOBAL.STRINGS.CHARACTERS.MEDIC.DESCRIBE.BONESAW = "All I can tell you about this next procedure is that it will be... excruciating!"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.BONESAW = "多么科学！"

GLOBAL.STRINGS.CHARACTERS.WILLOW.DESCRIBE.BONESAW = nil
GLOBAL.STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.BONESAW = nil
GLOBAL.STRINGS.CHARACTERS.WENDY.DESCRIBE.BONESAW = nil
GLOBAL.STRINGS.CHARACTERS.WX78.DESCRIBE.BONESAW = nil
GLOBAL.STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.BONESAW = nil
GLOBAL.STRINGS.CHARACTERS.WOODIE.DESCRIBE.BONESAW = nil
GLOBAL.STRINGS.CHARACTERS.WAXWELL.DESCRIBE.BONESAW = nil

GLOBAL.STRINGS.NAMES.BONESAW = "锯子"
_G.ChinesePlus.RenameRecipe("BONESAW", "锯的一手好骨头！")

-- Übersaw
--GLOBAL.STRINGS.CHARACTERS.MEDIC.DESCRIBE.UBERSAW = "I'm going to saw through your bones!"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.UBERSAW = "我现在可以从别人的痛苦中获益！"

GLOBAL.STRINGS.CHARACTERS.WILLOW.DESCRIBE.UBERSAW = nil
GLOBAL.STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.UBERSAW = nil
GLOBAL.STRINGS.CHARACTERS.WENDY.DESCRIBE.UBERSAW = nil
GLOBAL.STRINGS.CHARACTERS.WX78.DESCRIBE.UBERSAW = nil
GLOBAL.STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.UBERSAW = nil
GLOBAL.STRINGS.CHARACTERS.WOODIE.DESCRIBE.UBERSAW = nil
GLOBAL.STRINGS.CHARACTERS.WAXWELL.DESCRIBE.UBERSAW = nil

GLOBAL.STRINGS.NAMES.UBERSAW = "超能骨锯"
_G.ChinesePlus.RenameRecipe("UBERSAW", "每个命中都会获得生命。")

-- Amputator
--GLOBAL.STRINGS.CHARACTERS.MEDIC.DESCRIBE.AMPUTATOR = "Hoo, hoo! Perfect!"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.AMPUTATOR = "刺痛意味着它在工作！"

GLOBAL.STRINGS.CHARACTERS.WILLOW.DESCRIBE.AMPUTATOR = nil
GLOBAL.STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.AMPUTATOR = nil
GLOBAL.STRINGS.CHARACTERS.WENDY.DESCRIBE.AMPUTATOR = nil
GLOBAL.STRINGS.CHARACTERS.WX78.DESCRIBE.AMPUTATOR = nil
GLOBAL.STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.AMPUTATOR = nil
GLOBAL.STRINGS.CHARACTERS.WOODIE.DESCRIBE.AMPUTATOR = nil
GLOBAL.STRINGS.CHARACTERS.WAXWELL.DESCRIBE.AMPUTATOR = nil

GLOBAL.STRINGS.NAMES.AMPUTATOR = "截肢锯"
_G.ChinesePlus.RenameRecipe("AMPUTATOR", "回复附近玩家的生命。")

-- Vita-saw
--GLOBAL.STRINGS.CHARACTERS.MEDIC.DESCRIBE.VITASAW = nil
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.VITASAW = "自己注意；不要把这对着我的眼睛。"

GLOBAL.STRINGS.CHARACTERS.WILLOW.DESCRIBE.VITASAW = nil
GLOBAL.STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.VITASAW = nil
GLOBAL.STRINGS.CHARACTERS.WENDY.DESCRIBE.VITASAW = nil
GLOBAL.STRINGS.CHARACTERS.WX78.DESCRIBE.VITASAW = nil--"MEDICAL ANOMALY DETECTED"
GLOBAL.STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.VITASAW = nil--"It doesn't seem to have much of a purpose."
GLOBAL.STRINGS.CHARACTERS.WOODIE.DESCRIBE.VITASAW = nil--"An axe could do it's job better."
GLOBAL.STRINGS.CHARACTERS.WAXWELL.DESCRIBE.VITASAW = nil--"Nothing useful."

GLOBAL.STRINGS.NAMES.VITASAW = "还魂锯"
_G.ChinesePlus.RenameRecipe("VITASAW", "装备了它的话，死亡就会和你在一起。")

-- Crusader's Crossbow
--GLOBAL.STRINGS.CHARACTERS.MEDIC.DESCRIBE.BLOWDART_HEALTH = "Well, it's familiar to what I used to use. It will do well for now."
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.BLOWDART_HEALTH = "这似乎从根本上是安全的。"

GLOBAL.STRINGS.CHARACTERS.WILLOW.DESCRIBE.BLOWDART_HEALTH = nil--"I don't think healing is the same of burning."
GLOBAL.STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.BLOWDART_HEALTH = nil--"Dart do health to enemies."
GLOBAL.STRINGS.CHARACTERS.WENDY.DESCRIBE.BLOWDART_HEALTH = nil--"I blow on one end and others gain benefit."
GLOBAL.STRINGS.CHARACTERS.WX78.DESCRIBE.BLOWDART_HEALTH = nil--"THIS DOES NOT HELP ME IN ANYWAY."
GLOBAL.STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.BLOWDART_HEALTH = nil--"Improvised remedial device."
GLOBAL.STRINGS.CHARACTERS.WOODIE.DESCRIBE.BLOWDART_HEALTH = nil--"This seems a bit passive."
GLOBAL.STRINGS.CHARACTERS.WAXWELL.DESCRIBE.BLOWDART_HEALTH = nil--"A glorious tube of health-giving!"

GLOBAL.STRINGS.NAMES.BLOWDART_HEALTH = "生命吹箭"
_G.ChinesePlus.RenameRecipe("BLOWDART_HEALTH", "在你的朋友和敌人身上吐口生命。")

--Med-kits
GLOBAL.STRINGS.NAMES.MEDKIT_SMALL = "小医疗包"
_G.ChinesePlus.RenameRecipe("MEDKIT_SMALL", "尝起来像生命。")

--GLOBAL.STRINGS.CHARACTERS.MEDIC.DESCRIBE.MEDKIT_SMALL = "Come over here! I promise I will heal you!"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.MEDKIT_SMALL = "刺痛是我最喜欢的。"
GLOBAL.STRINGS.CHARACTERS.WILLOW.DESCRIBE.MEDKIT_SMALL = nil --"Eww, I hate medicine!"
GLOBAL.STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.MEDKIT_SMALL = nil --"Tiny bottle fix me!"
GLOBAL.STRINGS.CHARACTERS.WENDY.DESCRIBE.MEDKIT_SMALL = nil --"It can't heal every wound."
GLOBAL.STRINGS.CHARACTERS.WX78.DESCRIBE.MEDKIT_SMALL = nil --"TEMPORARY MAINTENANCE DEVICE"
GLOBAL.STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.MEDKIT_SMALL = nil --"This tiny canister contains medicinal properties."
GLOBAL.STRINGS.CHARACTERS.WOODIE.DESCRIBE.MEDKIT_SMALL = nil --"At least I didn't have to pay for it."
GLOBAL.STRINGS.CHARACTERS.WAXWELL.DESCRIBE.MEDKIT_SMALL = nil --"I don't remember allowing this."

GLOBAL.STRINGS.NAMES.MEDKIT_MED = "中医疗包"
_G.ChinesePlus.RenameRecipe("MEDKIT_MED", "治愈你的朋友！")

GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.MEDKIT_MED = "科学的医疗工具！"
GLOBAL.STRINGS.CHARACTERS.WILLOW.DESCRIBE.MEDKIT_MED = nil --"I still hate medicine!"
GLOBAL.STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.MEDKIT_MED = nil --"Box make Wolfgang strong!"
GLOBAL.STRINGS.CHARACTERS.WENDY.DESCRIBE.MEDKIT_MED = nil --"The tools in it can't help bring Abigail back."
GLOBAL.STRINGS.CHARACTERS.WX78.DESCRIBE.MEDKIT_MED = nil --"I AM CONFLICTED"
GLOBAL.STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.MEDKIT_MED = nil --"This medicinal case has many curative objects contained in it."
GLOBAL.STRINGS.CHARACTERS.WOODIE.DESCRIBE.MEDKIT_MED = nil --"From your friendly neighbor-hood doctor."
GLOBAL.STRINGS.CHARACTERS.WAXWELL.DESCRIBE.MEDKIT_MED = nil --"Do not let anyone take it from my grasps."

GLOBAL.STRINGS.NAMES.MEDKIT_LARGE = "大医疗包"
_G.ChinesePlus.RenameRecipe("MEDKIT_LARGE", "一次性补满你的生命。")

GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.MEDKIT_LARGE = "它装满了医疗工具！"
GLOBAL.STRINGS.CHARACTERS.WILLOW.DESCRIBE.MEDKIT_LARGE = nil --"There's gotta be some good burning tools in here!"
GLOBAL.STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.MEDKIT_LARGE = nil --"Large box is strong like me!"
GLOBAL.STRINGS.CHARACTERS.WENDY.DESCRIBE.MEDKIT_LARGE = nil --"You can't heal everything, box."
GLOBAL.STRINGS.CHARACTERS.WX78.DESCRIBE.MEDKIT_LARGE = nil --"YOU ARE MINE"
GLOBAL.STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.MEDKIT_LARGE = nil --"This large case of medicinal tools will help supply with a fine amount of health."
GLOBAL.STRINGS.CHARACTERS.WOODIE.DESCRIBE.MEDKIT_LARGE = nil --"Now this is a health kit!"
GLOBAL.STRINGS.CHARACTERS.WAXWELL.DESCRIBE.MEDKIT_LARGE = nil --"Give this to those who deserve it."

GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.MEDIC =
{
  GENERIC = "你好, %s!",
  ATTACKER = "那医生看起来鬼鬼祟祟的...",
  MURDERER = "凶手!",
  REVIVER = "%s, 不要辜负他们的名字。",
  GHOST = "更好的找个复活的装置。这儿不能没有医生。",
  FIRESTARTER = "燃烧它不是很科学, %s.",
}
--More characters
--[[
GLOBAL.STRINGS.CHARACTERS.MEDIC.DESCRIBE.MEDIC =
{
  GENERIC = "We have so much power!",
  ATTACKER = "%s, I see you have chosen battle Medic today!",
  MURDERER = "%s! Experiment first, kill later!",
  REVIVER = "Danke, Herr %s!",
  GHOST = "Well that's no good. I shall have heals ready for you in a moment!",
  FIRESTARTER = "That is not your area of expertise!",
}
]]

GLOBAL.STRINGS.CHARACTERS.WILLOW.DESCRIBE.MEDIC = nil
GLOBAL.STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.MEDIC = nil
GLOBAL.STRINGS.CHARACTERS.WENDY.DESCRIBE.MEDIC = nil
GLOBAL.STRINGS.CHARACTERS.WX78.DESCRIBE.MEDIC = nil
GLOBAL.STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.MEDIC = nil
GLOBAL.STRINGS.CHARACTERS.WOODIE.DESCRIBE.MEDIC = nil
GLOBAL.STRINGS.CHARACTERS.WAXWELL.DESCRIBE.MEDIC = nil
GLOBAL.STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.MEDIC = nil
GLOBAL.STRINGS.CHARACTERS.WEBBER.DESCRIBE.MEDIC = nil

