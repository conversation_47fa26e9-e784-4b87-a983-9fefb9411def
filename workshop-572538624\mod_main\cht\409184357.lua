STRINGS.NAMES.ZOROSWORDMOUTH = "和道一文字"
STRINGS.NAMES.SANDAI = "三代鬼徹"
STRINGS.NAMES.SHUSUI = "秋水"
STRINGS.NAMES.ZOROSHEATH = "索隆的劍鞘"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.ZOROSWORDMOUTH= "一把劍在劍客有很多的情感價值。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SHUSUI= "只有傳說中的人可以持有這樣的劍。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SANDAI= "只有少數人戰勝詛咒生活了下去。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.ZOROSHEATH= "它擁有三種特殊的劍，每一個都有自己的特點。"
-- The character select screen lines
STRINGS.CHARACTER_TITLES.zoro = "世界上最偉大的劍客"
STRINGS.CHARACTER_NAMES.zoro = "索隆"
STRINGS.CHARACTER_DESCRIPTIONS.zoro = "*強大的攻擊\n*劍有多種用途\n*沒有方向感"
STRINGS.CHARACTER_QUOTES.zoro = "\"背上的傷痕是劍士的恥辱\""

-- Custom speech strings
--STRINGS.CHARACTERS.ZORO = require(chinesefolder.."/Zoro/speech_zoro")
STRINGS.CHARACTERS.ZORO = nil

-- The character's name as appears in-game
STRINGS.NAMES.ZORO = "索隆"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.ZORO =
{
  GENERIC = "這是索隆！",
  ATTACKER = "索隆看起來很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "索隆, 鬼魂朋友！",
  GHOST = "索隆可以使用一顆心。",
}

