_G.ChinesePlus.SetConfigTranslation(mod_to_c, [[
在地圖上面顯示更多的圖示:								 當前版本: ]].."漢化無法顯示"..[[

- 植物群落
- 動物區域
- 菌類
- 怪異的東西
- 建築
- ...
- 等等!


相容 "Minimap HUD（小地圖）"

( 不要忘了調整選項來獲得更好的體驗。 )]],
	{

	},
	{


		{name = "Disp_rabbithole", label = "兔子洞", hover = "prefab: rabbithole",	options =	{ {description = "隱藏", data = "N" }, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_rabbit", label = "兔子", hover = "prefab: rabbit",	options =	{ {description = "隱藏", data = "N", hover = "" }, {description = "	顯示 *", data = "Y", hover = "僅在可視範圍內" }, },	},

		{name = "Disp_molehill", label = "鼴鼠窩", hover = "prefab: molehill", options =	{ {description = "隱藏", data = "N" }, {description = "顯示", data = "Y" }, },	},

		{name = "Disp_mole", label = "鼴鼠", hover = "prefab: mole",	options =	{ {description = "隱藏", data = "N", hover = "" }, {description = "	顯示 *", data = "Y", hover = "僅在可視範圍內" }, },	},

		{name = "Disp_gekko", label = "草蜥", hover = "prefab: grassgekko",	options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y" }, },	},

		{name = "Disp_catcoons", label = "貓熊", hover = "(會顯示貓熊的家)\nprefabs: catcoon, catcoonden",	options =	{ {description = "隱藏", data = "N" }, {description = "	顯示 *", data = "Y", hover = "僅在可視範圍內" }, },	},

		{name = "Disp_monkey", label = "暴躁猴", hover = "(會顯示暴躁猴的家)\nprefabs: monkey, monkeybarrel", options =	{ {description = "隱藏", data = "N" }, {description = "	顯示 *", data = "Y", hover = "僅在可視範圍內" }, },	},

		{name = "Disp_penguin", label = "企鵝", hover = "(會顯示對接地面)\nprefabs: penguin, penguin_ice", options =	{ {description = "隱藏", data = "N" }, {description = "	顯示 *", data = "Y", hover = "僅在可視範圍內" }, },	},

		{name = "Disp_perd", label = "火雞", hover = "prefab: perd", options =	{ {description = "隱藏", data = "N", hover = "" }, {description = "	顯示 *", data = "Y", hover = "僅在可視範圍內" }, },	},

		{name = "Disp_slurtle", label = "含糊蟲、黏糊蟲", hover = "prefabs: slurtle, snurtle", options =	{ {description = "隱藏", data = "N", hover = "" }, {description = "	顯示 *", data = "Y", hover = "僅在可視範圍內" }, },	},

		{name = "Disp_bird", label = "鳥", hover = "烏鴉, 紅雀，藍雀\nprefabs: crow, robin, robin_winter", options =	{ {description = "隱藏", data = "N", hover = "" }, {description = "	顯示 *", data = "Y", hover = "僅在可視範圍內" }, },	},

		{name = "Disp_bee", label = "蜜蜂、殺人蜂", hover = "prefabs: bee, killerbee", options =	{ {description = "隱藏", data = "N", hover = "" }, {description = "	顯示 *", data = "Y", hover = "僅在可視範圍內" }, },	},

		{name = "Disp_butterfly", label = "蝴蝶", hover = "prefab: butterfly", options =	{ {description = "隱藏", data = "N", hover = "" }, {description = "	顯示 *", data = "Y", hover = "僅在可視範圍內" }, },	},

		{name = "Disp_fireflies", label = "螢火蟲", hover = "prefab: fireflies", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},



		{name = "Disp_pigman", label = "豬人、豬人守衛", hover = "prefabs: pigman, pigguard",	options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y" }, },	},

		{name = "Disp_bunnyman", label = "兔人", hover = "prefab: bunnyman",	options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y" }, },	},

		{name = "Disp_beefalo", label = "牛", hover = "prefabs: beefalo, babybeefalo", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_koalefant", label = "大象、坐狼..", hover = "prefabs: koalefant_*, spat, warg ", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_tracks", label = "動物足跡", hover = "prefabs: animal_track, dirtpile", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_lightninggoat", label = "電羊", hover = "prefabs: lightninggoat, mother_goat", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_krampus", label = "坎普斯", hover = "prefab: krampuss", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},



		{name = "Disp_giants", label = "巨人", hover = "prefabs: moose, mooseegg, mossling, bearger, deerclops", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_dragonfly", label = "龍蠅", hover = "prefab: dragonfly", options =	{ {description = "隱藏", data = "N" }, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_treeguard", label = "樹精守衛", hover = "prefabs: leif, birchnutdrake, leif_sparse", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_clockwork", label = "發條怪物", hover = "prefabs: bishop, knight, rook, bishop_nightmare, knight_nightmare, rook_nightmare", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_nightmare", label = "影怪", hover = "prefabs: crawlinghorror, crawlingnightmare, nightmarebeak, terrorbeak", options =	{ {description = "隱藏", data = "N", hover = "" }, {description = "	顯示 *", data = "Y", hover = "僅在可視範圍內" }, },	},

		{name = "Disp_hound", label = "獵犬", hover = "prefabs: hound, firehound, icehound", options =	{ {description = "隱藏", data = "N", hover = "" }, {description = "	顯示 *", data = "Y", hover = "僅在可視範圍內" }, },	},

		{name = "Disp_spider", label = "蜘蛛", hover = "(還會顯示所有蜘蛛的巢)\nprefabs: spiderden, spider, spider_dropper, spider_hider, spider_spitter, spider_warrior, dropperweb", options =	{ {description = "隱藏", data = "N", hover = "" }, {description = "	顯示 *", data = "Y", hover = "僅在可視範圍內" }, },	},

		{name = "Disp_spiderqueen", label = "蜘蛛女皇", hover = "prefab: spiderqueen", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_tallbird", label = "高鳥", hover = "prefabs: tallbird, smallbird, teenbird", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_tentacle", label = "觸手", hover = "prefabs: tentacle, tentacle_pillar_arm", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_bigtentacle", label = "大觸手", hover = "(洞穴裡面的大觸手)\nprefabs: tentacle_pillar, tentacle_pillar_hole", options =	{ {description = "預設", data = "N", hover = "推薦圖示\n(無論是否摧毀都顯示相同圖示)" }, {description = "顯示", data = "Y", hover = "備用圖示"}, },	},

		{name = "Disp_walrus", label = "海象", hover = "prefabs: walrus, little_walrus", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_bat", label = "蝙蝠", hover = "prefab: bat", options =	{ {description = "隱藏", data = "N", hover = ""}, {description = "	顯示 *", data = "Y", hover = "僅在可視範圍內" }, },	},

		{name = "Disp_minotaur", label = "遠古守護者", hover = "prefabs: minotaur, rocky", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_slurper", label = "綴食者", hover = "prefab: slurper", options =	{ {description = "隱藏", data = "N", hover = ""}, {description = "	顯示 *", data = "Y", hover = "僅在可視範圍內" }, },	},

		{name = "Disp_worm", label = "洞穴蠕蟲", hover = "prefab: worm", options =	{ {description = "隱藏", data = "N"}, {description = "	顯示 *", data = "Y", hover = "僅在可視範圍內" }, },	},

		{name = "Disp_eyeplant", label = "眼樹", hover = "prefabs: eyeplant, lureplant", options =	{ {description = "隱藏", data = "N"}, {description = "	顯示 *", data = "Y", hover = "僅在可視範圍內" }, },	},

		{name = "Disp_frog", label = "青蛙", hover = "prefab: frog", options =	{ {description = "隱藏", data = "N", hover = ""}, {description = "	顯示 *", data = "Y", hover = "僅在可視範圍內" }, },	},

		{name = "Disp_mosquito", label = "蚊子", hover = "prefab: mosquito", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_merm", label = "魚人", hover = "prefab: merm", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_ghost", label = "鬼魂", hover = "prefab: ghost", options =	{ {description = "隱藏", data = "N", hover = "OoOOh?"}, {description = "	顯示 *", data = "Y", hover = "僅在可視範圍內" }, },	},





		{name = "Disp_mushroom", label = "蘑菇", hover = "prefabs: blue_mushroom, red_mushroom, green_mushroom", options =	{{description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_planted", label = "未採摘胡蘿蔔/曼德拉草", hover = "prefabs: carrot_planted, mandrake_planted", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_mandrake", label = "曼德拉草", hover = "prefab: mandrake, mandrake_active", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_flowers", label = "花", hover = "prefabs: flower, flower_evil, cave_fern", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_flower_cave", label = "熒光草", hover = "prefabs: flower_cave_double, flower_cave_triple", options =	{ {description = "預設", data = "N", hover = "推薦圖示\n(所有熒光草顯示單果熒光草)"}, {description = "顯示", data = "Y", hover="備用圖示"}, },	},

		{name = "Disp_glowberry", label = "發光漿果", hover = "prefab: wormlight_plant", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_marsh_bush", label = "尖銳的灌木叢", hover = "prefab: marsh_bush", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},





		{name = "Disp_wall", label = "牆", hover = "prefabs: wall_hay,wall_moonrock,wall_ruins,wall_stone,wall_wood", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_abigail", label = "阿比蓋爾", hover = "(溫蒂死去的雙胞胎姐姐)\nprefab: abigail", options =	{ {description = "隱藏", data = "N", hover = ""}, {description = "	顯示 *", data = "Y", hover = "僅在可視範圍內" }, },	},

		{name = "Disp_chester_eyebone", label = "切斯特的眼骨", hover = "prefab: eyebone", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_bone", label = "骨頭", hover = "prefabs: houndbone,scorched_skeleton,skeleton,skeleton_player", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_waxwelljournal", label = "暗影", hover = "prefab: waxwelljournal", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_gravestone", label = "墳墓、墓碑", hover = "prefabs: mound, gravestone", options =	{ {description = "預設", data = "N", hover = "推薦圖示"}, {description = "顯示", data = "Y", hover = "都顯示墳墓"}, },	},

		{name = "Disp_rock2", label = "金礦石", hover = "prefab: rock2", options =	{ {description = "預設", data = "N", hover = "推薦圖示\n(和普通礦石一樣)"}, {description = "顯示", data = "Y", hover = "備用圖示"}, },	},

		{name = "Disp_gold", label = "金塊", hover = "prefab: goldnugget", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_gems", label = "寶石", hover = "prefabs: redgem,bluegem,purplegem,orangegem,yellowhgem,greengem", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_gears", label = "齒輪", hover = "prefab: gears", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_flint", label = "燧石", hover = "prefab: flint", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_marble", label = "大理石", hover = "prefab: marble", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_rocks", label = "礦石", hover = "prefab: rocks", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_nitre", label = "硝石", hover = "prefab: nitre", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_nightmarefuel", label = "噩夢燃料", hover = "prefab: nightmarefuel", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_teleportato", label = "\"那些東西\"", hover = "prefabs: teleportato_box,teleportato_crank,teleportato_potato,teleportato_ring", options =	{ {description = "隱藏", data = "N", hover = "聯機版不存在"}, {description = "顯示", data = "Y", hover = "聯機版不存在"}, },	},

		{name = "Disp_altar", label = "遠古遺蹟", hover = "prefabs: ancient_altar, ancient_altar_broken", options =	{ {description = "預設", data = "N", hover = "推薦圖示\n(無論是否破損都顯示一樣)"}, {description = "顯示", data = "Y", hover = "備用圖示"}, },	},

		{name = "Disp_fissure", label = "裂縫", hover = "prefabs: fissure, fissure_lower", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_pighead", label = "豬人頭", hover = "prefab: pighead", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

		{name = "Disp_poop", label = "便便", hover = "prefabs: poop, guano", options =	{ {description = "隱藏", data = "N"}, {description = "顯示", data = "Y"}, },	},

	}
)
