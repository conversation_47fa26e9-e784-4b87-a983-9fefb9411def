-- Use the "ForceEnableMod" function when developing a mod. This will cause the
-- game to load the mod every time no matter what, saving you the trouble of
-- re-enabling it from the main menu.
--
-- Note! You shout NOT do this for normal mod loading. Please use the Mods menu
-- from the main screen instead.

--ForceEnableMod("kioskmode_dst")

-- Use "EnableModDebugPrint()" to show extra information during startup.

--EnableModDebugPrint()

-- Use "EnableModError()" to make the game more strict and crash on bad mod practices.

--EnableModError()

-- Use "DisableModDisabling()" to make the game stop disabling your mods when the game crashes

--DisableModDisabling()

-- Use "DisableLocalModWarning()" to make the game stop warning you when enabling local mods.

--DisableLocalModWarning()