-- The character select screen lines
STRINGS.CHARACTER_TITLES.windkind = "空想家"
STRINGS.CHARACTER_NAMES.windkind = "风之女"
STRINGS.CHARACTER_DESCRIPTIONS.windkind = "*没有很多伤害\n*有很高的精神\n*不容易饿"
STRINGS.CHARACTER_QUOTES.windkind = "\"去风吹的地方\""

-- Custom speech strings
--STRINGS.CHARACTERS.WINDKIND = require(chinesefolder.."/Windkind/speech_windkind")
STRINGS.CHARACTERS.WINDKIND = nil

-- The character's name as appears in-game
STRINGS.NAMES.WINDKIND = "风之女"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.WINDKIND =
{
  GENERIC = "这是风之女",
  ATTACKER = "风之女看起来很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "风之女, 鬼魂朋友！",
  GHOST = "风之女可以使用一颗心。",
}

