STRINGS.NAMES.NINJASWORD = "忍者刀"
STRINGS.NAMES.THROW = "爆裂飛鏢"
_G.ChinesePlus.RenameRecipe("THROW", "一個可以製作和扔的飛鏢！")

-- The character select screen lines
STRINGS.CHARACTER_TITLES.sonic = "Z市的忍者"
STRINGS.CHARACTER_NAMES.sonic = "音速索尼克"
STRINGS.CHARACTER_DESCRIPTIONS.sonic = "*有飛鏢X忍者刀\n*音速\n*高的飢餓速度和低的狀態"
STRINGS.CHARACTER_QUOTES.sonic = "\" 我將用我的終極忍術結束你！\""

-- Custom speech strings
--STRINGS.CHARACTERS.SONIC = require(chinesefolder.."/Sonic/speech_sonic")
STRINGS.CHARACTERS.SONIC = nil

-- The character's name as appears in-game
STRINGS.NAMES.SONIC = "索尼克"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SONIC =
{
  GENERIC = "它的索尼克！我的意思是，音速索尼克",
  ATTACKER = "索尼克看起來很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "索尼克, 鬼魂朋友！",
  GHOST = "索尼克可以使用一顆心。",
}

