STRINGS.NAMES.CACTUSMACE = "仙人掌拍"
_G.ChinesePlus.RenameRecipe("CACTUSMACE", "它会更多地伤害他们。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CACTUSMACE = "一个尖锐的拍子。"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.CACTUSMACE = "哎哟！我弄伤了手指。"
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.CACTUSMACE = "锋利的尖刺！"
STRINGS.CHARACTERS.WENDY.DESCRIBE.CACTUSMACE = "我会伤害自己的。"
STRINGS.CHARACTERS.WX78.DESCRIBE.CACTUSMACE = "它会伤害我和我的敌人"
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.CACTUSMACE = "一棵仙人掌上覆盖着锋利的尖刺。"
STRINGS.CHARACTERS.WOODIE.DESCRIBE.CACTUSMACE = "看起来很痛！"
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.CACTUSMACE = "这样会很有伤害力！"
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.CACTUSMACE = "凡是没能击垮你的，都使你变得更强！"
STRINGS.CHARACTERS.WEBBER.DESCRIBE.CACTUSMACE = "嗯，可怕的东西。"

STRINGS.NAMES.DARK_AXE = "黑暗之斧"
_G.ChinesePlus.RenameRecipe("DARK_AXE", "用梦魇来砍树。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.DARK_AXE = "那是一把可怕的斧头。"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.DARK_AXE = "我的梦可以砍树！"
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.DARK_AXE = "奇怪的鬼斧。"
STRINGS.CHARACTERS.WENDY.DESCRIBE.DARK_AXE = "树的噩梦。"
STRINGS.CHARACTERS.WX78.DESCRIBE.DARK_AXE = "砍树工具故障。"
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.DARK_AXE = "外质使这个工具显得半透明。"
STRINGS.CHARACTERS.WOODIE.DESCRIBE.DARK_AXE = "酷和前卫。这就是现在孩子们喜欢的，不是吗？"
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.DARK_AXE = "这是我信赖的斧头。"
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.DARK_AXE = "勇敢地砍！"
STRINGS.CHARACTERS.WEBBER.DESCRIBE.DARK_AXE = "砍啊砍啊~"

STRINGS.NAMES.DARK_PICKAXE = "黑暗之镐"
_G.ChinesePlus.RenameRecipe("DARK_PICKAXE", "破碎岩石的恶梦。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.DARK_PICKAXE = "这是一个可怕的镐。"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.DARK_PICKAXE = "我的梦可以粉碎岩石。"
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.DARK_PICKAXE = "奇怪的是鬼魂镐。"
STRINGS.CHARACTERS.WENDY.DESCRIBE.DARK_PICKAXE = "岩石的噩梦。"
STRINGS.CHARACTERS.WX78.DESCRIBE.DARK_PICKAXE = "挖掘工具故障。"
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.DARK_PICKAXE = "外质使这个工具显得半透明。"
STRINGS.CHARACTERS.WOODIE.DESCRIBE.DARK_PICKAXE = "酷和前卫。这就是现在孩子们喜欢的，不是吗？"
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.DARK_PICKAXE = "它用来挖掘较小的工作。"
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.DARK_PICKAXE = "勇敢地挖！"
STRINGS.CHARACTERS.WEBBER.DESCRIBE.DARK_PICKAXE = "它是半透明的，但非常硬。"

STRINGS.NAMES.EWECUSHAT = "骑士头盔"
_G.ChinesePlus.RenameRecipe("EWECUSHAT", "钢制的。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.EWECUSHAT = "耐用的头盔。"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.EWECUSHAT = "足够的火仍然会融化它."
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.EWECUSHAT = "强壮男人的强大帽子"
STRINGS.CHARACTERS.WENDY.DESCRIBE.EWECUSHAT = "它保护我脆弱的身体。"
STRINGS.CHARACTERS.WX78.DESCRIBE.EWECUSHAT = "一顶由金属制成的帽子。"
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.EWECUSHAT = "一件耐用的护甲。"
STRINGS.CHARACTERS.WOODIE.DESCRIBE.EWECUSHAT = "很好的头盔。"
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.EWECUSHAT = "看起来不错，用起来也不错。"
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.EWECUSHAT = "一顶适合战士的帽子。"
STRINGS.CHARACTERS.WEBBER.DESCRIBE.EWECUSHAT = "现在我们只需要一把剑了。"

STRINGS.NAMES.FLYSWATTER = "苍蝇拍"
_G.ChinesePlus.RenameRecipe("FLYSWATTER", "打死这些虫子。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.FLYSWATTER = "完美地杀死小昆虫。"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.FLYSWATTER = "杀死那些令人恶心的虫子真是太好了！"
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.FLYSWATTER = "是不是用来打苍蝇的。"
STRINGS.CHARACTERS.WENDY.DESCRIBE.FLYSWATTER = "我将更快地结束一些生命。"
STRINGS.CHARACTERS.WX78.DESCRIBE.FLYSWATTER = "实用工具狭窄领域的升级"
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.FLYSWATTER = "它专为杀害小昆虫设计。"
STRINGS.CHARACTERS.WOODIE.DESCRIBE.FLYSWATTER = "这将会很有用。"
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.FLYSWATTER = "实用的。"
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.FLYSWATTER = "杀虫的战斗武器"
STRINGS.CHARACTERS.WEBBER.DESCRIBE.FLYSWATTER = "不是我们信赖的网，但是是它的把戏。"

STRINGS.NAMES.FRYINGPAN = "平底锅"
_G.ChinesePlus.RenameRecipe("FRYINGPAN", "做点培根。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.FRYINGPAN = "它真的很难。"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.FRYINGPAN = "我会融化他们的脸。"
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.FRYINGPAN = "煎得好，打得好"
STRINGS.CHARACTERS.WENDY.DESCRIBE.FRYINGPAN = "被一个厨房工具搞死，多么滑稽。"
STRINGS.CHARACTERS.WX78.DESCRIBE.FRYINGPAN = "这是用金属做的，像我一样。"
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.FRYINGPAN = "一个钢制的平底锅。"
STRINGS.CHARACTERS.WOODIE.DESCRIBE.FRYINGPAN = "现在我要做些薄煎饼。"
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.FRYINGPAN = "嗯，任何武器，只要能打死我的敌人就是好的。"
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.FRYINGPAN = "厨房战士的军火库！"
STRINGS.CHARACTERS.WEBBER.DESCRIBE.FRYINGPAN = "我们喜欢它发出的声音。"

STRINGS.NAMES.GROWTHSTAFF = "生长法杖"
_G.ChinesePlus.RenameRecipe("GROWTHSTAFF", "快速生长。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.GROWTHSTAFF = "它散发着能量。"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.GROWTHSTAFF = "它真漂亮。"
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.GROWTHSTAFF = "沃尔夫冈觉得自己的肌肉正在成长。"
STRINGS.CHARACTERS.WENDY.DESCRIBE.GROWTHSTAFF = "帮助植物更快地死去。"
STRINGS.CHARACTERS.WX78.DESCRIBE.GROWTHSTAFF = "生长程序工具"
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.GROWTHSTAFF = "它加速了附近的植物的生长周期。"
STRINGS.CHARACTERS.WOODIE.DESCRIBE.GROWTHSTAFF = "我会让树更大。"
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.GROWTHSTAFF = "食人花可以很好地服务于它的目标。"
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.GROWTHSTAFF = "拥有使植物更强壮的能力。"
STRINGS.CHARACTERS.WEBBER.DESCRIBE.GROWTHSTAFF = "看起来狂野和昂贵。"

STRINGS.NAMES.HOLLOWHAT = "碎裂的头骨"
_G.ChinesePlus.RenameRecipe("HOLLOWHAT", "疯狂的快。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.HOLLOWHAT = "一个可怕的骷髅。"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.HOLLOWHAT = "它可以做个很好的的烟灰缸。"
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.HOLLOWHAT = "沃尔夫冈头上的头骨。"
STRINGS.CHARACTERS.WENDY.DESCRIBE.HOLLOWHAT = "有人不需要这个了。"
STRINGS.CHARACTERS.WX78.DESCRIBE.HOLLOWHAT = "到底是个头骨"
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.HOLLOWHAT = "多么野蛮。"
STRINGS.CHARACTERS.WOODIE.DESCRIBE.HOLLOWHAT = "啧，这是人的头骨。"
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.HOLLOWHAT = "它散发着暗能量。"
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.HOLLOWHAT = "战士的遗骸。"
STRINGS.CHARACTERS.WEBBER.DESCRIBE.HOLLOWHAT = "这个碗是空的！"

STRINGS.NAMES.LIGHTNECKLACE = "光明项链"
_G.ChinesePlus.RenameRecipe("LIGHTNECKLACE", "照亮你的路。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.LIGHTNECKLACE = "它的光芒是相当有用的。"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.LIGHTNECKLACE = "这不是火，但它发光！"
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.LIGHTNECKLACE = "珠宝。"
STRINGS.CHARACTERS.WENDY.DESCRIBE.LIGHTNECKLACE = "一根线上的花。"
STRINGS.CHARACTERS.WX78.DESCRIBE.LIGHTNECKLACE = "它坏得很快。"
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.LIGHTNECKLACE = "连到一根丝线上面的白色发光灯泡。"
STRINGS.CHARACTERS.WOODIE.DESCRIBE.LIGHTNECKLACE = "女人会喜欢这个。"
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.LIGHTNECKLACE = "一些珠宝首饰。我要更好的和更好的。"
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.LIGHTNECKLACE = "适合一个女人，但不是战士！"
STRINGS.CHARACTERS.WEBBER.DESCRIBE.LIGHTNECKLACE = "它正如它的名字。"

STRINGS.NAMES.SPARTAHELMUT = "黄金头盔"
_G.ChinesePlus.RenameRecipe("SPARTAHELMUT", "昂贵的护甲。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SPARTAHELMUT = "它的设计似乎古老。"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.SPARTAHELMUT = "毛簇蓬松又红，像火一样！"
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.SPARTAHELMUT = "愚蠢的样子。"
STRINGS.CHARACTERS.WENDY.DESCRIBE.SPARTAHELMUT = "它的价值超过我的生命。"
STRINGS.CHARACTERS.WX78.DESCRIBE.SPARTAHELMUT = "附着到身体部分的金属头盔"
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.SPARTAHELMUT = "一个金制的斯巴达式的头盔。"
STRINGS.CHARACTERS.WOODIE.DESCRIBE.SPARTAHELMUT = "那不是一个加拿大的头盔。"
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.SPARTAHELMUT = "看起来很时髦。"
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.SPARTAHELMUT = "这是黄金的，但缺一个角。"
STRINGS.CHARACTERS.WEBBER.DESCRIBE.SPARTAHELMUT = "三百或更少...我们将战斗！"

STRINGS.NAMES.SPARTASWURD = "锋利宝刀"
_G.ChinesePlus.RenameRecipe("SPARTASWURD", "不要割破了你的手指！")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SPARTASWURD = "它正如它的名字."
STRINGS.CHARACTERS.WILLOW.DESCRIBE.SPARTASWURD = "一个真正的大刀"
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.SPARTASWURD = "是强大的刀。像我这样的！"
STRINGS.CHARACTERS.WENDY.DESCRIBE.SPARTASWURD = "我会割很多喉咙。"
STRINGS.CHARACTERS.WX78.DESCRIBE.SPARTASWURD = "这将使杀人更容易."
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.SPARTASWURD = "一个斯巴达风格的剑。"
STRINGS.CHARACTERS.WOODIE.DESCRIBE.SPARTASWURD = "比我的斧头更锋利！"
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.SPARTASWURD = "一个非常好的刀。"
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.SPARTASWURD = "我喜欢这把刀。"
STRINGS.CHARACTERS.WEBBER.DESCRIBE.SPARTASWURD = "我们离一个真正的骑士更近了一步。"

STRINGS.NAMES.SUMMERBANDANA = "夏日头巾"
_G.ChinesePlus.RenameRecipe("SUMMERBANDANA", "在那些漫长的夏日里使用。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SUMMERBANDANA = "它可以把热量带走。"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.SUMMERBANDANA = "这将有助于我的头免于着火。"
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.SUMMERBANDANA = "夏天是沃尔夫冈的对手。"
STRINGS.CHARACTERS.WENDY.DESCRIBE.SUMMERBANDANA = "我穿着死亡，玷污的芦苇。"
STRINGS.CHARACTERS.WX78.DESCRIBE.SUMMERBANDANA = "中等层次的过热保护。"
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.SUMMERBANDANA = "从莎草纸做成的帽子。"
STRINGS.CHARACTERS.WOODIE.DESCRIBE.SUMMERBANDANA = "我看起来很好，嗯？"
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.SUMMERBANDANA = "一顶纸帽子？呃。"
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.SUMMERBANDANA = "一块抵挡太阳的护甲。"
STRINGS.CHARACTERS.WEBBER.DESCRIBE.SUMMERBANDANA = "我们看起来像个海盗！"

STRINGS.NAMES.WOODENMACE = "木锏"
_G.ChinesePlus.RenameRecipe("WOODENMACE", "重击他们的头。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.WOODENMACE = "它是用木头做的。"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.WOODENMACE = "战斗和作为燃料。"
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.WOODENMACE = "木头配得上沃尔夫冈的手吗？"
STRINGS.CHARACTERS.WENDY.DESCRIBE.WOODENMACE = "暴力工具。"
STRINGS.CHARACTERS.WX78.DESCRIBE.WOODENMACE = "做一棵树更有用。"
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.WOODENMACE = "用木头做的锏。"
STRINGS.CHARACTERS.WOODIE.DESCRIBE.WOODENMACE = "我喜欢它。"
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.WOODENMACE = "木制的球棒。"
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.WOODENMACE = "准备战斗的木头。"
STRINGS.CHARACTERS.WEBBER.DESCRIBE.WOODENMACE = "我们可以打棒球！"

STRINGS.NAMES.ARMORMOSQUITO = "蚊子套装"
_G.ChinesePlus.RenameRecipe("ARMORMOSQUITO", "血让你更健康。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.ARMORMOSQUITO = "闻起来像血。"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.ARMORMOSQUITO = "扔了丑陋的头就是好东西"
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.ARMORMOSQUITO = "虫子护甲。"
STRINGS.CHARACTERS.WENDY.DESCRIBE.ARMORMOSQUITO = "血腥的和令人恶心的。"
STRINGS.CHARACTERS.WX78.DESCRIBE.ARMORMOSQUITO = "一块小肉的好用处。"
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.ARMORMOSQUITO = "一种由蚊子制成的护甲。"
STRINGS.CHARACTERS.WOODIE.DESCRIBE.ARMORMOSQUITO = "充满了血液。"
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.ARMORMOSQUITO = "这些吸血鬼终于是好东西了。"
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.ARMORMOSQUITO = "我看到过比这更垃圾的护甲。"
STRINGS.CHARACTERS.WEBBER.DESCRIBE.ARMORMOSQUITO = "我们看起来像一只蚊子吗？"

STRINGS.NAMES.BARONSUIT = "男爵套装"
_G.ChinesePlus.RenameRecipe("BARONSUIT", "见鬼的衣冠楚楚。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.BARONSUIT = "我是一个现代的大将军。"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.BARONSUIT = "漂亮的套装。"
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.BARONSUIT = "聪明人的衣服。"
STRINGS.CHARACTERS.WENDY.DESCRIBE.BARONSUIT = "做这样的东西需要奉献精神。"
STRINGS.CHARACTERS.WX78.DESCRIBE.BARONSUIT = "我是唯一该穿这个的。"
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.BARONSUIT = "柔软的紫色套装有一个围兜。"
STRINGS.CHARACTERS.WOODIE.DESCRIBE.BARONSUIT = "这是漂亮的。"
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.BARONSUIT = "这个看起来很好。"
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.BARONSUIT = "适用于一般人。"
STRINGS.CHARACTERS.WEBBER.DESCRIBE.BARONSUIT = "有围兜我们吃晚餐时就不会弄脏自己了。"

STRINGS.NAMES.BIRCHNUTHAT = "尖刺头盔"
_G.ChinesePlus.RenameRecipe("BIRCHNUTHAT", "原始的弧形。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.BIRCHNUTHAT = "噢，看那些尖刺！"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.BIRCHNUTHAT = "一种带刺的壳甲。"
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.BIRCHNUTHAT = "头上带刺！"
STRINGS.CHARACTERS.WENDY.DESCRIBE.BIRCHNUTHAT = "一个空心壳。"
STRINGS.CHARACTERS.WX78.DESCRIBE.BIRCHNUTHAT = "为了这个我杀了很多植物宝宝。"
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.BIRCHNUTHAT = "尖刺会伤害任何试图攻击的人。"
STRINGS.CHARACTERS.WOODIE.DESCRIBE.BIRCHNUTHAT = "由“树种子”制作！"
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.BIRCHNUTHAT = "这款头盔将为我而战！"
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.BIRCHNUTHAT = "宝宝树甲！"
STRINGS.CHARACTERS.WEBBER.DESCRIBE.BIRCHNUTHAT = "尖刺无处不在！"
