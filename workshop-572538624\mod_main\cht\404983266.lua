STRINGS.NAMES.PICKLE = "醃製"

-- Pickle Barrel

STRINGS.NAMES.PICKLE_BARREL = "醃菜桶"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.PICKLE_BARREL_PICKLING = {
  "醃製這個需要一段時間！",
  "醃製這個肯定需要一段時間。",
  "我的食物完成醃製之前我很迷茫。",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.PICKLE_BARREL_GENERIC = {
  "醃製食物會用很長時間，對吧？",
  "嗯，優質的鹹味.",
  "不要與匹克球混淆.",
  "供應三明治，對吧？",
}

STRINGS.CHARACTERS.GENERIC.DESCRIBE.PICKLE_BARREL = STRINGS.CHARACTERS.GENERIC.DESCRIBE.PICKLE_BARREL_GENERIC

_G.ChinesePlus.RenameRecipe("PICKLE_BARREL", "醃製你的食物，讓它較長時間保持不壞！")

-- Pickle Sword

STRINGS.NAMES.PICKLE_SWORD = "黃瓜大保劍"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.PICKLE_SWORD = {
  "用你的黃瓜重擊敵人的菊花！",
  "我的黃瓜比你的大！",
  "醃出來的驚喜！",
}

_G.ChinesePlus.RenameRecipe("PICKLE_SWORD", "用你的黃瓜重擊敵人的菊花！")

-- Beet

STRINGS.NAMES.BEET = "甜菜"
STRINGS.NAMES.BEET_COOKED = "烤甜菜"
STRINGS.NAMES.BEET_SEEDS = "甜菜種子"
STRINGS.NAMES.BEET_PICKLED = "醃甜菜"
STRINGS.NAMES.BEET_PLANTED = "甜菜"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.BEET = {
  "一個事實：熊吃甜菜。熊吃甜菜，太空堡壘卡拉狄加。",
  "沒有人喜歡甜菜？也許我應該用生長的糖果代替。",
  "讓我們花園派對。\n萵苣蘿蔔甜菜。",
  "我可以醃製這個！",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.BEET_COOKED = {
  "烤甜菜有一個甜蜜的泥土味",
  "比未經焙烤的甜菜甜",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.BEET_SEEDS = {
  "這是一個種子。",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.BEET_PICKLED = {
  "我聽到人們很喜歡醃甜菜。\n 可能我應該讓他們嘗試一下。",
  "他們實際上看起來還挺好吃",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.BEET_PLANTED = {
  "看起來像甜菜",
}

-- Berries

STRINGS.NAMES.BERRIES_PICKLED = "醃漿果"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.BERRIES_PICKLED = {
  "醃漿果只是香的輕觸。",
  "與乳酪搭配口味更佳！",
}

-- Cabbage

STRINGS.NAMES.CABBAGE = "白菜"
STRINGS.NAMES.CABBAGE_COOKED = "烤白菜"
STRINGS.NAMES.CABBAGE_SEEDS = "白菜種子"
STRINGS.NAMES.CABBAGE_PICKLED = "酸菜"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.CABBAGE = {
  "一個叫白菜的人發明了電腦......\n無需等待，他叫巴貝奇",
  "作為一個人的頭是又大又聰明",
  "我聽說，孩子們開發了周圍白菜的修補程式",
  "我可以醃製這個！",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CABBAGE_COOKED = {
  "香脆可口",
  "這個很容易製作，只需要切和煮",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CABBAGE_SEEDS = {
  "這是一個種子.",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CABBAGE_PICKLED = {
  "我爺爺把酸菜放在他的巧克力蛋糕裡",
  "烘烤時嘗試用椰子替換酸菜。",
  "也被稱為人身自由的白菜",
}

-- Carrot

STRINGS.NAMES.CARROT_PICKLED = "醃胡蘿蔔"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.CARROT_PICKLED = {
  "壞掉的時間比正常的胡蘿蔔長",
  "有些人喜歡胡蘿蔔，而另一些人喜歡白菜。",
}

-- Corn

STRINGS.NAMES.CORN_PICKLED = "醃玉米"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.CORN_PICKLED = {
  "壞掉的時間比正常的玉米長",
  "玉米得到了表揚時說了什麼？噢，扯淡！",
}

-- Cucumber

STRINGS.NAMES.CUCUMBER = "黃瓜"
STRINGS.NAMES.CUCUMBER_COOKED = "烤黃瓜"
STRINGS.NAMES.CUCUMBER_SEEDS = "黃瓜種子"
STRINGS.NAMES.CUCUMBER_PICKLED = "醃黃瓜"
STRINGS.NAMES.CUCUMBER_GOLDEN_PICKLED = "黃金醃黃瓜"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.CUCUMBER = {
  "看起來繁瑣...有一些黃瓜",
  "我敢打賭，這將成為一個好醃菜",
  "像黃瓜一樣酷",
  "我要叫他拉里",
  "我可以醃製這個！",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CUCUMBER_COOKED = {
  "它現在永遠不會成為一個醃菜了",
  "嚐起來像開水",
  "可憐的拉里。",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CUCUMBER_SEEDS = {
  "這是一個種子。",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CUCUMBER_PICKLED = {
  "這是一個相當好的醃菜",
  "為什麼黃瓜要傻笑？他們撿東西國的！",
  "如果我有一個漢堡包把它放上去就好了！",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CUCUMBER_GOLDEN_PICKLED = {
  "一個超級特別的醃菜",
  "比普通醃菜好很多！",
}

-- Egg

STRINGS.NAMES.EGG_PICKLED = "醃蛋"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.EGG_PICKLED = {
  "我真的要吃這個？",
  "誰確定這應該是可食用的？",
}

-- Eggplant

STRINGS.NAMES.EGGPLANT_PICKLED = "醃茄子"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.EGGPLANT_PICKLED = {
  "壞掉的時間比正常的茄子長",
  "你不要偷苦茄子",
}

-- Fish

STRINGS.NAMES.FISH_PICKLED = "醃鯡魚"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.FISH_PICKLED = {
  "哇，真刺鼻！",
  "抓住一個醃鯡魚，把它放在你的口袋裡",
  "至少它不是帶泥的魚",
}

-- Mush

STRINGS.NAMES.MUSH_PICKLED = "醃菜濃湯"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.MUSH_PICKLED = {
  "最好不要吃這個",
  "我猜是醃完了被人忘了",
}

-- Mushroom

STRINGS.NAMES.MUSHROOM_PICKLED = "醃蘑菇"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.MUSHROOM_PICKLED = {
  "菌類為什麼離開派對？這兒沒有蘑菇。",
  "人們為什麼喜歡蘑菇先生？\n因為他是一個菌類！",
}

-- Onion

STRINGS.NAMES.ONION = "洋蔥"

STRINGS.NAMES.ONION_COOKED = "洋蔥圈"

STRINGS.NAMES.ONION_SEEDS = "洋蔥種子"
STRINGS.NAMES.ONION_PICKLED = "醃洋蔥"
STRINGS.NAMES.ONION_PLANTED = "洋蔥"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.ONION = {
  "”抽泣中“ 誰在切洋蔥？",
  "妖怪就像洋蔥一樣",
  "洋蔥總是讓我哭",
  "我可以醃製這個！",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.ONION_COOKED = {
  "如果你喜歡的話，你應該把洋蔥圈放在上面。",
  "如果你聽到一個洋蔥響了，回答它。",
  "我想這使我成為洋蔥圈的上帝。",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.ONION_SEEDS = {
  "這是一個種子。",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.ONION_PICKLED = {
  "什麼是圓的白色的笑聲？一個搔你癢的洋蔥！",
  "美麗而燦爛...味道好極了！",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.ONION_PLANTED = {
  "你看，一個野生的洋蔥！",
}

-- Potato

STRINGS.NAMES.POTATO = "土豆"
STRINGS.NAMES.POTATO_COOKED = "烤土豆"
STRINGS.NAMES.POTATO_PLANTED = "土豆"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.POTATO = {
  "煮熟他們，搗爛他們，把腳伸進罐子裡",
  "我思故我土豆",
  "土豆就是土豆",
  "馬鈴薯是什麼東西？",
  "什麼有眼睛卻看不見？\n土豆！",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.POTATO_COOKED = {
  "簡單，快速，美味的土豆",
  "美味的炸土豆",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.POTATO_PLANTED = {
  "如果我要那個土豆，我需要一把鏟子",
}

-- Pigs Foot

STRINGS.NAMES.PIGS_FOOT = "豬腳"
STRINGS.NAMES.PIGS_FOOT_COOKED = "豬肉皮"
STRINGS.NAMES.PIGS_FOOT_PICKLED = "醃豬腳"
STRINGS.NAMES.PIGS_FOOT_DRIED = "幹豬腳"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.PIGS_FOOT = {
  "可憐的威爾伯…",
  "這隻小豬不會再去市場了",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.PIGS_FOOT_COOKED = {
  "最好吃邊看足球",
  "一個肉製成的鬆脆的點心！",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.PIGS_FOOT_PICKLED = {
  "誰認為這是一個好主意？",
  "我不認為一個飢餓的猛禽會吃這個",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.PIGS_FOOT_DRIED = {
  "好吃的豬腳幹…",
}

-- Pumpkin

STRINGS.NAMES.PUMPKIN_PICKLED = "醃南瓜"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.PUMPKIN_PICKLED = {
  "彼得-辣椒醃了一大堆醃南瓜… 呃，辣椒",
  "南瓜最喜歡的運動是什麼？\n壁球（南瓜）",
}

-- Radish

STRINGS.NAMES.RADISH = "蘿蔔"
STRINGS.NAMES.RADISH_COOKED = "烤蘿蔔"
STRINGS.NAMES.RADISH_SEEDS = "蘿蔔種子"
STRINGS.NAMES.RADISH_PICKLED = "醃蘿蔔"
STRINGS.NAMES.RADISH_PLANTED = "蘿蔔"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.RADISH = {
  "什麼是小的，紅色的耳語？嘶啞的蘿蔔！",
  "這個素食者是那麼像輪子",
  "我可以醃製這個！",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.RADISH_COOKED = {
  "美味加健康",
  "比生蘿蔔更甜美柔軟",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.RADISH_SEEDS = {
  "這是一個種子。",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.RADISH_PICKLED = {
  "甜的，濃烈的，粉紅色的",
  "這將是偉大的裝飾"
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.RADISH_PLANTED = {
  "這是一個小小的野生蘿蔔",
}

-- Watermelon

STRINGS.NAMES.WATERMELON_PICKLED = "醃西瓜皮"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.WATERMELON_PICKLED = {
  "誰想到的醃製西瓜皮？",
  "與乳酪和餅乾搭配會很好吃。",
}

_G.ChinesePlus.RenameAction("PICKLEIT","醃製")
