-- The character select screen lines
STRINGS.CHARACTER_TITLES.kyle = "共生體"
STRINGS.CHARACTER_NAMES.kyle = "凱爾"
STRINGS.CHARACTER_DESCRIPTIONS.kyle = "*戰鬥時很可怕\n*慢慢的瘋狂\n*命令蠕蟲蜘蛛軍隊"
STRINGS.CHARACTER_QUOTES.kyle = "\"現在假設直接控制。\""

-- Custom speech strings
STRINGS.CHARACTERS.KYLE = nil --require "speech_kyle"

-- The character's name as appears in-game
STRINGS.NAMES.KYLE = "凱爾"

--- [Worm Spiders]
STRINGS.NAMES.WORMSPIDER = "蠕蟲蜘蛛"
_G.ChinesePlus.RenameRecipe("WORMSPIDER", "在你身邊戰鬥的蠕蟲")

STRINGS.NAMES.WORM_SPIDERLING = "蠕蟲幼蛛"
_G.ChinesePlus.RenameRecipe("WORM_SPIDERLING", "支援外線作戰")

STRINGS.NAMES.WORM_FIGHTER = "蠕蟲戰士"
_G.ChinesePlus.RenameRecipe("WORM_FIGHTER", "聰明能幹的戰士")

STRINGS.NAMES.WORM_ROCKY = "巖蟲蜘蛛"
_G.ChinesePlus.RenameRecipe("WORM_ROCKY", "真正的石牆")

STRINGS.NAMES.WORM_FLYING = "飛蟲蜘蛛"
_G.ChinesePlus.RenameRecipe("WORM_FLYING", "躲避敵人，從遠處攻擊")

STRINGS.NAMES.WORM_WARRIOR = "蠕蟲蜘蛛戰士"
_G.ChinesePlus.RenameRecipe("WORM_WARRIOR", "攻擊一個區域的一切")

STRINGS.NAMES.WORM_ASSASSIN = "蠕蟲蜘蛛刺客"
_G.ChinesePlus.RenameRecipe("WORM_ASSASSIN", "玻璃大炮")

--- [Describe Worms]

--STRINGS.CHARACTERS.KYLE.DESCRIBE.WORMSPIDER = "老朋友."
--STRINGS.CHARACTERS.KYLE.DESCRIBE.WORM_SPIDERLING = "你好，小傢伙."
--STRINGS.CHARACTERS.KYLE.DESCRIBE.WORM_FIGHTER = "你一直在工作，不是嗎？"
--STRINGS.CHARACTERS.KYLE.DESCRIBE.WORM_ROCKY = "我要震動房子"
--STRINGS.CHARACTERS.KYLE.DESCRIBE.WORM_FLYING = "遺憾的是太小騎不了!"
--STRINGS.CHARACTERS.KYLE.DESCRIBE.WORM_WARRIOR = "我撫慰野蠻的野獸。"
--STRINGS.CHARACTERS.KYLE.DESCRIBE.WORM_ASSASSIN = "高階刺客"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.WORMSPIDER = "它是一個像蠕蟲一樣的蜘蛛，還是一個像蜘蛛一樣的蠕蟲？"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.WORM_SPIDERLING = "哇，真是太可愛了！"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.WORM_FIGHTER = "消瘦卑鄙的戰鬥機器。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.WORM_ROCKY = "它是由岩石製成的嗎？"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.WORM_FLYING = "它是如何停留在空中的？"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.WORM_WARRIOR = "最好保持我的距離。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.WORM_ASSASSIN = "真是令人毛骨悚然。"

STRINGS.CHARACTERS.WILLOW.DESCRIBE.WORMSPIDER = nil --"GAH! Kill it with fire!"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.WORM_SPIDERLING = nil --"I need a giant magnifying glass."
STRINGS.CHARACTERS.WILLOW.DESCRIBE.WORM_FIGHTER = nil --"That thing looks crazy nasty."
STRINGS.CHARACTERS.WILLOW.DESCRIBE.WORM_ROCKY = nil --"I don't know how to burn rocks!"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.WORM_FLYING = nil --"Stupid flying thing!"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.WORM_WARRIOR = nil --"I hope that fur is flammable..."
STRINGS.CHARACTERS.WILLOW.DESCRIBE.WORM_ASSASSIN = nil --"NOPENOPENOPE"

STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.WORMSPIDER = nil --"Odd bug."
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.WORM_SPIDERLING = nil --"It is tiny and friendly!"
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.WORM_FIGHTER = nil --"Fight with me, bug!"
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.WORM_ROCKY = nil --"I have broken many rocks."
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.WORM_FLYING = nil --"It fly really fast!"
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.WORM_WARRIOR = nil --"It is mighty! I am mightier!"
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.WORM_ASSASSIN = nil --"Scary bug!"

STRINGS.CHARACTERS.WENDY.DESCRIBE.WORMSPIDER = nil --"It wriggles and writhes."
STRINGS.CHARACTERS.WENDY.DESCRIBE.WORM_SPIDERLING = nil --"There is no greater illusion than innocence."
STRINGS.CHARACTERS.WENDY.DESCRIBE.WORM_FIGHTER = nil --"It's seen things no bug should see."
STRINGS.CHARACTERS.WENDY.DESCRIBE.WORM_ROCKY = nil --"It has a heart of stone."
STRINGS.CHARACTERS.WENDY.DESCRIBE.WORM_FLYING = nil --"It hasn't a care in the world."
STRINGS.CHARACTERS.WENDY.DESCRIBE.WORM_WARRIOR = nil --"It knows not what it fights for."
STRINGS.CHARACTERS.WENDY.DESCRIBE.WORM_ASSASSIN = nil --"There is an emptiness in its eyes."

STRINGS.CHARACTERS.WX78.DESCRIBE.WORMSPIDER = nil --"THAT INSECT IS SO FULL OF HIMSELF"
STRINGS.CHARACTERS.WX78.DESCRIBE.WORM_SPIDERLING = nil --"YOUR CUTENESS WILL NOT WORK ON ME, INSECT"
STRINGS.CHARACTERS.WX78.DESCRIBE.WORM_FIGHTER = nil --"U W0T, M8?"
STRINGS.CHARACTERS.WX78.DESCRIBE.WORM_ROCKY = nil --"GREETINGS, FELLOW INORGANIC LIFEFORM"
STRINGS.CHARACTERS.WX78.DESCRIBE.WORM_FLYING = nil --"HOW ARE YOU FLYING?"
STRINGS.CHARACTERS.WX78.DESCRIBE.WORM_WARRIOR = nil --"IT HATES LIFE ALMOST AS MUCH AS I DO"
STRINGS.CHARACTERS.WX78.DESCRIBE.WORM_ASSASSIN = nil --"COME, INSECT. LET US KILL THE WORLD TOGETHER"

STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.WORMSPIDER = nil --"A species of chilopoda, perhaps."
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.WORM_SPIDERLING = nil --"It exudes a sweet-smelling pheromone."
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.WORM_FIGHTER = nil --"It looks tougher, though it is still technically a larva."
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.WORM_ROCKY = nil --"The shell is near impenetrable."
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.WORM_FLYING = nil --"Despite all logic, its wings generate lift."
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.WORM_WARRIOR = nil --"Its attacks can do significant collateral damage."
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.WORM_ASSASSIN = nil --"It is cloaked in an endoplasmic residue."

STRINGS.CHARACTERS.WOODIE.DESCRIBE.WORMSPIDER = nil --"There's an interesting story there, eh?"
STRINGS.CHARACTERS.WOODIE.DESCRIBE.WORM_SPIDERLING = nil --"You're pretty cute, eh?"
STRINGS.CHARACTERS.WOODIE.DESCRIBE.WORM_FIGHTER = nil --"I'll bet he's seen some things, eh?"
STRINGS.CHARACTERS.WOODIE.DESCRIBE.WORM_ROCKY = nil --"The master of the montage."
STRINGS.CHARACTERS.WOODIE.DESCRIBE.WORM_FLYING = nil --"He'll be my apprentice."
STRINGS.CHARACTERS.WOODIE.DESCRIBE.WORM_WARRIOR = nil --"It's got such nice, soft fur."
STRINGS.CHARACTERS.WOODIE.DESCRIBE.WORM_ASSASSIN = nil --"Horseworm of the apocalypse."

STRINGS.CHARACTERS.WAXWELL.DESCRIBE.WORMSPIDER = nil --"What an interesting little critter."
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.WORM_SPIDERLING = nil --"It's not a baby, it's just smaller."
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.WORM_FIGHTER = nil --"It fights for its friends."
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.WORM_ROCKY = nil --"It's tough, but not invincible."
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.WORM_FLYING = nil --"The little one's grown up."
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.WORM_WARRIOR = nil --"They grow into such frightening beasts."
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.WORM_ASSASSIN = nil --"You merely adopted the shadows."

STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.WORMSPIDER = nil --"You won't escap me, lindworm."
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.WORM_SPIDERLING = nil --"Who knew lindworms could be so cute?"
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.WORM_FIGHTER = nil --"A mighty lindworm."
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.WORM_ROCKY = nil --"Ragnarock the house."
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.WORM_FLYING = nil --"It defies gravity!"
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.WORM_WARRIOR = nil --"I'll call him Fenrir."
STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.WORM_ASSASSIN = nil --"Jormungandr"

STRINGS.CHARACTERS.WEBBER.DESCRIBE.WORMSPIDER = nil --"I wonder if he understands us."
STRINGS.CHARACTERS.WEBBER.DESCRIBE.WORM_SPIDERLING = nil --"Hello, buddy!"
STRINGS.CHARACTERS.WEBBER.DESCRIBE.WORM_FIGHTER = nil --"Don't be such a bully!"
STRINGS.CHARACTERS.WEBBER.DESCRIBE.WORM_ROCKY = nil --"Aw, I want a hat like that!"
STRINGS.CHARACTERS.WEBBER.DESCRIBE.WORM_FLYING = nil --"Fly away, my friend! Be free!"
STRINGS.CHARACTERS.WEBBER.DESCRIBE.WORM_WARRIOR = nil --"I don't wanna mess with him."
STRINGS.CHARACTERS.WEBBER.DESCRIBE.WORM_ASSASSIN = nil --"He's really creepy."

----------------------------------------------------------------------------------------------------------------------------------------------------------------------------
--- [Worm Names]

GLOBAL.STRINGS.WORMNAMES =
{
  "敖敗",
  "步驚雲",
  "純爺們",
  "大郎",
  "大名",
  "大炮",
  "蛋炒飯",
  "蛋蛋",
  "蛋疼",
  "逗比",
  "額外搞",
  "二胎",
  "廢話",
  "付大桶",
  "敢死隊",
  "合格",
  "黃大炮",
  "金蓮",
  "捲毛狗",
  "李大嘴",
  "馬大哈",
  "槑槑",
  "蘑菇",
  "胖豆",
  "熱熱",
  "水電工",
  "陶大糞",
  "同人",
  "微波爐",
  "唯一",
  "無聊",
  "西門*",
  "閒的",
  "小馬哥",
  "夜來歡",
  "意外",
  "芋頭",
  "豬大腸",
  "豬豬俠",
}
