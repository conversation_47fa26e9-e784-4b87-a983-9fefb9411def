_G.ChinesePlus.SetConfigTranslation(mod_to_c, [[
在地图上面显示更多的图标:								 当前版本: ]].."汉化无法显示"..[[

- 植物群落
- 动物区域
- 菌类
- 怪异的东西
- 建筑
- ...
- 等等!


兼容 "Minimap HUD（小地图）"

( 不要忘了调整选项来获得更好的体验。 )]],
	{

	},
	{


		{name = "Disp_rabbithole", label = "兔子洞", hover = "prefab: rabbithole",	options =	{ {description = "隐藏", data = "N" }, {description = "显示", data = "Y"}, },	},

		{name = "Disp_rabbit", label = "兔子", hover = "prefab: rabbit",	options =	{ {description = "隐藏", data = "N", hover = "" }, {description = "	显示 *", data = "Y", hover = "仅在可视范围内" }, },	},

		{name = "Disp_molehill", label = "鼹鼠窝", hover = "prefab: molehill", options =	{ {description = "隐藏", data = "N" }, {description = "显示", data = "Y" }, },	},

		{name = "Disp_mole", label = "鼹鼠", hover = "prefab: mole",	options =	{ {description = "隐藏", data = "N", hover = "" }, {description = "	显示 *", data = "Y", hover = "仅在可视范围内" }, },	},

		{name = "Disp_gekko", label = "草蜥", hover = "prefab: grassgekko",	options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y" }, },	},

		{name = "Disp_catcoons", label = "猫熊", hover = "(会显示猫熊的家)\nprefabs: catcoon, catcoonden",	options =	{ {description = "隐藏", data = "N" }, {description = "	显示 *", data = "Y", hover = "仅在可视范围内" }, },	},

		{name = "Disp_monkey", label = "暴躁猴", hover = "(会显示暴躁猴的家)\nprefabs: monkey, monkeybarrel", options =	{ {description = "隐藏", data = "N" }, {description = "	显示 *", data = "Y", hover = "仅在可视范围内" }, },	},

		{name = "Disp_penguin", label = "企鹅", hover = "(会显示对接地面)\nprefabs: penguin, penguin_ice", options =	{ {description = "隐藏", data = "N" }, {description = "	显示 *", data = "Y", hover = "仅在可视范围内" }, },	},

		{name = "Disp_perd", label = "火鸡", hover = "prefab: perd", options =	{ {description = "隐藏", data = "N", hover = "" }, {description = "	显示 *", data = "Y", hover = "仅在可视范围内" }, },	},

		{name = "Disp_slurtle", label = "含糊虫、黏糊虫", hover = "prefabs: slurtle, snurtle", options =	{ {description = "隐藏", data = "N", hover = "" }, {description = "	显示 *", data = "Y", hover = "仅在可视范围内" }, },	},

		{name = "Disp_bird", label = "鸟", hover = "乌鸦, 红雀，蓝雀\nprefabs: crow, robin, robin_winter", options =	{ {description = "隐藏", data = "N", hover = "" }, {description = "	显示 *", data = "Y", hover = "仅在可视范围内" }, },	},

		{name = "Disp_bee", label = "蜜蜂、杀人蜂", hover = "prefabs: bee, killerbee", options =	{ {description = "隐藏", data = "N", hover = "" }, {description = "	显示 *", data = "Y", hover = "仅在可视范围内" }, },	},

		{name = "Disp_butterfly", label = "蝴蝶", hover = "prefab: butterfly", options =	{ {description = "隐藏", data = "N", hover = "" }, {description = "	显示 *", data = "Y", hover = "仅在可视范围内" }, },	},

		{name = "Disp_fireflies", label = "萤火虫", hover = "prefab: fireflies", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},



		{name = "Disp_pigman", label = "猪人、猪人守卫", hover = "prefabs: pigman, pigguard",	options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y" }, },	},

		{name = "Disp_bunnyman", label = "兔人", hover = "prefab: bunnyman",	options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y" }, },	},

		{name = "Disp_beefalo", label = "牛", hover = "prefabs: beefalo, babybeefalo", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_koalefant", label = "大象、坐狼..", hover = "prefabs: koalefant_*, spat, warg ", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_tracks", label = "动物足迹", hover = "prefabs: animal_track, dirtpile", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_lightninggoat", label = "电羊", hover = "prefabs: lightninggoat, mother_goat", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_krampus", label = "坎普斯", hover = "prefab: krampuss", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},



		{name = "Disp_giants", label = "巨人", hover = "prefabs: moose, mooseegg, mossling, bearger, deerclops", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_dragonfly", label = "龙蝇", hover = "prefab: dragonfly", options =	{ {description = "隐藏", data = "N" }, {description = "显示", data = "Y"}, },	},

		{name = "Disp_treeguard", label = "树精守卫", hover = "prefabs: leif, birchnutdrake, leif_sparse", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_clockwork", label = "发条怪物", hover = "prefabs: bishop, knight, rook, bishop_nightmare, knight_nightmare, rook_nightmare", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_nightmare", label = "影怪", hover = "prefabs: crawlinghorror, crawlingnightmare, nightmarebeak, terrorbeak", options =	{ {description = "隐藏", data = "N", hover = "" }, {description = "	显示 *", data = "Y", hover = "仅在可视范围内" }, },	},

		{name = "Disp_hound", label = "猎犬", hover = "prefabs: hound, firehound, icehound", options =	{ {description = "隐藏", data = "N", hover = "" }, {description = "	显示 *", data = "Y", hover = "仅在可视范围内" }, },	},

		{name = "Disp_spider", label = "蜘蛛", hover = "(还会显示所有蜘蛛的巢)\nprefabs: spiderden, spider, spider_dropper, spider_hider, spider_spitter, spider_warrior, dropperweb", options =	{ {description = "隐藏", data = "N", hover = "" }, {description = "	显示 *", data = "Y", hover = "仅在可视范围内" }, },	},

		{name = "Disp_spiderqueen", label = "蜘蛛女皇", hover = "prefab: spiderqueen", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_tallbird", label = "高鸟", hover = "prefabs: tallbird, smallbird, teenbird", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_tentacle", label = "触手", hover = "prefabs: tentacle, tentacle_pillar_arm", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_bigtentacle", label = "大触手", hover = "(洞穴里面的大触手)\nprefabs: tentacle_pillar, tentacle_pillar_hole", options =	{ {description = "默认", data = "N", hover = "推荐图标\n(无论是否摧毁都显示相同图标)" }, {description = "显示", data = "Y", hover = "备用图标"}, },	},

		{name = "Disp_walrus", label = "海象", hover = "prefabs: walrus, little_walrus", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_bat", label = "蝙蝠", hover = "prefab: bat", options =	{ {description = "隐藏", data = "N", hover = ""}, {description = "	显示 *", data = "Y", hover = "仅在可视范围内" }, },	},

		{name = "Disp_minotaur", label = "远古守护者", hover = "prefabs: minotaur, rocky", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_slurper", label = "缀食者", hover = "prefab: slurper", options =	{ {description = "隐藏", data = "N", hover = ""}, {description = "	显示 *", data = "Y", hover = "仅在可视范围内" }, },	},

		{name = "Disp_worm", label = "洞穴蠕虫", hover = "prefab: worm", options =	{ {description = "隐藏", data = "N"}, {description = "	显示 *", data = "Y", hover = "仅在可视范围内" }, },	},

		{name = "Disp_eyeplant", label = "眼树", hover = "prefabs: eyeplant, lureplant", options =	{ {description = "隐藏", data = "N"}, {description = "	显示 *", data = "Y", hover = "仅在可视范围内" }, },	},

		{name = "Disp_frog", label = "青蛙", hover = "prefab: frog", options =	{ {description = "隐藏", data = "N", hover = ""}, {description = "	显示 *", data = "Y", hover = "仅在可视范围内" }, },	},

		{name = "Disp_mosquito", label = "蚊子", hover = "prefab: mosquito", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_merm", label = "鱼人", hover = "prefab: merm", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_ghost", label = "鬼魂", hover = "prefab: ghost", options =	{ {description = "隐藏", data = "N", hover = "OoOOh?"}, {description = "	显示 *", data = "Y", hover = "仅在可视范围内" }, },	},





		{name = "Disp_mushroom", label = "蘑菇", hover = "prefabs: blue_mushroom, red_mushroom, green_mushroom", options =	{{description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_planted", label = "未采摘胡萝卜/曼德拉草", hover = "prefabs: carrot_planted, mandrake_planted", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_mandrake", label = "曼德拉草", hover = "prefab: mandrake, mandrake_active", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_flowers", label = "花", hover = "prefabs: flower, flower_evil, cave_fern", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_flower_cave", label = "荧光草", hover = "prefabs: flower_cave_double, flower_cave_triple", options =	{ {description = "默认", data = "N", hover = "推荐图标\n(所有荧光草显示单果荧光草)"}, {description = "显示", data = "Y", hover="备用图标"}, },	},

		{name = "Disp_glowberry", label = "发光浆果", hover = "prefab: wormlight_plant", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_marsh_bush", label = "尖锐的灌木丛", hover = "prefab: marsh_bush", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},





		{name = "Disp_wall", label = "墙", hover = "prefabs: wall_hay,wall_moonrock,wall_ruins,wall_stone,wall_wood", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_abigail", label = "阿比盖尔", hover = "(温蒂死去的双胞胎姐姐)\nprefab: abigail", options =	{ {description = "隐藏", data = "N", hover = ""}, {description = "	显示 *", data = "Y", hover = "仅在可视范围内" }, },	},

		{name = "Disp_chester_eyebone", label = "切斯特的眼骨", hover = "prefab: eyebone", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_bone", label = "骨头", hover = "prefabs: houndbone,scorched_skeleton,skeleton,skeleton_player", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_waxwelljournal", label = "暗影", hover = "prefab: waxwelljournal", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_gravestone", label = "坟墓、墓碑", hover = "prefabs: mound, gravestone", options =	{ {description = "默认", data = "N", hover = "推荐图标"}, {description = "显示", data = "Y", hover = "都显示坟墓"}, },	},

		{name = "Disp_rock2", label = "金矿石", hover = "prefab: rock2", options =	{ {description = "默认", data = "N", hover = "推荐图标\n(和普通矿石一样)"}, {description = "显示", data = "Y", hover = "备用图标"}, },	},

		{name = "Disp_gold", label = "金块", hover = "prefab: goldnugget", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_gems", label = "宝石", hover = "prefabs: redgem,bluegem,purplegem,orangegem,yellowhgem,greengem", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_gears", label = "齿轮", hover = "prefab: gears", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_flint", label = "燧石", hover = "prefab: flint", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_marble", label = "大理石", hover = "prefab: marble", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_rocks", label = "矿石", hover = "prefab: rocks", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_nitre", label = "硝石", hover = "prefab: nitre", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_nightmarefuel", label = "噩梦燃料", hover = "prefab: nightmarefuel", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_teleportato", label = "\"那些东西\"", hover = "prefabs: teleportato_box,teleportato_crank,teleportato_potato,teleportato_ring", options =	{ {description = "隐藏", data = "N", hover = "联机版不存在"}, {description = "显示", data = "Y", hover = "联机版不存在"}, },	},

		{name = "Disp_altar", label = "远古遗迹", hover = "prefabs: ancient_altar, ancient_altar_broken", options =	{ {description = "默认", data = "N", hover = "推荐图标\n(无论是否破损都显示一样)"}, {description = "显示", data = "Y", hover = "备用图标"}, },	},

		{name = "Disp_fissure", label = "裂缝", hover = "prefabs: fissure, fissure_lower", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_pighead", label = "猪人头", hover = "prefab: pighead", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

		{name = "Disp_poop", label = "便便", hover = "prefabs: poop, guano", options =	{ {description = "隐藏", data = "N"}, {description = "显示", data = "Y"}, },	},

	}
)
