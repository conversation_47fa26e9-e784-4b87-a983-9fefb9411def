_G.ChinesePlus.SetConfigTranslation(mod_to_c,
  "血淋淋的辉煌！\n你准备好砍他们血他妈的脖子了吗？",
  {

  },
  {
    {
      name = "demomanbasehp",
      label = "炸弹人的生命:",
      options =
      {
        {description = "5", data = 5},
        {description = "10", data = 10},
        {description = "15", data = 15},
        {description = "20", data = 20},
        {description = "25", data = 25},
        {description = "30", data = 30},
        {description = "35", data = 35},
        {description = "40", data = 40},
        {description = "45", data = 45},
        {description = "50", data = 50},
        {description = "55", data = 55},
        {description = "60", data = 60},
        {description = "65", data = 65},
        {description = "70", data = 70},
        {description = "75", data = 75},
        {description = "80", data = 80},
        {description = "85", data = 85},
        {description = "90", data = 90},
        {description = "95", data = 95},
        {description = "100", data = 100},
        {description = "105", data = 105},
        {description = "110", data = 110},
        {description = "115", data = 115},
        {description = "120", data = 120},
        {description = "125", data = 125},
        {description = "130", data = 130},
        {description = "135", data = 135},
        {description = "140", data = 140},
        {description = "145", data = 145},
        {description = "150", data = 150},
        {description = "155", data = 155},
        {description = "160", data = 160},
        {description = "165", data = 165},
        {description = "170", data = 170},
        {description = "175(默认)", data = 175},
        {description = "180", data = 180},
        {description = "185", data = 185},
        {description = "190", data = 190},
        {description = "195", data = 195},
        {description = "200", data = 200},
        {description = "205", data = 205},
        {description = "210", data = 210},
        {description = "215", data = 215},
        {description = "220", data = 220},
        {description = "225", data = 225},
        {description = "230", data = 230},
        {description = "235", data = 235},
        {description = "240", data = 240},
        {description = "245", data = 245},
        {description = "250", data = 250},
        {description = "255", data = 255},
        {description = "260", data = 260},
        {description = "265", data = 265},
        {description = "270", data = 270},
        {description = "275", data = 275},
        {description = "280", data = 280},
        {description = "285", data = 285},
        {description = "290", data = 290},
        {description = "295", data = 295},
        {description = "300", data = 300},
      },
      default = 175,
    },

    {
      name = "demomanbasesanity",
      label = "炸弹人的理智:",
      options =
      {
        {description = "5", data = 5},
        {description = "10", data = 10},
        {description = "15", data = 15},
        {description = "20", data = 20},
        {description = "25", data = 25},
        {description = "30", data = 30},
        {description = "35", data = 35},
        {description = "40", data = 40},
        {description = "45", data = 45},
        {description = "50", data = 50},
        {description = "55", data = 55},
        {description = "60", data = 60},
        {description = "65", data = 65},
        {description = "70", data = 70},
        {description = "75", data = 75},
        {description = "80", data = 80},
        {description = "85", data = 85},
        {description = "90", data = 90},
        {description = "95", data = 95},
        {description = "100", data = 100},
        {description = "105", data = 105},
        {description = "110", data = 110},
        {description = "115", data = 115},
        {description = "120", data = 120},
        {description = "125", data = 125},
        {description = "130", data = 130},
        {description = "135", data = 135},
        {description = "140", data = 140},
        {description = "145", data = 145},
        {description = "150(默认)", data = 150},
        {description = "155", data = 155},
        {description = "160", data = 160},
        {description = "165", data = 165},
        {description = "170", data = 170},
        {description = "175", data = 175},
        {description = "180", data = 180},
        {description = "185", data = 185},
        {description = "190", data = 190},
        {description = "195", data = 195},
        {description = "200", data = 200},
        {description = "205", data = 205},
        {description = "210", data = 210},
        {description = "215", data = 215},
        {description = "220", data = 220},
        {description = "225", data = 225},
        {description = "230", data = 230},
        {description = "235", data = 235},
        {description = "240", data = 240},
        {description = "245", data = 245},
        {description = "250", data = 250},
        {description = "255", data = 255},
        {description = "260", data = 260},
        {description = "265", data = 265},
        {description = "270", data = 270},
        {description = "275", data = 275},
        {description = "280", data = 280},
        {description = "285", data = 285},
        {description = "290", data = 290},
        {description = "295", data = 295},
        {description = "300", data = 300},
      },
      default = 150,
    },

    {
      name = "demomanbasehunger",
      label = "炸弹人的饥饿:",
      options =
      {
        {description = "5", data = 5},
        {description = "10", data = 10},
        {description = "15", data = 15},
        {description = "20", data = 20},
        {description = "25", data = 25},
        {description = "30", data = 30},
        {description = "35", data = 35},
        {description = "40", data = 40},
        {description = "45", data = 45},
        {description = "50", data = 50},
        {description = "55", data = 55},
        {description = "60", data = 60},
        {description = "65", data = 65},
        {description = "70", data = 70},
        {description = "75", data = 75},
        {description = "80", data = 80},
        {description = "85", data = 85},
        {description = "90", data = 90},
        {description = "95", data = 95},
        {description = "100", data = 100},
        {description = "105", data = 105},
        {description = "110", data = 110},
        {description = "115", data = 115},
        {description = "120", data = 120},
        {description = "125", data = 125},
        {description = "130", data = 130},
        {description = "135", data = 135},
        {description = "140", data = 140},
        {description = "145", data = 145},
        {description = "150(默认)", data = 150},
        {description = "155", data = 155},
        {description = "160", data = 160},
        {description = "165", data = 165},
        {description = "170", data = 170},
        {description = "175", data = 175},
        {description = "180", data = 180},
        {description = "185", data = 185},
        {description = "190", data = 190},
        {description = "195", data = 195},
        {description = "200", data = 200},
        {description = "205", data = 205},
        {description = "210", data = 210},
        {description = "215", data = 215},
        {description = "220", data = 220},
        {description = "225", data = 225},
        {description = "230", data = 230},
        {description = "235", data = 235},
        {description = "240", data = 240},
        {description = "245", data = 245},
        {description = "250", data = 250},
        {description = "255", data = 255},
        {description = "260", data = 260},
        {description = "265", data = 265},
        {description = "270", data = 270},
        {description = "275", data = 275},
        {description = "280", data = 280},
        {description = "285", data = 285},
        {description = "290", data = 290},
        {description = "295", data = 295},
        {description = "300", data = 300},
      },
      default = 150,
    },

    {
      name = "eyelanderbasedmg",
      label = "魔眼阔剑的伤害:",
      options =
      {
        {description = "17", data = 17},
        {description = "27.2", data = 27.2},
        {description = "34", data = 34},
        {description = "42.5(默认)", data = 42.5},
        {description = "51", data = 51},
        {description = "59.5", data = 59.5},
        {description = "68", data = 68},
        {description = "100", data = 100},
        {description = "200", data = 200},
        {description = "300", data = 300},
        {description = "400", data = 400},
        {description = "500", data = 500},
        {description = "600", data = 600},
        {description = "700", data = 700},
        {description = "800", data = 800},
        {description = "900", data = 900},
        {description = "1000", data = 1000},
      },
      default = 42.5,
    },

    {
      name = "stickybombradius",
      label = "粘性炸弹爆炸范围:",
      options =
      {
        {description = "2", data = 2},
        {description = "3", data = 3},
        {description = "4", data = 4},
        {description = "5(默认)", data = 5},
        {description = "6", data = 6},
        {description = "7", data = 7},
        {description = "8", data = 8},
      },
      default = 5,
    },

    {
      name = "stickybombdmg",
      label = "粘性炸弹伤害:",
      options =
      {
        {description = "17", data = 17},
        {description = "27.2", data = 27.2},
        {description = "34", data = 34},
        {description = "42.5", data = 42.5},
        {description = "51(默认)", data = 51},
        {description = "59.5", data = 59.5},
        {description = "68", data = 68},
        {description = "100", data = 100},
        {description = "200", data = 200},
        {description = "300", data = 300},
        {description = "400", data = 400},
        {description = "500", data = 500},
        {description = "600", data = 600},
        {description = "700", data = 700},
        {description = "800", data = 800},
        {description = "900", data = 900},
        {description = "1000", data = 1000},
      },
      default = 51,
    },

    {
      name = "stickylaunchereloadspd",
      label = "粘性炸弹发射器装填时间:",
      options =
      {
        {description = "1 秒ond", data = 1},
        {description = "3 秒", data = 3},
        {description = "5 秒", data = 5},
        {description = "10 秒", data = 10},
        {description = "15 秒", data = 15},
        {description = "20 秒(默认)", data = 20},
        {description = "25 秒", data = 25},
        {description = "30 秒", data = 30},
        {description = "35 秒", data = 35},
        {description = "40 秒", data = 40},
      },
      default = 20,
    },

    {
      name = "maxstickiesdeployed",
      label = "部署粘性炸弹的最大数量:",
      options =
      {
        {description = "2", data = 2},
        {description = "4", data = 4},
        {description = "6(默认)", data = 6},
        {description = "8", data = 8},
        {description = "10", data = 10},
        {description = "12", data = 12},
        {description = "14", data = 14},
        {description = "16", data = 16},
        {description = "18", data = 18},
        {description = "20", data = 20},
        {description = "22", data = 22},
        {description = "24", data = 24},
        {description = "26", data = 26},
        {description = "28", data = 28},
        {description = "30", data = 30},
      },
      default = 6,
    },

    {
      name = "scrumpyduration",
      label = "苹果烈酒持续时间:",
      options =
      {
        {description = "30 秒", data = 30},
        {description = "40 秒", data = 40},
        {description = "50 秒", data = 50},
        {description = "60 秒(默认)", data = 60},
        {description = "70 秒", data = 70},
        {description = "80 秒", data = 80},
        {description = "90 秒", data = 90},
        {description = "100 秒", data = 100},
      },
      default = 60,
    },

    {
      name = "scrumpyrecipe",
      label = "苹果烈酒制作材料:",
      options =
      {
        {description = "不公平", data = 1},
        {description = "简单", data = 2},
        {description = "默认", data = 3},
        {description = "中等", data = 4},
        {description = "困难", data = 5},
        {description = "极难", data = 6},
        {description = "不可能", data = 7},
      },
      default = 3,
    },

    {
      name = "weebootiesperishtime",
      label = "尖头靴腐败时间:",
      options =
      {
        {description = "1 天", data = 1},
        {description = "2 天", data = 2},
        {description = "3 天(默认)", data = 3},
        {description = "4 天", data = 4},
        {description = "5 天", data = 5},
        {description = "6 天", data = 6},
        {description = "7 天", data = 7},
        {description = "8 天", data = 8},
        {description = "9 天", data = 9},
        {description = "10 天", data = 10},
        {description = "15 天", data = 15},
        {description = "20 天", data = 20},
        {description = "30 天", data = 30},
        {description = "40 天", data = 40},
        {description = "50 天", data = 50},
      },
      default = 3,
    },

    {
      name = "weebootieshpbonus",
      label = "尖头靴生命增益:",
      options =
      {
        {description = "无", data = 0},
        {description = "5 HP", data = 5},
        {description = "10 HP", data = 10},
        {description = "15 HP", data = 15},
        {description = "20 HP", data = 20},
        {description = "25 HP(默认)", data = 25},
        {description = "30 HP", data = 30},
        {description = "35 HP", data = 35},
        {description = "40 HP", data = 40},
        {description = "50 HP", data = 50},
        {description = "60 HP", data = 60},
        {description = "70 HP", data = 70},
        {description = "80 HP", data = 80},
        {description = "90 HP", data = 90},
        {description = "100 HP", data = 100},
      },
      default = 25,
    },

    {
      name = "weebootiesspdbonus",
      label = "尖头靴速度增益:",
      options =
      {
        {description = "无", data = 0},
        {description = "+5%", data = 1.05},
        {description = "+10%", data = 1.1},
        {description = "+15%", data = 1.15},
        {description = "+20%", data = 1.2},
        {description = "+25%(默认)", data = 1.25},
        {description = "+30%", data = 1.3},
        {description = "+35%", data = 1.35},
        {description = "+40%", data = 1.4},
        {description = "+50%", data = 1.5},
        {description = "+60%", data = 1.6},
        {description = "+70%", data = 1.7},
        {description = "+80%", data = 1.8},
        {description = "+90%", data = 1.9},
        {description = "+100%", data = 2},
        {description = "+110%", data = 2.1},
        {description = "+120%", data = 2.2},
        {description = "+130%", data = 2.3},
        {description = "+140%", data = 2.4},
        {description = "+150%", data = 2.5},
      },
      default = 1.25,
    },

    {
      name = "weebootiesrecipe",
      label = "尖头靴配方:",
      options =
      {
        {description = "简单", data = 0},
        {description = "默认", data = 1},
        {description = "困难", data = 2},
      },
      default = 1,
    },

    {
      name = "eyelanderrecipe",
      label = "魔眼阔剑配方:",
      options =
      {
        {description = "是(默认)", data = 1},
        {description = "否", data = 0},
      },
      default = 1,
    },

    {
      name = "decaydemoman",
      label = "粘住炸弹的���坏时间",
      options =
      {
        {description = "1 天", data = 1},
        {description = "2 天", data = 2},
        {description = "3 天", data = 3},
        {description = "4 天", data = 4},
        {description = "5 天", data = 5},
        {description = "6 天", data = 6},
        {description = "7 天", data = 7},
        {description = "8 天", data = 8},
        {description = "9 天", data = 9},
        {description = "10 天", data = 10},
        {description = "11 天", data = 11},
        {description = "12 天", data = 12},
        {description = "13 天", data = 13},
        {description = "14 天", data = 14},
        {description = "15 天", data = 15},
        {description = "永不", data = 1000},
      },
      default = 1000,
    },

    {
      name = "demomime",
      label = "哑巴炸弹人？:",
      options =
      {
        {description = "是", data = 1},
        {description = "否(默认)", data = 0},
      },
      default = 0,
    },

  }
)
