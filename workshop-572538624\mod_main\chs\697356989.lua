--W<PERSON>i
STRINGS.CHARACTER_TITLES.walani = "轻松的冲浪者"
STRINGS.CHARACTER_NAMES.walani = "瓦拉尼"
STRINGS.CHARACTER_DESCRIPTIONS.walani = "*喜欢冲浪。\n*潮湿后干的快\n*一个很潮的女孩"
STRINGS.CHARACTER_QUOTES.walani = "\"有人可以给我一些食物吗？\""
STRINGS.CHARACTERS.WALANI = nil
STRINGS.NAMES.WALANI = "瓦拉尼"

--Character interaction strings
STRINGS.CHARACTERS.GENERIC.DESCRIBE.WALANI =
{
  GENERIC = "你好, %s!",
  ATTACKER = "%s 看起来坏坏的...",
  MURDERER = "凶手!",
  REVIVER = "%s, 鬼魂之友。",
  GHOST = "%s可以使用救赎之心。",
}
STRINGS.CHARACTERS.WILLOW.DESCRIBE.WALANI = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.WALANI = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.WALANI = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.WALANI = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.WALANI = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.WALANI = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.WALANI = nil

STRINGS.CHARACTERS.GENERIC.DESCRIBE.SURFBOARD = "冲浪板"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.SURFBOARD = "一块蠢蠢的木板。"
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.SURFBOARD = "小小的木板支撑得了我吗？"
STRINGS.CHARACTERS.WENDY.DESCRIBE.SURFBOARD = "使用冲浪板给人岛屿的氛围。"
STRINGS.CHARACTERS.WX78.DESCRIBE.SURFBOARD = "几乎不能海上旅行的船只。"
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.SURFBOARD = "一块没有使用过的充满活力，容易操作，舒适冲浪板。"
STRINGS.CHARACTERS.WOODIE.DESCRIBE.SURFBOARD = "可怜的东西不能在这里冲浪。"
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.SURFBOARD = "对我来说看起来太危险了。"

--Warly
STRINGS.CHARACTER_TITLES.warly = "厨师"
STRINGS.CHARACTER_NAMES.warly = "沃利"
STRINGS.CHARACTER_DESCRIPTIONS.warly = "*具有特殊的品味\n*拥有自己的锅\n*拥有自己的袋子"
STRINGS.CHARACTER_QUOTES.warly = "\"祝你有个好胃口！\""
STRINGS.CHARACTERS.WARLY = nil
STRINGS.NAMES.WARLY = "沃利"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.WARLY =
{
  GENERIC = "今天好, %s!",
  ATTACKER = "%s看起来打算要把我当食材！",
  MURDERER = "%s已经煮过头了！",
  REVIVER = "%s烹饪出了友谊。",
  GHOST = "看起来你已经熟了, %s.",
}
STRINGS.CHARACTERS.WILLOW.DESCRIBE.WARLY = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.WARLY = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.WARLY = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.WARLY = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.WARLY = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.WARLY = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.WARLY = nil

STRINGS.FRESHFRUITCREPES = "水果薄饼"
STRINGS.NAMES.FRESHFRUITCREPES = "水果薄饼"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.FRESHFRUITCREPES = "含糖的水果！营养均衡早餐的一部分。"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.FRESHFRUITCREPES = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.FRESHFRUITCREPES = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.FRESHFRUITCREPES = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.FRESHFRUITCREPES = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.FRESHFRUITCREPES = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.FRESHFRUITCREPES = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.FRESHFRUITCREPES = nil

STRINGS.MONSTERTARTARE = "怪物鞑靼"
STRINGS.NAMES.MONSTERTARTARE = "怪物鞑靼"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MONSTERTARTARE = "这儿一定有什么别的吃的。"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.MONSTERTARTARE = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.MONSTERTARTARE = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.MONSTERTARTARE = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.MONSTERTARTARE = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.MONSTERTARTARE = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.MONSTERTARTARE = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.MONSTERTARTARE = nil

STRINGS.CHARACTERS.GENERIC.DESCRIBE.PORTABLECOOKPOTINV = "现在我们来做饭！"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.PORTABLECOOKPOTINV = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.PORTABLECOOKPOTINV = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.PORTABLECOOKPOTINV = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.PORTABLECOOKPOTINV = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.PORTABLECOOKPOTINV = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.PORTABLECOOKPOTINV = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.PORTABLECOOKPOTINV = nil

STRINGS.CHARACTERS.GENERIC.DESCRIBE.CHEFPACK = "满满都是厨师的技巧。"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.CHEFPACK = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.CHEFPACK = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.CHEFPACK = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.CHEFPACK = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.CHEFPACK = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.CHEFPACK = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.CHEFPACK = nil

--Wilbur
STRINGS.CHARACTER_TITLES.wilbur = "猴王"
STRINGS.CHARACTER_NAMES.wilbur = "威尔伯"
STRINGS.CHARACTER_DESCRIPTIONS.wilbur = "*不会说话\n*双教跑慢，四肢并用跑快\n*猴子"
STRINGS.CHARACTER_QUOTES.wilbur = "\"Ooo ooa oah ah!\""
STRINGS.CHARACTERS.WILBUR = nil
STRINGS.NAMES.WILBUR = "威尔伯"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.WILBUR =
{
  GENERIC = "今天好, %s!",
  ATTACKER = "%s的香蕉没了!",
  MURDERER = "%s不再发狂了！",
  REVIVER = "%s, 鬼魂之友。",
  GHOST = "%s可以使用救赎之心和吃些香蕉。",
}
STRINGS.CHARACTERS.WILLOW.DESCRIBE.WILBUR = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.WILBUR = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.WILBUR = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.WILBUR = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.WILBUR = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.WILBUR = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.WILBUR = nil

--Woodlegs:
STRINGS.CHARACTER_TITLES.woodlegs = "海盗船长"
STRINGS.CHARACTER_NAMES.woodlegs = "木腿"
STRINGS.CHARACTER_DESCRIPTIONS.woodlegs = "*有一个幸运帽\n*有一把幸运弯刀\n*海盗"
STRINGS.CHARACTER_QUOTES.woodlegs = "\"呀~~~呀~~吖!\""
STRINGS.CHARACTERS.WOODLEGS = nil
STRINGS.NAMES.WOODLEGS = "木腿"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.WOODLEGS =
{
  GENERIC = "你好, %s!",
  ATTACKER = "%s把他们海盗的事情看得太重。",
  MURDERER = "%s残酷的饭桶不见了！",
  REVIVER = "%s, 同船的友谊！",
  GHOST = "%s可以用救赎之心，但他们更喜欢黄金。",
}
STRINGS.CHARACTERS.WILLOW.DESCRIBE.WOODLEGS = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.WOODLEGS = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.WOODLEGS = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.WOODLEGS = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.WOODLEGS = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.WOODLEGS = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.WOODLEGS = nil

STRINGS.CHARACTERS.GENERIC.DESCRIBE.LUCKYHAT = "只适合海盗船长。"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.LUCKYHAT = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.LUCKYHAT = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.LUCKYHAT = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.LUCKYHAT = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.LUCKYHAT = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.LUCKYHAT = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.LUCKYHAT = nil

STRINGS.CHARACTERS.GENERIC.DESCRIBE.LUCKYCUTLASS = "有人打算跳海！"
STRINGS.CHARACTERS.WILLOW.DESCRIBE.LUCKYCUTLASS = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.LUCKYCUTLASS = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.LUCKYCUTLASS = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.LUCKYCUTLASS = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.LUCKYCUTLASS = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.LUCKYCUTLASS = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.LUCKYCUTLASS = nil

STRINGS.CHARACTERS.GENERIC.DESCRIBE.PORTABLECOOKPOTMAP =
{
  --BURNT = "His pot got cooked.",
  COOKING_LONG = "这需要一段时间。",
  COOKING_SHORT = "差不多就完成了！",
  DONE = "嗯嗯嗯！！准备吃饭！",
  EMPTY = "他从来没有不带它离开家过。",
}
STRINGS.CHARACTERS.WILLOW.DESCRIBE.PORTABLECOOKPOTMAP = nil
STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.PORTABLECOOKPOTMAP = nil
STRINGS.CHARACTERS.WENDY.DESCRIBE.PORTABLECOOKPOTMAP = nil
STRINGS.CHARACTERS.WX78.DESCRIBE.PORTABLECOOKPOTMAP = nil
STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.PORTABLECOOKPOTMAP = nil
STRINGS.CHARACTERS.WOODIE.DESCRIBE.PORTABLECOOKPOTMAP = nil
STRINGS.CHARACTERS.WAXWELL.DESCRIBE.PORTABLECOOKPOTMAP = nil

local function ForceTranslate()

  STRINGS.CHEFPACK = "厨师袋"
  STRINGS.NAMES.CHEFPACK = "厨师袋"
  _G.ChinesePlus.RenameRecipe("CHEFPACK", "让你的你的食物新鲜点。")

  STRINGS.LUCKYCUTLASS = "幸运弯刀"
  STRINGS.NAMES.LUCKYCUTLASS = "幸运弯刀"
  _G.ChinesePlus.RenameRecipe("LUCKYCUTLASS", "切割和姑娘的主人。弯刀。")

  STRINGS.LUCKYHAT = "幸运帽"
  STRINGS.NAMES.LUCKYHAT = "幸运帽"
  _G.ChinesePlus.RenameRecipe("LUCKYHAT", "这是一个海盗的生活。")

  STRINGS.PORTABLECOOKPOTINV = "便携式烹饪锅"
  STRINGS.NAMES.PORTABLECOOKPOTINV = "便携式烹饪锅"
  _G.ChinesePlus.RenameRecipe("PORTABLECOOKPOTINV", "你烹饪的朋友。")

  STRINGS.PORTABLECOOKPOTMAP = "便携式烹饪锅"
  STRINGS.NAMES.PORTABLECOOKPOTMAP = "便携式烹饪锅"
  _G.ChinesePlus.RenameRecipe("PORTABLECOOKPOTMAP", "你烹饪的朋友。")

  STRINGS.SURFBOARD = "冲浪板"
  STRINGS.NAMES.SURFBOARD = "冲浪板"
  _G.ChinesePlus.RenameRecipe("SURFBOARD", "好啊!加油! 帅哥！")
end

table.insert(ForceTranslateList, ForceTranslate)