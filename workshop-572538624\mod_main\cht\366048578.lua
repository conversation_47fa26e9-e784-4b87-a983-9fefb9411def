-- The character select screen lines
STRINGS.CHARACTER_TITLES.devon = "獵人"
STRINGS.CHARACTER_NAMES.devon = "德文"
STRINGS.CHARACTER_DESCRIPTIONS.devon = "*生存主義者：不需要食物；快速而弱小。 \n*曾於死亡擦肩而過，現在他看到它無處不在。\n*他最好的朋友，柏蒂"
STRINGS.CHARACTER_QUOTES.devon = "\"你從哪兒下車了，小鳥？\""

-- Custom speech strings
--STRINGS.CHARACTERS.DEVON = require(chinesefolder.."/Devon/speech_devon")
STRINGS.CHARACTERS.DEVON = nil

STRINGS.NAMES.BIRDY = "柏蒂"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.BIRDY = "嘿，漂亮的小鳥！"

-- The character's name as appears in-game
STRINGS.NAMES.DEVON = "德文"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.DEVON =
{
  GENERIC = "這是德文",
  ATTACKER = "德文看起來很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "德文, 鬼魂朋友！",
  GHOST = "德文可以使用一顆心。",
}

