STRINGS.NAMES.CHOPPERHAT = "乔巴的帽子"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.CHOPPERHAT = "一顶被小驯鹿珍惜的帽子"

_G.ChinesePlus.RenameRecipe("REDMEDICINE", "一种缓解疼痛的红色药物。")
STRINGS.NAMES.REDMEDICINE = "红药"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.REDMEDICINE = "这能很好的缓解疼痛。"

_G.ChinesePlus.RenameRecipe("GREENMEDICINE", "一种缓解压力的绿色药物。")
STRINGS.NAMES.GREENMEDICINE = "绿药"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.GREENMEDICINE = "我认为这对大脑有好处。"

_G.ChinesePlus.RenameRecipe("BLUEMEDICINE", "一种对一切都有作用的药物。")
STRINGS.NAMES.BLUEMEDICINE = "蓝药"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.BLUEMEDICINE = "当你有这个时还有谁需要阿司匹林？"

-- The character select screen lines
STRINGS.CHARACTER_TITLES.chopper = "棉花糖的爱好者"
STRINGS.CHARACTER_NAMES.chopper = "乔巴"
STRINGS.CHARACTER_DESCRIPTIONS.chopper = "*可以变形（只是还没有）\n*可以制作几种药品\n*是一只驯鹿，不是浣熊，驼鹿，或狐狸。"
STRINGS.CHARACTER_QUOTES.chopper = "\"别担心！我是医生！\""

-- Custom speech strings
--STRINGS.CHARACTERS.CHOPPER = require(chinesefolder.."/Chopper/speech_chopper")
STRINGS.CHARACTERS.CHOPPER = nil

-- The character's name as appears in-game
STRINGS.NAMES.CHOPPER = "乔巴"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CHOPPER =
{
  GENERIC = "这是乔巴！",
  ATTACKER = "乔巴看起来很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "乔巴, 鬼魂朋友！",
  GHOST = "乔巴可以使用一颗心。",
}

