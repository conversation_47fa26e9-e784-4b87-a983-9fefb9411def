_G.ChinesePlus.RenameRecipe("TENTONHAMMER", "必須重拳出擊!")
_G.ChinesePlus.RenameRecipe("SMALLROCKS", "他們是岩石，但有一口是大小！!")

STRINGS.NAMES.TENTONHAMMER = "十噸之錘"
STRINGS.NAMES.SMALLROCKS = "小石頭"
STRINGS.NAMES.IMPACTDIAL = "衝擊貝"
STRINGS.NAMES.KUROKABUTO = "黑兜"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.TENTONHAMMER = "怎麼能有人提起這樣的事呢？！"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SMALLROCKS = "沒有多少關於小石頭的說法…"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.IMPACTDIAL = "如果使用了，會是毀滅性的破壞力。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.KUROKABUTO = "小玩意兒，需要極大的瞄準技巧。"

_G.ChinesePlus.RenameTab("Usopp", "烏索普")

-- The character select screen lines
STRINGS.CHARACTER_TITLES.usopp = "狙擊之王"
STRINGS.CHARACTER_NAMES.usopp = "烏索普"
STRINGS.CHARACTER_DESCRIPTIONS.usopp = "*鷹的眼睛\n*從遠處射擊\n*玻璃大炮"
STRINGS.CHARACTER_QUOTES.usopp = "\"總有一天會像一個男人站起來去戰鬥\""

-- Custom speech strings
--STRINGS.CHARACTERS.USOPP = require(chinesefolder.."/Usopp/speech_usopp")
STRINGS.CHARACTERS.USOPP = nil

-- The character's name as appears in-game
STRINGS.NAMES.USOPP = "烏索普"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.USOPP =
{
  GENERIC = "這是烏索普！",
  ATTACKER = "烏索普看起來很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "烏索普, 鬼魂朋友！",
  GHOST = "烏索普可以使用一顆心。",
}

