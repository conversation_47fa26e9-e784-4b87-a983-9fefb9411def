STRINGS.NAMES.NINJASWORD = "忍者刀"
STRINGS.NAMES.THROW = "爆裂飞镖"
_G.ChinesePlus.RenameRecipe("THROW", "一个可以制作和扔的飞镖！")

-- The character select screen lines
STRINGS.CHARACTER_TITLES.sonic = "Z市的忍者"
STRINGS.CHARACTER_NAMES.sonic = "音速索尼克"
STRINGS.CHARACTER_DESCRIPTIONS.sonic = "*有飞镖X忍者刀\n*音速\n*高的饥饿速度和低的状态"
STRINGS.CHARACTER_QUOTES.sonic = "\" 我将用我的终极忍术结束你！\""

-- Custom speech strings
--STRINGS.CHARACTERS.SONIC = require(chinesefolder.."/Sonic/speech_sonic")
STRINGS.CHARACTERS.SONIC = nil

-- The character's name as appears in-game
STRINGS.NAMES.SONIC = "索尼克"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SONIC =
{
  GENERIC = "它的索尼克！我的意思是，音速索尼克",
  ATTACKER = "索尼克看起来很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "索尼克, 鬼魂朋友！",
  GHOST = "索尼克可以使用一颗心。",
}

