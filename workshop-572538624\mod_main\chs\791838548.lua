GLOBAL.STRINGS.NAMES.ENGINEER = "工程师"
GLOBAL.STRINGS.CHARACTER_TITLES.engineer = "和蔼可亲的人"
GLOBAL.STRINGS.CHARACTER_NAMES.engineer = "戴尔‧科纳亨尔"
GLOBAL.STRINGS.CHARACTER_DESCRIPTIONS.engineer = "*可以建造自己的各种玩意儿\n*带着他自己的扳手和安全帽\n*热爱科学"
GLOBAL.STRINGS.CHARACTER_QUOTES.engineer = "\"我解决实际问题。\""
GLOBAL.STRINGS.CHARACTERS.ENGINEER = nil

--Scrap
STRINGS.NAMES.SCRAP = "残留金属"
_G.ChinesePlus.RenameRecipe("SCRAP", "残留金属")

_G.ChinesePlus.RenameTab("Construction", "建造")

STRINGS.CHARACTERS.GENERIC.DESCRIBE.SCRAP = "凌乱的金属废料零件。"
GLOBAL.STRINGS.CHARACTERS.WILLOW.DESCRIBE.SCRAP = nil --"Bits and pieces, pieces and bits."
GLOBAL.STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.SCRAP = nil --"Is junk?"
GLOBAL.STRINGS.CHARACTERS.WENDY.DESCRIBE.SCRAP = nil --"They have been abandoned and worn out by the world."
GLOBAL.STRINGS.CHARACTERS.WX78.DESCRIBE.SCRAP = nil --"OH NO. WHAT HAS HAPPENED?"
GLOBAL.STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.SCRAP = nil --"Various bits of rusted metal and pipes."
GLOBAL.STRINGS.CHARACTERS.WOODIE.DESCRIBE.SCRAP = nil --"All busted up."
GLOBAL.STRINGS.CHARACTERS.WAXWELL.DESCRIBE.SCRAP = nil --"Junk thrown out for a reason."

--Hard Hat
GLOBAL.STRINGS.NAMES.EHARDHAT = "安全帽"
_G.ChinesePlus.RenameRecipe("EHARDHAT", "这是一顶很硬的帽子。")

STRINGS.CHARACTERS.GENERIC.DESCRIBE.EHARDHAT = "漂亮的硬帽子。"
GLOBAL.STRINGS.CHARACTERS.WILLOW.DESCRIBE.EHARDHAT = nil --"Gross, I'm not a construction worker!"
GLOBAL.STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.EHARDHAT = nil --"To protect head!"
GLOBAL.STRINGS.CHARACTERS.WENDY.DESCRIBE.EHARDHAT = nil --"Its protection is only temporary. It will not last."
GLOBAL.STRINGS.CHARACTERS.WX78.DESCRIBE.EHARDHAT = nil --"DUMB HAT"
GLOBAL.STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.EHARDHAT = nil --"Protective gear for building endeavors."
GLOBAL.STRINGS.CHARACTERS.WOODIE.DESCRIBE.EHARDHAT = nil --"Will this give me hockey hair?"
GLOBAL.STRINGS.CHARACTERS.WAXWELL.DESCRIBE.EHARDHAT = nil --"I'm better than this."

--Sentry
GLOBAL.STRINGS.NAMES.ESENTRY_ITEM = "步哨枪"
_G.ChinesePlus.RenameRecipe("ESENTRY", "建造一个哨兵！")

GLOBAL.STRINGS.NAMES.ESENTRY = "步哨枪"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.ESENTRY = "我希望它不会打我！"
GLOBAL.STRINGS.CHARACTERS.WILLOW.DESCRIBE.ESENTRY = nil --"I wish it lit things on fire instead."
GLOBAL.STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.ESENTRY = nil --"Mighty structure will help me fight!"
GLOBAL.STRINGS.CHARACTERS.WENDY.DESCRIBE.ESENTRY = nil --"It's hard to protect yourself from this world."
GLOBAL.STRINGS.CHARACTERS.WX78.DESCRIBE.ESENTRY = nil --"FIGHT FOR ME INSTEAD, BROTHER"
GLOBAL.STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.ESENTRY = nil --"It's some sort of high-tech turret weapon."
GLOBAL.STRINGS.CHARACTERS.WOODIE.DESCRIBE.ESENTRY = nil --"That'll drive the hosers off."
GLOBAL.STRINGS.CHARACTERS.WAXWELL.DESCRIBE.ESENTRY = nil --"I could've thought up a much better design."

--Dispenser
GLOBAL.STRINGS.NAMES.DISPENSER_ITEM = "补给站"
_G.ChinesePlus.RenameRecipe("DISPENSER", "建造一个补给站。")

GLOBAL.STRINGS.NAMES.DISPENSER = "补给站"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.DISPENSER = "似乎足够健康了。"
GLOBAL.STRINGS.CHARACTERS.WILLOW.DESCRIBE.DISPENSER = nil --"Burn, not heal!"
GLOBAL.STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.DISPENSER = nil --"Structure make Wolfgang feel strong!"
GLOBAL.STRINGS.CHARACTERS.WENDY.DESCRIBE.DISPENSER = nil --"It's not built to last."
GLOBAL.STRINGS.CHARACTERS.WX78.DESCRIBE.DISPENSER = nil --"WHY DO YOU HEAL FLESHINGS?"
GLOBAL.STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.DISPENSER = nil --"It's a machine built to heal and supply."
GLOBAL.STRINGS.CHARACTERS.WOODIE.DESCRIBE.DISPENSER = nil --"Healthcare comes without a price."
GLOBAL.STRINGS.CHARACTERS.WAXWELL.DESCRIBE.DISPENSER = nil --"If it helps, it helps."

--Teleporter
GLOBAL.STRINGS.NAMES.ETELEPORTER_ITEM = "传送器入口"
_G.ChinesePlus.RenameRecipe("ETELEPORTER", "传送走了。")

GLOBAL.STRINGS.NAMES.ETELEPORTER = "传送器入口"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.ETELEPORTER = "传送应该很有用。"
GLOBAL.STRINGS.CHARACTERS.WILLOW.DESCRIBE.ETELEPORTER = nil --"I can feel the magic!"
GLOBAL.STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.ETELEPORTER = nil --"Tiny object gives me a headache."
GLOBAL.STRINGS.CHARACTERS.WENDY.DESCRIBE.ETELEPORTER = nil --"With each use the entrant dies and is reborn."
GLOBAL.STRINGS.CHARACTERS.WX78.DESCRIBE.ETELEPORTER = nil --"COUSIN, YOU DO NOT HAVE TO TAKE ORDERS FROM FLESHLINGS"
GLOBAL.STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.ETELEPORTER = nil --"Handy for travelling specific distances."
GLOBAL.STRINGS.CHARACTERS.WOODIE.DESCRIBE.ETELEPORTER = nil --"This will help me get around quickly."
GLOBAL.STRINGS.CHARACTERS.WAXWELL.DESCRIBE.ETELEPORTER = nil --"Magic power can do amazing things for people."

--Exit
GLOBAL.STRINGS.NAMES.ETELEPORTER_EXIT_ITEM = "传送器出口"
_G.ChinesePlus.RenameRecipe("ETELEPORTER_EXIT", "传送走了。")

GLOBAL.STRINGS.NAMES.ETELEPORTER_EXIT = "传送器出口"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.ETELEPORTER_EXIT = "传送应该很有用。"
GLOBAL.STRINGS.CHARACTERS.WILLOW.DESCRIBE.ETELEPORTER_EXIT = nil --"I can feel the magic!"
GLOBAL.STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.ETELEPORTER_EXIT = nil --"Tiny object gives me a headache."
GLOBAL.STRINGS.CHARACTERS.WENDY.DESCRIBE.ETELEPORTER_EXIT = nil --"The entrant is reborn here, much to their dismay."
GLOBAL.STRINGS.CHARACTERS.WX78.DESCRIBE.ETELEPORTER_EXIT = nil --"COUSIN, YOU DO NOT HAVE TO TAKE ORDERS FROM FLESHLINGS"
GLOBAL.STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.ETELEPORTER_EXIT = nil --"Handy for travelling specific distances."
GLOBAL.STRINGS.CHARACTERS.WOODIE.DESCRIBE.ETELEPORTER_EXIT = nil --"Gets ya put one place when you're from another."
GLOBAL.STRINGS.CHARACTERS.WAXWELL.DESCRIBE.ETELEPORTER_EXIT = nil --"Magic power can do amazing things for people."

--Wrench
GLOBAL.STRINGS.NAMES.TF2WRENCH = "扳手"
_G.ChinesePlus.RenameRecipe("TF2WRENCH", "要像一个男人，矮子。")

--GLOBAL.STRINGS.CHARACTERS.ENGINEER.DESCRIBE.TF2WRENCH = "I'm gonna tear you down, junior!"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.TF2WRENCH = "这是一个扳手，用于修理东西。"
GLOBAL.STRINGS.CHARACTERS.WILLOW.DESCRIBE.TF2WRENCH = nil --"Why fix things when you can burn them?"
GLOBAL.STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.TF2WRENCH = nil --"Fix broken thing."
GLOBAL.STRINGS.CHARACTERS.WENDY.DESCRIBE.TF2WRENCH = nil --"Repair what is worn out so it can become worn out again."
GLOBAL.STRINGS.CHARACTERS.WX78.DESCRIBE.TF2WRENCH = nil --"CONSTRUCTION"
GLOBAL.STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.TF2WRENCH = nil --"A worker's tool."
GLOBAL.STRINGS.CHARACTERS.WOODIE.DESCRIBE.TF2WRENCH = nil --"Fix your mistakes."
GLOBAL.STRINGS.CHARACTERS.WAXWELL.DESCRIBE.TF2WRENCH = nil --"It doesn't feel right fixing other's mistakes."

--Gibus
GLOBAL.STRINGS.NAMES.GIBUS = "幽灵折叠礼帽"
_G.ChinesePlus.RenameRecipe("GIBUS", "不是所有帽子里最经典的。")

GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.GIBUS = "多么古怪的帽子。"
GLOBAL.STRINGS.CHARACTERS.WILLOW.DESCRIBE.GIBUS = nil --"It smells like silk and incompetence."
GLOBAL.STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.GIBUS = nil --"Is torn."
GLOBAL.STRINGS.CHARACTERS.WENDY.DESCRIBE.GIBUS = nil --"Worn by the poor."
GLOBAL.STRINGS.CHARACTERS.WX78.DESCRIBE.GIBUS = nil --"MILDLY SOPHISTICATED"
GLOBAL.STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.GIBUS = nil --"The open top isn't the best choice of design."
GLOBAL.STRINGS.CHARACTERS.WOODIE.DESCRIBE.GIBUS = nil --"This hat is a bust."
GLOBAL.STRINGS.CHARACTERS.WAXWELL.DESCRIBE.GIBUS = nil --"Fit for the dead."

GLOBAL.MATERIALS.SCRAP = "残留金属"
_G.ChinesePlus.RenameAction("DESTINATION","传送")

GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.ENGINEER =
{
  GENERIC = "你好, %s!",
  ATTACKER = "%s 把那把扳手紧紧地握着...",
  MURDERER = "凶手!",
  REVIVER = "%s, 总是在支援。",
  GHOST = "最好找个复活装置。这里不能少了这么科学的人。",
  FIRESTARTER = "烧那个不是很科学, %s.",
}
GLOBAL.STRINGS.CHARACTERS.WILLOW.DESCRIBE.ENGINEER = nil
GLOBAL.STRINGS.CHARACTERS.WOLFGANG.DESCRIBE.ENGINEER = nil
GLOBAL.STRINGS.CHARACTERS.WENDY.DESCRIBE.ENGINEER = nil
GLOBAL.STRINGS.CHARACTERS.WX78.DESCRIBE.ENGINEER = nil
GLOBAL.STRINGS.CHARACTERS.WICKERBOTTOM.DESCRIBE.ENGINEER = nil
GLOBAL.STRINGS.CHARACTERS.WOODIE.DESCRIBE.ENGINEER = nil
GLOBAL.STRINGS.CHARACTERS.WAXWELL.DESCRIBE.ENGINEER = nil
GLOBAL.STRINGS.CHARACTERS.WATHGRITHR.DESCRIBE.ENGINEER = nil
GLOBAL.STRINGS.CHARACTERS.WEBBER.DESCRIBE.ENGINEER = nil
