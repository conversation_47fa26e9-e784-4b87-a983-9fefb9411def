_G.ChinesePlus.RenameTab("Turf","地毯")

--TURFED TURFS -yes its fun to say
STRINGS.NAMES.TURF_TEST = "測試用地毯。"

--Carpet
STRINGS.NAMES.TURF_CARPETBLACKFUR = "熊毛地毯"
_G.ChinesePlus.RenameRecipe("TURF_CARPETBLACKFUR", "建立一卷熊毛地毯。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_CARPETBLACKFUR = "從怪物毛皮製作的溫暖和舒適地毯。"

STRINGS.NAMES.TURF_CARPETBLUE = "藍色地毯"
_G.ChinesePlus.RenameRecipe("TURF_CARPETBLUE", "建立一卷藍色地毯。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_CARPETBLUE = "As blue as you."

STRINGS.NAMES.TURF_CARPETCAMO = "迷彩地毯"
_G.ChinesePlus.RenameRecipe("TURF_CARPETCAMO", "建立一卷迷彩地毯。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_CARPETCAMO = "我敢打賭你看不到這地毯。"

STRINGS.NAMES.TURF_CARPETFUR = "牛毛地毯"
_G.ChinesePlus.RenameRecipe("TURF_CARPETFUR", "建立一卷牛毛地毯。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_CARPETFUR = "溫暖，舒服，臭。"

STRINGS.NAMES.TURF_CARPETPINK = "粉紅地毯"
_G.ChinesePlus.RenameRecipe("TURF_CARPETPINK", "建立一卷粉紅地毯。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_CARPETPINK = "粉紅色的地毯？ 彷彿！"

STRINGS.NAMES.TURF_CARPETPURPLE = "紫色地毯"
_G.ChinesePlus.RenameRecipe("TURF_CARPETPURPLE", "建立一卷紫色地毯。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_CARPETPURPLE = "紫色是皇室的顏色，也是該地毯的顏色。"

STRINGS.NAMES.TURF_CARPETRED = "紅色地毯"
_G.ChinesePlus.RenameRecipe("TURF_CARPETRED", "建立一卷紅色地毯。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_CARPETRED = "他們告訴我把地毯洗的像漿果一樣白點會比較好..."

STRINGS.NAMES.TURF_CARPETRED2 = "鑽石地毯"
_G.ChinesePlus.RenameRecipe("TURF_CARPETRED2", "建立一卷鑽石地毯。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_CARPETRED2 = "像血鑽一樣光澤亮麗。"

STRINGS.NAMES.TURF_CARPETTD = "扎染地毯"
_G.ChinesePlus.RenameRecipe("TURF_CARPETTD", "建立一卷扎染地毯。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_CARPETTD = "這種地毯很男人，它像...哇..."

STRINGS.NAMES.TURF_CARPETWIFI = "無線地毯"
_G.ChinesePlus.RenameRecipe("TURF_CARPETWIFI", "建立一卷無線地毯。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_CARPETWIFI = "現在你有訊號了嗎？ 好吧。"
--Nature
STRINGS.NAMES.TURF_NATUREASTROTURF = "天文草坪"
_G.ChinesePlus.RenameRecipe("TURF_NATUREASTROTURF", "建立一卷天文草坪。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_NATUREASTROTURF = "不麻煩的草！"

STRINGS.NAMES.TURF_NATUREDESERT = "沙漠地皮"
_G.ChinesePlus.RenameRecipe("TURF_NATUREDESERT", "建立一些沙漠地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_NATUREDESERT = "你有一片乾裂的不毛之地。"
--Rock
STRINGS.NAMES.TURF_ROCKBLACKTOP = "柏油地皮"
_G.ChinesePlus.RenameRecipe("TURF_ROCKBLACKTOP", "建立一些柏油地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_ROCKBLACKTOP = "柏油，無盡，毫無意義。"

STRINGS.NAMES.TURF_ROCKGIRAFFE = "長頸鹿石地皮"
_G.ChinesePlus.RenameRecipe("TURF_ROCKGIRAFFE", "建立一些長頸鹿石地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_ROCKGIRAFFE = "從新鮮的長頸鹿製作。"

STRINGS.NAMES.TURF_ROCKMOON = "月岩地皮"
_G.ChinesePlus.RenameRecipe("TURF_ROCKMOON", "建立一些月岩地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_ROCKMOON = "月岩地皮，呼叫總部。"

STRINGS.NAMES.TURF_ROCKYELLOWBRICK = "黃磚地皮"
_G.ChinesePlus.RenameRecipe("TURF_ROCKYELLOWBRICK", "建立一些黃磚地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_ROCKYELLOWBRICK = "跟著它。"
--Tile
STRINGS.NAMES.TURF_TILECHECKERBOARD = "棋盤瓷磚"
_G.ChinesePlus.RenameRecipe("TURF_TILECHECKERBOARD", "建立一些棋盤瓷磚。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_TILECHECKERBOARD = "將軍。"

STRINGS.NAMES.TURF_TILEFROSTY = "霧瓷磚"
_G.ChinesePlus.RenameRecipe("TURF_TILEFROSTY", "建立一些霧瓷磚。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_TILEFROSTY = "你想創造一些瓷磚？"

STRINGS.NAMES.TURF_TILESQUARES = "廣場瓷磚"
_G.ChinesePlus.RenameRecipe("TURF_TILESQUARES", "建立一些廣場瓷磚。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_TILESQUARES = "這樣的格調，更加廣場。"
--Wood
STRINGS.NAMES.TURF_WOODCHERRY = "櫻花木地板"
_G.ChinesePlus.RenameRecipe("TURF_WOODCHERRY", "建立一些櫻花木地板。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_WOODCHERRY = "你在哪裡找到的櫻桃？"

STRINGS.NAMES.TURF_WOODDARK = "暗木地板"
_G.ChinesePlus.RenameRecipe("TURF_WOODDARK", "建立一些暗木地板。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_WOODDARK = "查理最喜歡的顏色。"

STRINGS.NAMES.TURF_WOODPINE = "松木地板"
_G.ChinesePlus.RenameRecipe("TURF_WOODPINE", "建立一些松木地板。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_WOODPINE = "讓松果有用的地板。"

--DST TURFS
STRINGS.NAMES.TURF_FOREST = "森林地皮"
_G.ChinesePlus.RenameRecipe("TURF_FOREST", "建立一塊森林地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_FOREST = "一塊森林地皮。"

STRINGS.NAMES.TURF_DECIDUOUS = "季節性地皮"
_G.ChinesePlus.RenameRecipe("TURF_DECIDUOUS", "建立一塊季節性地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_DECIDUOUS = "一塊季節性地皮。"

STRINGS.NAMES.TURF_GRASS = "青草地皮"
_G.ChinesePlus.RenameRecipe("TURF_GRASS", "建立一塊青草地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_GRASS = "一塊青草地皮。"

STRINGS.NAMES.TURF_SAVANNA = "熱帶草原地皮"
_G.ChinesePlus.RenameRecipe("TURF_SAVANNA", "建立一塊熱帶草原地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_SAVANNA = "一塊熱帶草原地皮。"

STRINGS.NAMES.TURF_DESERTDIRT = "沙漠地皮"
_G.ChinesePlus.RenameRecipe("TURF_DESERTDIRT", "建立一塊沙漠地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_DESERTDIRT = "一塊沙漠地皮。"

STRINGS.NAMES.TURF_MARSH = "沼澤地皮"
_G.ChinesePlus.RenameRecipe("TURF_MARSH", "建立一塊沼澤地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_MARSH = "一塊沼澤地皮。"

STRINGS.NAMES.TURF_FUNGUS = "藍菌地皮"
_G.ChinesePlus.RenameRecipe("TURF_FUNGUS", "建立一塊藍菌地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_FUNGUS = "一塊藍菌地皮。"

STRINGS.NAMES.TURF_FUNGUS_RED = "紅菌地皮"
_G.ChinesePlus.RenameRecipe("TURF_FUNGUS_RED", "建立一塊紅菌地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_FUNGUS_RED = "一塊紅菌地皮。"

STRINGS.NAMES.TURF_FUNGUS_GREEN = "綠菌地皮"
_G.ChinesePlus.RenameRecipe("TURF_FUNGUS_GREEN", "建立一塊綠菌地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_FUNGUS_GREEN = "一塊綠菌地皮。"

STRINGS.NAMES.TURF_MUD = "泥濘地皮"
_G.ChinesePlus.RenameRecipe("TURF_MUD", "建立一塊泥濘地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_MUD = "一塊泥濘地皮。"

STRINGS.NAMES.TURF_SINKHOLE = "汙水地皮"
_G.ChinesePlus.RenameRecipe("TURF_SINKHOLE", "建立一塊汙水地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_SINKHOLE = "一塊汙水地皮。"

STRINGS.NAMES.TURF_ROCKY = "岩石地皮"
_G.ChinesePlus.RenameRecipe("TURF_ROCKY", "建立一塊岩石地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_ROCKY = "一塊岩石地皮。"

STRINGS.NAMES.TURF_CAVE = "鳥糞地皮"
_G.ChinesePlus.RenameRecipe("TURF_CAVE", "建立一塊鳥糞地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_CAVE = "一塊鳥糞地皮。"

STRINGS.NAMES.TURF_UNDERROCK = "洞穴岩石地皮"
_G.ChinesePlus.RenameRecipe("TURF_UNDERROCK", "建立一塊洞穴岩石地皮。")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.TURF_UNDERROCK = "一塊洞穴岩石地皮。"

