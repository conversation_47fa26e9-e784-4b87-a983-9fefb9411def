local assets = {}
local prefabs = {}

-- 引入网络变量
local net_tinybyte = GLOBAL.net_tinybyte
local net_float = GLOBAL.net_float
local net_string = GLOBAL.net_string

local function UpdateAltarIcon(inst)
    if inst.MiniMapEntity == nil then return end
    local is_cooling = inst._cooldown and inst._cooldown > GetTime()
    if is_cooling then
        -- 冷却中：提高优先级并切换为更暖色的原版图标以示“冷却中”
        inst.MiniMapEntity:SetPriority(1)
        inst.MiniMapEntity:SetIcon("firepit.png")
    else
        inst.MiniMapEntity:SetPriority(0)
        inst.MiniMapEntity:SetIcon("moonbase.png")
    end
end

local function SyncAltarState(inst)
    if not TheWorld.ismastersim then return end

    -- 同步季芯数量
    inst._net_cores:set(inst._cores or 0)

    -- 同步冷却状态
    local cooldown_remaining = 0
    if inst._cooldown and inst._cooldown > GetTime() then
        cooldown_remaining = inst._cooldown - GetTime()
    end
    inst._net_cooldown:set(cooldown_remaining)

    -- 同步最后季芯类型
    if inst._last_core_season then
        inst._net_last_season:set(inst._last_core_season)
    else
        inst._net_last_season:set("")
    end
end

-- Boss死亡时触发祭坛冷却的回调函数
local function OnBossDefeated(inst)
    -- 读取配置项中的冷却天数
    local cooldown_days = GetModConfigData("altar_cooldown_days") or TUNING.SEASON_BOSS_COOLDOWN_DAYS or 5
    inst._cooldown = GetTime() + cooldown_days * (TUNING.TOTAL_DAY_TIME or 480)
    UpdateAltarIcon(inst)
    SyncAltarState(inst) -- 同步状态到客户端
    -- 通知玩家祭坛进入冷却
    for _, p in ipairs(AllPlayers or {}) do
        if p and p:IsValid() and p.components and p.components.talker then
            p.components.talker:Say(string.format("季祭坛进入冷却状态（%d天）。", cooldown_days))
        end
    end
end

local function TryActivate(inst, doer)
    if inst._cooldown and inst._cooldown > GetTime() then
        if doer and doer.components.talker then
            doer.components.talker:Say("祭坛正在冷却...")
        end
        return
    end

    -- 检查附近是否已有Boss存在，防止重复召唤
    local existing_boss = FindEntity(inst, 50, function(ent)
        return ent.prefab == "boss_season_warden" and ent._altar == inst
    end)
    if existing_boss then
        if doer and doer.components.talker then
            doer.components.talker:Say("季冠树守已在附近，无法重复召唤。")
        end
        return
    end

    if inst._cores and inst._cores >= 4 then
        -- 召唤Boss：生成季冠树守
        if inst.SoundEmitter then inst.SoundEmitter:PlaySound("dontstarve/common/teleportworm/travel") end
        local x,y,z = inst.Transform:GetWorldPosition()
        local boss = SpawnPrefab("boss_season_warden")
        if boss then
            boss.Transform:SetPosition(x+2, 0, z+2)
            -- 关键修复：监听Boss死亡事件，死亡后才触发祭坛冷却
            boss._altar = inst  -- 让Boss记住召唤它的祭坛

            -- 设置Boss的初始弱点季节（根据最后放入的季芯）
            if inst._last_core_season then
                boss._initial_weakness = inst._last_core_season
                -- Boss会在自己的初始化中使用_initial_weakness来设置_phase

                if doer and doer.components.talker then
                    local season_names = {
                        spring = "春季",
                        summer = "夏季",
                        autumn = "秋季",
                        winter = "冬季"
                    }
                    doer.components.talker:Say(string.format("季冠树守已被召唤！初始弱点：%s", season_names[inst._last_core_season]))
                end
            else
                -- 通用季芯召唤，使用随机初始弱点
                local phases = {"spring", "summer", "autumn", "winter"}
                local random_season = phases[math.random(1, 4)]
                boss._initial_weakness = random_season

                if doer and doer.components.talker then
                    doer.components.talker:Say("季冠树守已被召唤！")
                end
            end

            boss:ListenForEvent("death", function(boss_inst)
                if boss_inst._altar and boss_inst._altar:IsValid() then
                    OnBossDefeated(boss_inst._altar)
                end
            end)

            -- 优化：降低检查频率，减少性能消耗
            boss:DoPeriodicTask(5, function(boss_inst)
                if not boss_inst:IsValid() or not boss_inst._altar or not boss_inst._altar:IsValid() then
                    return
                end

                local altar_x, altar_y, altar_z = boss_inst._altar.Transform:GetWorldPosition()
                local boss_x, boss_y, boss_z = boss_inst.Transform:GetWorldPosition()
                local distance = math.sqrt((altar_x - boss_x)^2 + (altar_z - boss_z)^2)

                -- 如果距离祭坛超过50格，且没有玩家在附近
                if distance > 50 then
                    local nearby_player = FindEntity(boss_inst, 30, function(ent)
                        return ent:HasTag("player") and not ent:HasTag("playerghost")
                    end)

                    if not nearby_player then
                        -- 回归祭坛并缓慢回血
                        boss_inst.Transform:SetPosition(altar_x + 2, 0, altar_z + 2)
                        if boss_inst.components.health then
                            local heal_amount = boss_inst.components.health.maxhealth * 0.05  -- 每5秒回复5%血量（保持相同的回血速率）
                            boss_inst.components.health:DoDelta(heal_amount, true, "altar_return")
                        end

                        -- 通知玩家
                        for _, p in ipairs(AllPlayers or {}) do
                            if p and p:IsValid() and p.components and p.components.talker then
                                p.components.talker:Say("季冠树守回归了祭坛...")
                            end
                        end
                    end
                end
            end)
        else
            -- 召唤失败，返还季芯
            if doer and doer.components.talker then
                doer.components.talker:Say("召唤失败，季芯已返还。")
            end
            for i = 1, 4 do
                local core = SpawnPrefab("season_core")
                if core then
                    core.Transform:SetPosition(x + math.random(-2, 2), 0, z + math.random(-2, 2))
                end
            end
        end
        inst._cores = 0
        SyncAltarState(inst) -- 同步状态到客户端
        -- 移除立即冷却：现在只有Boss被击败后才冷却
        -- inst._cooldown = GetTime() + (TUNING.SEASON_BOSS_COOLDOWN_DAYS or 5) * (TUNING.TOTAL_DAY_TIME or 480)
        -- UpdateAltarIcon(inst)
    else
        if doer and doer.components.talker then
            doer.components.talker:Say("需要4枚季芯。")
        end
    end
end

local function OnAccept(inst, giver, item)
    -- 接受所有类型的季芯
    if item:HasTag("season_core") then
        inst._cores = (inst._cores or 0) + 1

        -- 记录最后放入的季芯类型，用于决定Boss初始弱点
        if item._season then
            inst._last_core_season = item._season
        elseif item.prefab == "season_core_spring" then
            inst._last_core_season = "spring"
        elseif item.prefab == "season_core_summer" then
            inst._last_core_season = "summer"
        elseif item.prefab == "season_core_autumn" then
            inst._last_core_season = "autumn"
        elseif item.prefab == "season_core_winter" then
            inst._last_core_season = "winter"
        else
            -- 通用季芯，使用当前世界季节
            inst._last_core_season = TheWorld.state.season or "spring"
        end

        -- 同步状态到客户端
        SyncAltarState(inst)

        -- 提供反馈给玩家
        if giver and giver.components.talker then
            if inst._cores >= 4 then
                giver.components.talker:Say("季芯已满，正在激活祭坛...")
            else
                giver.components.talker:Say(string.format("已放入%d/4枚季芯", inst._cores))
            end
        end

        if inst._cores >= 4 then
            inst:DoTaskInTime(0.2, function() TryActivate(inst, giver) end)
        end
        return true
    end
    return false
end

local function fn()
    local inst = CreateEntity()
    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    -- 小地图图标（复用 moonbase 图标颜色变化）
    if inst.components.minimap == nil then
        inst.entity:AddMiniMapEntity()
        inst.MiniMapEntity:SetIcon("moonbase.png")
    end

    inst.entity:AddSoundEmitter()
    inst.entity:AddNetwork()

    inst.AnimState:SetBank("moonbase")
    inst.AnimState:SetBuild("moonbase")
    inst.AnimState:PlayAnimation("idle")

    inst:AddTag("structure")

    -- 网络变量初始化
    inst._net_cores = net_tinybyte(inst.GUID, "season_altar.cores", "cores_dirty")
    inst._net_cooldown = net_float(inst.GUID, "season_altar.cooldown", "cooldown_dirty")
    inst._net_last_season = net_string(inst.GUID, "season_altar.last_season", "last_season_dirty")

    inst.entity:SetPristine()

    if not TheWorld.ismastersim then
        -- 客户端监听网络同步事件
        inst:ListenForEvent("cores_dirty", function()
            inst._cores = inst._net_cores:value()
        end)

        inst:ListenForEvent("cooldown_dirty", function()
            local cooldown_remaining = inst._net_cooldown:value()
            if cooldown_remaining > 0 then
                inst._cooldown = GetTime() + cooldown_remaining
            else
                inst._cooldown = nil
            end
            UpdateAltarIcon(inst)
        end)

        inst:ListenForEvent("last_season_dirty", function()
            local season = inst._net_last_season:value()
            inst._last_core_season = season ~= "" and season or nil
        end)

        return inst
    end

    inst._cores = 0

    -- 初始化网络变量
    inst._net_cores:set(0)
    inst._net_cooldown:set(0)
    inst._net_last_season:set("")

    inst:AddComponent("inspectable")
    inst.components.inspectable.getstatus = function(inst)
        if inst._cooldown and inst._cooldown > GetTime() then
            local remain = math.max(0, inst._cooldown - GetTime())
            local days = math.ceil(remain / (TUNING.TOTAL_DAY_TIME or 480))
            return string.format("冷却中（还需%d天）", days)
        elseif inst._cores and inst._cores > 0 then
            local status = string.format("已放入%d/4枚季芯", inst._cores)
            if inst._last_core_season then
                local season_names = {
                    spring = "春季",
                    summer = "夏季",
                    autumn = "秋季",
                    winter = "冬季"
                }
                status = status .. string.format("\\n下次召唤弱点：%s", season_names[inst._last_core_season])
            end
            return status
        else
            return "等待季芯激活\\n提示：最后放入的季芯类型将决定Boss初始弱点"
        end
    end

    inst:AddComponent("trader")
    inst.components.trader:SetAcceptTest(function(inst, item)
        return item:HasTag("season_core") and (not inst._cooldown or inst._cooldown <= GetTime())
    end)

    -- 优化：降低冷却提示频率，减少性能消耗和玩家干扰
    inst:DoPeriodicTask(30, function()
        if inst._cooldown and inst._cooldown > GetTime() and TheNet:GetIsServer() then
            local remain = math.max(0, inst._cooldown - GetTime())
            local days = math.ceil(remain / (TUNING.TOTAL_DAY_TIME or 480))
            -- 只在有玩家靠近时才提示
            local nearby_player = FindEntity(inst, 10, function(ent)
                return ent:HasTag("player") and not ent:HasTag("playerghost")
            end)
            if nearby_player and nearby_player.components and nearby_player.components.talker then
                nearby_player.components.talker:Say(string.format("季祭坛冷却中（%d天）", days))
            end
        end
    end)
    inst.components.trader.onaccept = OnAccept

    inst:AddComponent("lootdropper")

    -- 数据持久化：保存祭坛状态
    inst:AddComponent("savedrotation")  -- 用于保存数据

    -- 保存数据
    inst.OnSave = function(inst, data)
        data.cores = inst._cores or 0
        data.cooldown = inst._cooldown
        data.last_core_season = inst._last_core_season
    end

    -- 加载数据
    inst.OnLoad = function(inst, data)
        if data then
            inst._cores = data.cores or 0
            inst._cooldown = data.cooldown
            inst._last_core_season = data.last_core_season
            UpdateAltarIcon(inst)
        end
    end

    return inst
end

return Prefab("season_altar", fn, assets, prefabs)
