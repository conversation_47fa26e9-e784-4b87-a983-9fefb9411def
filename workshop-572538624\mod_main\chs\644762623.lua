--STRINGS.CHARACTERS.Miku = require(chinesefolder.."/Miku/speech_miku")
STRINGS.CHARACTERS.Miku = nil

STRINGS.NAMES.LEEK = "初音的韭菜"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.LEEK = "我问这韭菜怎么长这么大；—；"

STRINGS.NAMES.CANDY = "糖果^^"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CANDY = "这是一个糖果？"

-- The character select screen lines
STRINGS.CHARACTER_TITLES.miku = "说谎的女孩"
STRINGS.CHARACTER_NAMES.miku = "初音"
STRINGS.CHARACTER_DESCRIPTIONS.miku = "*寒冷免疫\n*自己就是个冰箱\n*有个韭菜"
STRINGS.CHARACTER_QUOTES.miku = "\" 一个螺丝…或螺栓？所以没关系！\""

_G.ChinesePlus.RenameRecipe("CANDY","也许我能做点糖果？我喜欢糖果 ^^")

STRINGS.NAMES.MIKU = "初音"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.MIKU =
{
  GENERIC = "它存在吗？",
  ATTACKER = "初音看起来很狡猾...",
  MURDERER = "嗯..",
  REVIVER = "她似乎很好。",
  GHOST = "我最好不要问你是怎么死的。",
}

