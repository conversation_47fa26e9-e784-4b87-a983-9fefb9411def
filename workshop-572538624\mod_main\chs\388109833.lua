STRINGS.NAMES.ACEHAT = "艾斯的帽子"
STRINGS.NAMES.ACEFIRE = "艾斯的火球"

-- The character select screen lines
STRINGS.CHARACTER_TITLES.ace = "第二师司令"
STRINGS.CHARACTER_NAMES.ace = "艾斯"
STRINGS.CHARACTER_DESCRIPTIONS.ace = "*火焰的辉光\n*火焰免疫\n*燃烧一切"
STRINGS.CHARACTER_QUOTES.ace = "\"我们必须过一种没有遗憾的生活。\""

-- Custom speech strings
--STRINGS.CHARACTERS.ACE = require(chinesefolder.."/Ace/speech_ace")
STRINGS.CHARACTERS.ACE = nil

-- The character's name as appears in-game
STRINGS.NAMES.ACE = "艾斯"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.ACE =
{
  GENERIC = "这是艾斯！",
  ATTACKER = "艾斯看起来很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "艾斯, 鬼魂朋友！",
  GHOST = "艾斯可以使用一颗心。",
}

