_G.ChinesePlus.RenameRecipe("CHESTER_EYEBONE", "召喚切斯特。")
_G.ChinesePlus.RenameRecipe("HUTCH_FISHBOWL", "召喚哈奇。")
_G.ChinesePlus.RenameRecipe("BUTTER", "黃油")
_G.ChinesePlus.RenameRecipe("BEARDHAIR", "你留鬍子嗎？")
_G.ChinesePlus.RenameRecipe("LUREPLANTBULB", "廢物處置的好地方。")
_G.ChinesePlus.RenameRecipe("REDGEM", "彩色寶石。")
_G.ChinesePlus.RenameRecipe("BLUEGEM", "彩色寶石。")
_G.ChinesePlus.RenameRecipe("GREENGEM", "彩色寶石。")
_G.ChinesePlus.RenameRecipe("ORANGEGEM", "彩色寶石。")
_G.ChinesePlus.RenameRecipe("YELLOWGEM", "彩色寶石。")
_G.ChinesePlus.RenameRecipe("WALRUSHAT", "漂亮的帽子。")
_G.ChinesePlus.RenameRecipe("KRAMPUS_SACK", "一個大揹包。")
_G.ChinesePlus.RenameRecipe("GEARS", "很實用的東西。")
_G.ChinesePlus.RenameRecipe("STEELWOOL", "噁心的動物，很少看到。")
_G.ChinesePlus.RenameRecipe("MOONROCKNUGGET", "來自天空的石頭。")
_G.ChinesePlus.RenameRecipe("THULECITE_PIECES", "使用它來建造更多科技。")
_G.ChinesePlus.RenameRecipe("MINOTAURHORN", "貌似沒什麼用。")
_G.ChinesePlus.RenameRecipe("ARMORSNURTLESHELL", "感覺很奇怪。")
_G.ChinesePlus.RenameRecipe("SLURTLEHAT", "酷。")
_G.ChinesePlus.RenameRecipe("WORMLIGHT", "光。")
_G.ChinesePlus.RenameRecipe("GOOSE_FEATHER", "柔軟的羽毛。")
_G.ChinesePlus.RenameRecipe("FURTUFT", "毛簇 * 30")
_G.ChinesePlus.RenameRecipe("DRAGON_SCALES", "看上去很難。")
_G.ChinesePlus.RenameRecipe("DEERCLOPS_EYEBALL", "一個生物的一部分")
_G.ChinesePlus.RenameRecipe("BLUEPRINT", "隨機。")
_G.ChinesePlus.RenameRecipe("LIVINGLOG", "活木。")
_G.ChinesePlus.RenameRecipe("MANDRAKESOUP", "美味啊。")
_G.ChinesePlus.RenameRecipe("PETALS_EVIL", "惡作劇。")
_G.ChinesePlus.RenameRecipe("MARBLE", "堅硬的石頭。")
_G.ChinesePlus.RenameRecipe("MANDRAKE_PLANTED", "奇怪的草，嚐起來很美味。")
_G.ChinesePlus.RenameRecipe("CATCOONDEN", "一隻貓的家。")
_G.ChinesePlus.RenameRecipe("WALRUS_CAMP", "有一隻海象和他的兒子。")
_G.ChinesePlus.RenameRecipe("ANCIENT_ALTAR_BROKEN", "解鎖更多的技術。")
_G.ChinesePlus.RenameRecipe("ANCIENT_ALTAR", "解鎖更多的技術。")
_G.ChinesePlus.RenameRecipe("POND", "只是一個池塘")
_G.ChinesePlus.RenameRecipe("POND_MOS", "只是一個池塘")
_G.ChinesePlus.RenameRecipe("LAVA_POND", "熱的池塘")
_G.ChinesePlus.RenameRecipe("POND_CAVE", "只是一個池塘")
_G.ChinesePlus.RenameRecipe("MERMHOUSE", "魚的房子。")
_G.ChinesePlus.RenameRecipe("RESURRECTIONSTONE", "復活.")
_G.ChinesePlus.RenameRecipe("BEEHIVE", "蜜蜂群。")
_G.ChinesePlus.RenameRecipe("WASPHIVE", "一群殺人蜂。")
_G.ChinesePlus.RenameRecipe("SPIDERHOLE", "地下蜘蛛巢。")
_G.ChinesePlus.RenameRecipe("SLURTLEHOLE", "蝸牛窩。")
_G.ChinesePlus.RenameRecipe("BATCAVE", "討厭的傢伙。")
_G.ChinesePlus.RenameRecipe("MONKEYBARREL", "猴子呆在這裡。")
_G.ChinesePlus.RenameRecipe("TALLBIRDNEST", "長腿鳥的巢。")
_G.ChinesePlus.RenameRecipe("HOUNDMOUND", "獵犬的出生地。")
_G.ChinesePlus.RenameRecipe("CAVE_BANANA_TREE", "香蕉。")
_G.ChinesePlus.RenameRecipe("STATUEGLOMMER", "奇怪的雕像。")
_G.ChinesePlus.RenameRecipe("BABYBEEFALO", "小牛。")
_G.ChinesePlus.RenameRecipe("CAVE_ENTRANCE_OPEN", "洞穴的入口")
_G.ChinesePlus.RenameRecipe("CAVE_EXIT", "退出洞穴。")
_G.ChinesePlus.RenameRecipe("WORMLIGHT_PLANT", "奇怪的植物。")
