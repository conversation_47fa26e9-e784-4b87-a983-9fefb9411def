_G.STRINGS.TABS["<PERSON>'s Tab"] = "梅"

-- The character select screen lines
STRINGS.CHARACTER_TITLES.mea = "一缕记忆碎片"
STRINGS.CHARACTER_NAMES.mea = "梅"
STRINGS.CHARACTER_DESCRIPTIONS.mea = "*她把制造一个可以睡觉和吃的生物。\n*牛奶和羊毛可以在满月收获。\n*到处都可以睡觉。"
STRINGS.CHARACTER_QUOTES.mea = "\"请教我你的记忆...\""

-- Custom speech strings
STRINGS.CHARACTERS.MEA = nil --require "speech_mea"

-- The character's name as appears in-game
STRINGS.NAMES.MEA = "梅"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MEA =
{
	GENERIC = "这是梅!",
	ATTACKER = "梅看起来很狡猾...",
	MURDERER = "凶手!",
	REVIVER = "梅, 鬼魂之友.",
	GHOST = "梅可以使用救赎之心.",
}

GLOBAL.STRINGS.NAMES.DREAMCANDY = "梦醒掉落"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.DREAMCANDY = "这似乎是短暂和清晰的。"
_G.ChinesePlus.RenameRecipe("DREAMCANDY", "梦是现实的力量！")

GLOBAL.STRINGS.NAMES.COWBELL = "牛铃"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.COWBELL = "那是一个有光泽的钟。"
_G.ChinesePlus.RenameRecipe("COWBELL", "让我们玩“叮叮”。")

GLOBAL.STRINGS.NAMES.SHEEPMILK = "羊奶"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.SHEEPMILK = "这是泥泞但甜的气味。"
_G.ChinesePlus.RenameRecipe("SHEEPMILK", "一种肤浅的浑浊的香味。")

GLOBAL.STRINGS.NAMES.DREAMWOOL = "梦之羊毛"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.DREAMWOOL = "这是软的，但不普通。"
_G.ChinesePlus.RenameRecipe("DREAMWOOL", "看起来像绵羊的头发。")

GLOBAL.STRINGS.NAMES.DREAMPILLOW = "梦之枕头"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.DREAMPILLOW = "我不知道它是由什么制成的。"
_G.ChinesePlus.RenameRecipe("DREAMPILLOW", "它保证会睡个好觉！")

GLOBAL.STRINGS.NAMES.MEA_MANT = "梅的钻石"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.MEA_MANT = "我不知道它是由什么制成的。"
_G.ChinesePlus.RenameRecipe("MEA_MANT", "羊可以和这个一起睡。")

GLOBAL.STRINGS.NAMES.DREAMLANTERN = "灵魂碎片"
GLOBAL.STRINGS.CHARACTERS.GENERIC.DESCRIBE.DREAMLANTERN = "我不知道它是由什么制成的。"
_G.ChinesePlus.RenameRecipe("DREAMLANTERN", "饥饿和燃料！")
