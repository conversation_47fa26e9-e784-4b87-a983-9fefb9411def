-- The character select screen lines
STRINGS.CHARACTER_TITLES.crashbandi = "實驗品"
STRINGS.CHARACTER_NAMES.crashbandi = "Crash"
STRINGS.CHARACTER_DESCRIPTIONS.crashbandi = "*伴隨他的同伴 AKU AKU 出生。\n*有一個大的理智帽子並且比別人快。\n*砸碎箱子，吃了海象！"
STRINGS.CHARACTER_QUOTES.crashbandi = "\"哇！\""

-- Custom speech strings
--STRINGS.CHARACTERS.CRASHBANDI = require(chinesefolder.."/Crash/speech_crashbandi")
STRINGS.CHARACTERS.CRASHBANDI = nil

--STRINGS.NAMES.FOLLOWER = "Aku Aku"
--STRINGS.CHARACTERS.GENERIC.DESCRIBE.FOLLOWER = "An ancient spirit lies within this tiki mask."
STRINGS.NAMES.WUMPA = "Wumpa"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.WUMPA = "如果我收集一百個可以獲得額外的生命嗎？"
STRINGS.NAMES.WUMPA_COOKED = "烤Wumpa"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.WUMPA_COOKED = "在我的比賽中從來沒有看到過！"
STRINGS.NAMES.WUMPA_PLANTED = "Wumpa箱"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.WUMPA_PLANTED = "我可以把這隻箱子打碎嗎？"

-- The character's name as appears in-game
STRINGS.NAMES.CRASHBANDI = "Crash"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CRASHBANDI =
{
  GENERIC = "這是隻袋鼠!",
  ATTACKER = "那隻袋鼠看起來很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "Crash, 不是 Casper, 鬼魂朋友！",
  GHOST = "那隻袋鼠可以用一顆心。",
}

STRINGS.NAMES.AKUAKU = "Aku Aku"
--STRINGS.CHARACTERS.CRASHBANDI.DESCRIBE.AKUAKU = "Aku Aku!"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.AKUAKU = "我可以發誓這是我從另一個遊戲看到的夏威夷面具。"
