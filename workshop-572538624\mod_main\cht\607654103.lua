STRINGS.CHARACTER_NAMES.warly = "沃利"
STRINGS.CHARACTER_DESCRIPTIONS.warly = "*傑出的味道\n*用定製的炊具做飯\n*有一個時尚廚師口袋"
STRINGS.CHARACTER_QUOTES.warly = "\"祝你有個好胃口！\""
STRINGS.CHARACTER_TITLES.warly = "大廚"

--STRINGS.CHARACTERS.WARLY = require(chinesefolder.."/Warly/speech_warly")
STRINGS.CHARACTERS.WARLY = nil

STRINGS.NAMES.WARLY = "沃利"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.WARLY =
{
  GENERIC = "這是沃利",
  ATTACKER = "沃利看起來很狡猾...",
  MURDERER = "沃利！",
  REVIVER = "沃利, 鬼魂朋友！",
  GHOST = "沃利可以使用一顆心。",
}

STRINGS.NAMES.PORTABLECOOKPOT = "行動式鍋"
_G.ChinesePlus.RenameRecipe("PORTABLECOOKPOT","沃利的行動式鍋")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.PORTABLECOOKPOT = "我們要進行什麼新的烹飪冒險，老朋友。"

STRINGS.NAMES.PORTABLECOOKPOT_ITEM = "行動式鍋"
_G.ChinesePlus.RenameRecipe("PORTABLECOOKPOT_ITEM","沃利的行動式鍋")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.PORTABLECOOKPOT_ITEM = "我們要進行什麼新的烹飪冒險，老朋友。"

STRINGS.NAMES.SPICEPACK = "廚師袋"
_G.ChinesePlus.RenameRecipe("SPICEPACK","沃利的廚師袋...")
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SPICEPACK = "裡面有一包廚師技巧！"

STRINGS.NAMES.SWEETPOTATOSOUFFLE = "甘薯蛋奶酥"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SWEETPOTATOSOUFFLE = "甘薯蛋奶酥"

STRINGS.NAMES.MONSTERTARTARE = "怪物韃靼"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MONSTERTARTARE = "怪物韃靼"

STRINGS.NAMES.FRESHFRUITCREPES = "水果薄餅"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.FRESHFRUITCREPES = "水果薄餅"

STRINGS.NAMES.MUSSELBOUILLABAISE = "貽貝濃湯"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MUSSELBOUILLABAISE = "貽貝濃湯"

