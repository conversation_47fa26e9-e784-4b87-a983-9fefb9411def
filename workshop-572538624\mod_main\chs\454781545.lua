STRINGS.NAMES.SANJIHAT = "香烟"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SANJIHAT = "不健康但可以放松一下。"
_G.ChinesePlus.RenameRecipe("SANJIHAT","标准的香烟。")

-- The character select screen lines
STRINGS.CHARACTER_TITLES.sanji = "厨师长"
STRINGS.CHARACTER_NAMES.sanji = "山治"
STRINGS.CHARACTER_DESCRIPTIONS.sanji = "*出色的厨师\n*喜欢女人\n*支柱"
STRINGS.CHARACTER_QUOTES.sanji = "\"我需要女士！！！！\""

-- Custom speech strings
--STRINGS.CHARACTERS.SANJI = require(chinesefolder.."/Sanji/speech_sanji")
STRINGS.CHARACTERS.SANJI = nil

-- The character's name as appears in-game
STRINGS.NAMES.SANJI = "山治"

-- The default responses of examining the character
STRING<PERSON>.CHARACTERS.GENERIC.DESCRIBE.SANJI =
{
  GENERIC = "这是山治！",
  ATTACKER = "山治看起来很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "山治, 鬼魂朋友！",
  GHOST = "山治可以使用一颗心。",
}
