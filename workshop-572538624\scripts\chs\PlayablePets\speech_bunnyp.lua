return {
	ACTIONFAIL =
	{
		BUILD =
		{
			MOUNTED = "太高了！",
		},
		SHAVE =
		{
			AWAKEBEEFALO = "或许等到它睡死了。",
			GENERIC = "兔男不能刮！",
			NOBITS = "没有了！",
		},
		STORE =
		{
			GENERIC = "没空间！",
			NOTALLOWED = "不！",
			INUSE = "正在使用！",
		},
		RUMMAGE =
		{
			GENERIC = "不能做这个！",
			INUSE = "正在使用！",
		},
		COOK =
		{
			GENERIC = "不能做这个！",
			INUSE = "必须等等！",
			TOOFAR = "太远了！",
		},
		GIVE =
		{
			DEAD = "兔男拿着这个！",
			SLEEPING = "太困了！",
			BUSY = "或许再等等！",
		},
		GIVETOPLAYER =
		{
			FULL = "满了！",
			DEAD = "死了",
			SLEEPING = "太困了！",
			BUSY = "或许再等等！",
		},
		GIVEALLTOPLAYER =
		{
			FULL = "满了",
			DEAD = "死了",
			SLEEPING = "太困了！",
			BUSY = "或许再等等！",
		},
		WRITE =
		{
			GENERIC = "不能做这个！",
			INUSE = "朋友先乱写的！",
		},
		CHANGEIN =
		{
			GENERIC = "不！",
			BURNING = "太吓人了！",
			INUSE = "等等才轮到你！",
		},
		ATTUNE =
		{
			NOHEALTH = "感觉不太好！",
		},
		MOUNT =
		{
			TARGETINCOMBAT = "太生气了！",
			INUSE = "我太迟了！",
		},
		SADDLE =
		{
			TARGETINCOMBAT = "它生气时不让我做这个。",
		},
	},
	ACTIONFAIL_GENERIC = "不！",
	ANNOUNCE_ADVENTUREFAIL = "我下次会做得更好！",
	ANNOUNCE_MOUNT_LOWHEALTH = "毛还好吗？",
	ANNOUNCE_BEES = "嗡嗡嗡的虫子！",
	ANNOUNCE_BOOMERANG = "木头混蛋！",
	ANNOUNCE_CHARLIE = "怪物在这里！",
	ANNOUNCE_CHARLIE_ATTACK = "受伤了！帮助兔男！",
	ANNOUNCE_COLD = "好冷！",
	ANNOUNCE_HOT = "好热！",
	ANNOUNCE_CRAFTING_FAIL = "东西不够！",
	ANNOUNCE_DEERCLOPS = "兔男感觉不好！",
	ANNOUNCE_DUSK = "天太暗了！",
	ANNOUNCE_EAT =
	{
		GENERIC = "好吃！",
		PAINFUL = "食物伤害！",
		SPOILED = "食物总量",
		STALE = "食物不新鲜",
		INVALID = "兔男有标准",
		YUCKY = "我拒绝！",
	},
	ANNOUNCE_ENTER_DARK = "天黑了！",
	ANNOUNCE_ENTER_LIGHT = "有光就好！",
	ANNOUNCE_FREEDOM = "我，自由！",
	ANNOUNCE_HIGHRESEARCH = "我，天才！",
	ANNOUNCE_HOUNDS = "危险的声音",
	ANNOUNCE_WORMS = "我感觉到了有东西靠近！",
	ANNOUNCE_HUNGRY = "我饿了！",
	ANNOUNCE_HUNT_BEAST_NEARBY = "我闻到野兽在这里！",
	ANNOUNCE_HUNT_LOST_TRAIL = "我找不到踪迹",
	ANNOUNCE_HUNT_LOST_TRAIL_SPRING = "道路太湿了！",
	ANNOUNCE_INV_FULL = "我太满了",
	ANNOUNCE_KNOCKEDOUT = "我的头很痛...",
	ANNOUNCE_LOWRESEARCH = "我学会了点",
	ANNOUNCE_MOSQUITOS = "烦人的虫子！",
	ANNOUNCE_NOWARDROBEONFIRE = "太热了！",
	ANNOUNCE_NODANGERGIFT = "现在很危险！",
	ANNOUNCE_NODANGERSLEEP = "太吓人了！",
	ANNOUNCE_NODAYSLEEP = "不是睡眠时间！",
	ANNOUNCE_NODAYSLEEP_CAVE = "我不累",
	ANNOUNCE_NOHUNGERSLEEP = "我没有睡觉的小吃",
	ANNOUNCE_NOSLEEPONFIRE = "太热了！",
	ANNOUNCE_NODANGERSIESTA = "太吓人了！",
	ANNOUNCE_NONIGHTSIESTA = "太暗了！",
	ANNOUNCE_NONIGHTSIESTA_CAVE = "洞穴太可怕！",
	ANNOUNCE_NOHUNGERSIESTA = "我太饿了！",
	ANNOUNCE_NODANGERAFK = "我必须攻击！",
	ANNOUNCE_NO_TRAP = "太容易了！",
	ANNOUNCE_PECKED = "坏鸟！",
	ANNOUNCE_QUAKE = "地面太生气了！",
	ANNOUNCE_RESEARCH = "我聪明了！",
	ANNOUNCE_SHELTER = "树，帮我！",
	ANNOUNCE_THORNS = "植物是小气鬼",
	ANNOUNCE_BURNT = "热！",
	ANNOUNCE_TORCH_OUT = "发光的树枝走了！",
	ANNOUNCE_FAN_OUT = "风扇走了！",
	ANNOUNCE_COMPASS_OUT = "上吧！兔男！",
	ANNOUNCE_TRAP_WENT_OFF = "我失败了！",
	ANNOUNCE_UNIMPLEMENTED = "也许还没有准备好！",
	ANNOUNCE_WORMHOLE = "洞是可怕的！",
	ANNOUNCE_CANFIX = "\n我可以帮助！",
	ANNOUNCE_ACCOMPLISHMENT = "我做到了！ ",
	ANNOUNCE_ACCOMPLISHMENT_DONE = "我必须给村民看！",
	ANNOUNCE_INSUFFICIENTFERTILIZER = "植物是饿了吗？",
	ANNOUNCE_TOOL_SLIP = "回来！",
	ANNOUNCE_LIGHTNING_DAMAGE_AVOIDED = "我太强大了！",

	ANNOUNCE_DAMP = "兔男湿了！",
	ANNOUNCE_WET = "兔男的衣服湿了",
	ANNOUNCE_WETTER = "我讨厌潮湿",
	ANNOUNCE_SOAKED = "我太湿了！",

	ANNOUNCE_BECOMEGHOST = "ooOOoooOOOoOoooink！！",
	ANNOUNCE_GHOSTDRAIN = "我失去了理智！",

	DESCRIBE_SAMECHARACTER = "我看起来更好！",

	BATTLECRY =
	{
		GENERIC = "我攻击！",
		PIG = "其他的兔男真丑！",
		PREY = "现在摧毁它！",
		SPIDER = "兔男讨厌蜘蛛！",
		SPIDER_WARRIOR = "兔男更讨厌黄色的蜘蛛！",
	},
	COMBAT_QUIT =
	{
		GENERIC = "兔男太吓人！",
		PIG = "兔男让他活到现在。",
		PREY = "它太害怕兔男！兔男赢了！",
		SPIDER = "你不值得呢！",
		SPIDER_WARRIOR = "再见了，笨蛋！",
	},
	DESCRIBE =
	{

		BERNIE_INACTIVE =
		{
			BROKEN = "可怜的玩具。",
			GENERIC = "玩具被烧了。",
		},
		BERNIE_ACTIVE = "玩具正在移动！",


		PLAYER =
		{
			GENERIC = "%s 是朋友！",
			ATTACKER = "我不相信 %s...",
			MURDERER = "%s 不是朋友！",
			REVIVER = "%s 死了很多。",
			GHOST = "%s 阴森森的。",
		},
		WILSON =
		{
			GENERIC = "头发有趣的人",
			ATTACKER = "威尔逊看上去狡猾...",
			MURDERER = "杀人凶手！",
			REVIVER = "威尔逊，朋友的鬼魂。",
			GHOST = "威尔逊可以使用一个心。",
		},
		WOLFGANG =
		{
			GENERIC = "这是沃尔夫冈！",
			ATTACKER = "沃尔夫冈看起来很狡猾...",
			MURDERER = "杀人凶手！",
			REVIVER = "沃尔夫冈，朋友的鬼魂。",
			GHOST = "沃尔夫冈可以使用一个心。",
		},
		WAXWELL =
		{
			GENERIC = "这是麦斯威尔！",
			ATTACKER = "麦斯威尔看起来很狡猾...",
			MURDERER = "杀人凶手！",
			REVIVER = "麦斯威尔，朋友的鬼魂。",
			GHOST = "麦斯威尔可以使用一个心。",
		},
		WX78 =
		{
			GENERIC = "这是WX-78！",
			ATTACKER = "WX-78看起来很狡猾...",
			MURDERER = "杀人凶手！",
			REVIVER = "WX-78，朋友的鬼魂。",
			GHOST = "WX-78可以使用一个心。",
		},
		WILLOW =
		{
			GENERIC = "这是薇洛！",
			ATTACKER = "薇洛看起来很狡猾...",
			MURDERER = "杀人凶手！",
			REVIVER = "薇洛，朋友的鬼魂。",
			GHOST = "薇洛可以使用一个心。",
		},
		WENDY =
		{
			GENERIC = "这是温蒂！",
			ATTACKER = "温蒂看起来很狡猾...",
			MURDERER = "杀人凶手！",
			REVIVER = "温蒂，朋友的鬼魂。",
			GHOST = "温蒂可以使用一个心。",
		},
		WOODIE =
		{
			GENERIC = "这是伍迪！",
			ATTACKER = "伍迪看起来很狡猾...",
			MURDERER = "杀人凶手！",
			REVIVER = "伍迪，朋友的鬼魂。",
			GHOST = "伍迪可以使用一个心。",
		},
		WICKERBOTTOM =
		{
			GENERIC = "这是薇克波顿！",
			ATTACKER = "薇克波顿看起来很狡猾...",
			MURDERER = "杀人凶手！",
			REVIVER = "薇克波顿，朋友的鬼魂。",
			GHOST = "薇克波顿可以使用一个心。",
		},
		WES =
		{
			GENERIC = "这是韦斯！",
			ATTACKER = "韦斯看起来很狡猾...",
			MURDERER = "杀人凶手！",
			REVIVER = "韦斯，朋友的鬼魂。",
			GHOST = "韦斯可以使用一个心。",
		},
		MULTIPLAYER_PORTAL = "我用这个去了新的地方",
		MIGRATION_PORTAL = {
			GENERIC = "如果我有朋友，这可以带我去他们那。",
			OPEN = "如果我一步一步过去，我还是我吗？",
			FULL = "似乎很受欢迎。",
		},
		GLOMMER = "他很可爱！",
		GLOMMERFLOWER =
		{
			GENERIC = "花的气味",
			DEAD = "现在，花和死亡的气味",
		},
		GLOMMERWINGS = "我会想你的！",
		GLOMMERFUEL = "这是宝贵的便便",
		BELL = "它将召唤众神",
		STATUEGLOMMER =
		{
			GENERIC = "蜘蛛侠雕像",
			EMPTY = "蜘蛛侠雕像不是朋友",
		},

		WEBBERSKULL = "继续死着",
		WORMLIGHT = "那是食物吗？",
		WORMLIGHT_LESSER = "那是食物吗？",
		WORM =
		{
			PLANT = "我想植物",
			DIRT = "臭污垢",
			WORM = "蠕虫的意思",
		},
		WORMLIGHT_PLANT = "我想要它",
		MOLE =
		{
			HELD = "你好",
			UNDERGROUND = "我最好看一下我掉的东西",
			ABOVEGROUND = "你好小鼹鼠！",
		},
		MOLEHILL = "它像一个兔男洞，但是蠕虫的",
		MOLEHAT = "兔男为蠕虫感到抱歉",

		EEL = "猪很快就会吃",
		EEL_COOKED = "会让猪饥饿",
		UNAGI = "猪是好菜",
		EYETURRET = "我现在感到很安全",
		EYETURRET_ITEM = "这有助于兔男",
		MINOTAURHORN = "古老的岩石碎片",
		MINOTAURCHEST = "兔男必须打开！",
		THULECITE_PIECES = "小的金子碎片",
		POND_ALGAE = "丑陋的东西",
		GREENSTAFF = "兔男最后还是很效率",
		POTTEDFERN = "兔男喜欢这个",

		THULECITE = "在过去使用过",
		ARMORRUINS = "上帝之触",
		RUINS_BAT = "凶手的武器",
		RUINSHAT = "兔男将大说特说。",
		NIGHTMARE_TIMEPIECE =
		{
			CALM = "一切都很好",
			WARN = "也许很好",
			WAXING = "不是很好",
			STEADY = "仍然不好",
			WANING = "变好了吗？",
			DAWN = "现在很好",
			NOMAGIC = "一切完美",
		},
		BISHOP_NIGHTMARE = "我看起来更好了",
		ROOK_NIGHTMARE = "坏了仍旧不友好",
		KNIGHT_NIGHTMARE = "即使坏了我也不喜欢你",
		MINOTAUR = "我听过你的故事",
		SPIDER_DROPPER = "蜘蛛在洞里的天上吗？",
		NIGHTMARELIGHT = "它在我的噩梦里燃烧",
		NIGHTSTICK = "这令人震惊",
		GREENGEM = "绿宝石是闪亮的",
		RELIC = "兔男有更好的东西",
		RUINS_RUBBLE = "兔男会帮助你把这个做的更好",
		MULTITOOL_AXE_PICKAXE = "兔男制造最好的工具",
		ORANGESTAFF = "我觉得拿着这个就像国王一样",
		YELLOWAMULET = "我感到温暖和富有",
		GREENAMULET = "我更加高效",
		SLURPERPELT = "它还令我忍俊不禁",

		SLURPER = "饥饿的搞笑怪兽",
		SLURPER_PELT = "它还令我忍俊不禁",
		ARMORSLURPER = "很臭的盔甲",
		ORANGEAMULET = "兔男有移动魔法了",
		YELLOWSTAFF = "兔男可以召唤太阳",
		YELLOWGEM = "温暖的发光的石头",
		ORANGEGEM = "最喜欢的石头",
		TELEBASE =
		{
			VALID = "它很好",
			GEMS = "需要更多的邪恶石头",
		},
		GEMSOCKET =
		{
			VALID = "它很好",
			GEMS = "需要邪恶石头",
		},
		STAFFLIGHT = "兔男感觉很强大",

		ANCIENT_ALTAR = "和图纸上的一样",

		ANCIENT_ALTAR_BROKEN = "鱼人做的吗？",

		ANCIENT_STATUE = "过去的兔男吗？",

		LICHEN = "奇怪的植物",
		CUTLICHEN = "奇怪但美味",

		CAVE_BANANA = "从来没有见过这个",
		CAVE_BANANA_COOKED = "兔男的嘴流着口水",
		CAVE_BANANA_TREE = "好吃好吃树",
		ROCKY = "它看起来很不友好",

		COMPASS =
		{
			GENERIC="哪条路！",
			N = "北",
			S = "南",
			E = "东",
			W = "西",
			NE = "东北",
			SE = "东南",
			NW = "西北",
			SW = "西南",
		},

		NIGHTMARE_TIMEPIECE =
		{
			WAXING = "意味着事情发生",
			STEADY = "意味着的东西还在这里",
			WANING = "意味着东西离开了",
			DAWN = "意味着东西几乎消失了",
			WARN = "兔男预言的事情",
			CALM = "一切都好",
			NOMAGIC = "意味着这里没有什么",
		},

		HOUNDSTOOTH="坏狗的奖杯",
		ARMORSNURTLESHELL= "我觉得安全！",
		BAT="蜘蛛飞了！？",
		BATBAT = "飞翔蜘蛛的力量",
		BATWING="所有蜘蛛都很臭！",
		BATWING_COOKED="谁会吃这个",
		BATCAVE = "兔男应该小心",
		BEDROLL_FURRY="兔男不知道如何感觉这个！",
		BUNNYMAN="你好，兄弟！",
		FLOWER_CAVE="科学使它发光。",
		FLOWER_CAVE_DOUBLE="科学使它发光。",
		FLOWER_CAVE_TRIPLE="科学使它发光。",
		GUANO="兔男白大便",
		LANTERN="人类喜欢使用这个",
		LIGHTBULB="安全的灯",
		MANRABBIT_TAIL="它帮助我忘记我的兄弟死了！",
		MUSHTREE_TALL = {
			GENERIC = "食物大树",
			BLOOM = "好漂亮啊！",
		},
		MUSHTREE_MEDIUM = {
			GENERIC = "不是很好吃的食物树",
			BLOOM = "它在繁殖",
		},
		MUSHTREE_SMALL = {
			GENERIC = "你好，食物树",
			BLOOM = "它射出漂亮的灯光",
		},
		MUSHTREE_TALL_WEBBED = "被肉食者破坏了",
		SPORE_TALL = "你真漂亮",
		SPORE_MEDIUM = "你好，小小的光",
		SPORE_SMALL = "是小小的发光朋友吗？",
		SPORE_TALL_INV = "漂亮的东西是我的！",
		SPORE_MEDIUM_INV = "小小的光喜欢这里",
		SPORE_SMALL_INV = "朋友在我的口袋里",
		RABBITHOUSE=
		{
			GENERIC = " 展示",
			BURNT = "现在谁的房子更好？",
		},
		SLURTLE="我认为你很丑，但是可爱。",
		SLURTLE_SHELLPIECES="这曾经是个家",
		SLURTLEHAT="我现在有丑陋的粘液了。",
		SLURTLEHOLE="我想看看里面。",
		SLURTLESLIME="和它玩的很好",
		SNURTLE="兔男没有经常看到他们",
		SPIDER_HIDER="他们喜欢躲起来！",
		SPIDER_SPITTER="蜘蛛在丑陋的房子里不应该吐出石头",
		SPIDERHOLE="它很恶心",
		STALAGMITE="大自然是整洁的",
		STALAGMITE_FULL="大自然是整洁的",
		STALAGMITE_LOW="大自然是整洁的",
		STALAGMITE_MED="大自然是整洁的",
		STALAGMITE_TALL="大自然是整洁的",
		STALAGMITE_TALL_FULL="大自然是整洁的",
		STALAGMITE_TALL_LOW="大自然是整洁的",
		STALAGMITE_TALL_MED="大自然是整洁的",

		TURF_CARPETFLOOR = "软软的",
		TURF_CHECKERFLOOR = "讨厌的人喜欢这个",
		TURF_DIRT = "泥土",
		TURF_FOREST = "家里的草坪",
		TURF_GRASS = "看起来很舒服",
		TURF_MARSH = "它比较臭",
		TURF_ROAD = "更好的地板",
		TURF_ROCKY = "不会睡在这上面",
		TURF_SAVANNA = "希望毛茸茸的野兽不吃它",
		TURF_WOODFLOOR = "猪王有这个",

		TURF_CAVE="它不软",
		TURF_FUNGUS="另一种地面类型。",
		TURF_SINKHOLE="另一种地面类型。",
		TURF_UNDERROCK="另一种地面类型。",
		TURF_MUD="另一种地面类型。",

		TURF_DECIDUOUS = "另一种地面类型。",
		TURF_SANDY = "另一种地面类型。",
		TURF_BADLANDS = "另一种地面类型。",

		POWCAKE = "我不饿",
		CAVE_ENTRANCE = "这通向家",
		CAVE_ENTRANCE_RUINS = "这可能藏着什么。",
		CAVE_ENTRANCE_OPEN = {
			GENERIC = "哦",
			OPEN = "这儿通向家！",
			FULL = "现在还不能回家",
		},
		CAVE_EXIT = {
			GENERIC = "兔男会在家呆一会儿",
			OPEN = "兔男想探索地面",
			FULL = "太多人在那里！",
		},
		MAXWELLPHONOGRAPH = "那就是音乐的来源。",
		BOOMERANG = "神奇的木头",
		PIGGUARD = "它们不仅是肉食者，而且也很讨厌",
		ABIGAIL = "可爱的幽灵",
		ADVENTURE_PORTAL = "我不确定我想来第二次。",
		AMULET = "这个护身符是强大的，我能感觉到",
		ANIMAL_TRACK = "也许奇怪的长毛兽丢下了它",
		ARMORGRASS = "大自然会保护兔男！",
		ARMORMARBLE = "华丽的盔甲",
		ARMORWOOD = "现在兔男安全了！",
		ARMOR_SANITY = "一个声音告诉我要穿上它",
		ASH =
		{
			GENERIC = "火留下这个。可怜的东西。",
			REMAINS_GLOMMERFLOWER = "哦，不~",
			REMAINS_EYE_BONE = "哦，不~",
			REMAINS_THINGIE = "这以前可能很酷",
		},
		AXE = "这东西可以砍树",
		BABYBEEFALO = "你爸爸去哪儿啦？",
		BACKPACK = "兔男旅行时使用！",
		BACONEGGS = "肉是凶手",
		BANDAGE = "这看起来很酷！",
		BASALT = "罕见的石头牢不可破",
		BATBAT = "会飞的蜘蛛帮助我",
		BEARDHAIR = "人类有时制造这个",
		BEARGER = "大恶霸喜欢吃我们！",
		BEARGERVEST = "大恶霸的外套",
		ICEPACK = "你再也不能吃我们了！",
		BEARGER_FUR = "你再也不能吃我们了！",
		BEDROLL_STRAW = "大自然的床",
		BEE =
		{
			GENERIC = "它可以做漂亮的东西",
			HELD = "你好，朋友！",
		},
		BEEBOX =
		{
			READY = "它充满了美味",
			FULLHONEY = "它充满了美味",
			GENERIC = "蜜蜂！",
			NOHONEY = "没有好吃的",
			SOMEHONEY = "也许应该等待更多好吃的",
			BURNT = "不会有美味了吗？",
		},
		BEEFALO =
		{
			FOLLOWER = "长毛兽来了",
			GENERIC = "你好，长毛兽！",
			NAKED = "抱歉，长毛兽...",
			SLEEPING = "我想在长毛兽边睡觉。",
			--Domesticated states:
			DOMESTICATED = "兔男的长毛兽是最好的！",
			ORNERY = "长毛兽是强硬的",
			RIDER = "长毛兽很友善",
			PUDGY = "兔男认为你看起来有趣！",
		},
		BEEFALOHAT = "很软的帽子！",
		BEEFALOWOOL = "兔男要用这个做床",
		BEEHAT = "蜜蜂不能碰到我了！",
		BEEHIVE = "蜜蜂的房子很吵！",
		BEEMINE = "蜜蜂的球！",
		BEEMINE_MAXWELL = "讨厌的人做的蜜蜂球",
		BERRIES = "灌木食物是美味的",
		BERRIES_COOKED = "灌木食物味道一样",
		BERRYBUSH =
		{
			BARREN = "你为什么不做食物呢？",
			WITHERED = "热死了我的灌木！",
			GENERIC = "灌木食物是美味的",
			PICKED = "我稍后再回来",
		},
		BIGFOOT = "你是神吗？",
		BIRDCAGE =
		{
			GENERIC = "这东西用来放鸟",
			OCCUPIED = "兔男的鸟！",
			SLEEPING = "你有梦想吗？",
			HUNGRY = "小鸟饿了！",
			STARVING = "小鸟太饿了！",
			DEAD = "小鸟?",
			SKELETON = "鸟哪去了？",
		},
		BIRDTRAP = "小鸟会是我的",
		BIRD_EGG = "鸟宝宝在这里",
		BIRD_EGG_COOKED = "糟糕！",
		BISHOP = "奇怪的东西是个混蛋！",
		BLOWDART_FIRE = "兔男会让东西燃烧！",
		BLOWDART_SLEEP = "我的瞌睡虫！",
		BLOWDART_PIPE = "冬季兔男有这个",
		BLUEAMULET = "冷冷的！",
		BLUEGEM = "漂亮闪亮宝石！",
		BLUEPRINT = "我喜欢学习！",
		BELL_BLUEPRINT = "这是能做什么？",
		BLUE_CAP = "臭的食物",
		BLUE_CAP_COOKED = "臭的食物仍然是臭的",
		BLUE_MUSHROOM =
		{
			GENERIC = "地上的食物",
			INGROUND = "夜晚",
			PICKED = "新的不久就来了",
		},
		BOARDS = "我们用这个做房子",
		BOAT = "那是什么？",
		BONESHARD = "这东西出现在死亡的地方",
		BONESTEW = "尝起来像死亡",
		BUGNET = "我现在抓虫子",
		BUSHHAT = "这头兔男看起来很滑稽",
		BUTTER = "这使得食物美味",
		BUTTERFLY =
		{
			GENERIC = "漂亮的虫！",
			HELD = "如此美丽！",
		},
		BUTTERFLYMUFFIN = "很好吃",
		BUTTERFLYWINGS = "依然漂亮！",
		BUZZARD = "丑陋的小鸟！",
		CACTUS =
		{
			GENERIC = "会痛的美味食物",
			PICKED = "现在它只是痛了",
		},
		CACTUS_MEAT_COOKED = "热的食物",
		CACTUS_MEAT = "会痛的美味食物",
		CACTUS_FLOWER = "会痛的美丽食物",

		COLDFIRE =
		{
			EMBERS = "很快就完了",
			GENERIC = "燃烧吧！",
			HIGH = "好冷啊！",
			LOW = "它不多啦",
			NORMAL = "冷的火",
			OUT = "再见，火",
		},
		CAMPFIRE =
		{
			EMBERS = "很快就完了",
			GENERIC = "燃烧吧！",
			HIGH = "好热啊！",
			LOW = "它不多啦",
			NORMAL = "热的火",
			OUT = "再见，火",
		},
		CANE = "兔男的动作快了！",
		CATCOON = "KITTY!",
		CATCOONDEN =
		{
			GENERIC = "是KITTY KITTY！",
			EMPTY = "KITTY?...",
		},
		CATCOONHAT = "KITTY的帽子！",
		COONTAIL = "抱歉KITTY...",
		CARROT = "兔人的食物",
		CARROT_COOKED = "熟兔人的食物",
		CARROT_PLANTED = "兔人在吗？",
		CARROT_SEEDS = "兔人会喜欢这些",
		WATERMELON_SEEDS = "种子",
		CAVE_FERN = "这是蕨类植物。",
		CHARCOAL = "这个又小又黑，闻起来像烧焦的木头。",
		CHESSJUNK1 = "一堆讨厌的东西",
		CHESSJUNK2 = "我能修理它吗？",
		CHESSJUNK3 = "他们看起来很伤心...",
		CHESTER = "你好，毛茸茸的东西！",
		CHESTER_EYEBONE =
		{
			GENERIC = "你好",
			WAITING = "夜晚",
		},
		COOKEDMANDRAKE = "你现在很好吃",
		COOKEDMEAT = "现在更好吃了",
		COOKEDMONSTERMEAT = "仍旧不太好吃",
		COOKEDSMALLMEAT = "小但是好吃",
		COOKPOT =
		{
			COOKING_LONG = "兔男应该做一些美味的东西",
			COOKING_SHORT = "差不多了",
			DONE = "好吃!",
			EMPTY = "它用来做好吃的",
			BURNT = "锅并不好吃",
		},
		CORN = "好吃的东西",
		CORN_COOKED = "好吃的东西",
		CORN_SEEDS = "种子",
		CROW =
		{
			GENERIC = "你好！",
			HELD = "哈哈！",
		},
		CUTGRASS = "柔软，有用",
		CUTREEDS = "沼泽的草",
		CUTSTONE = "光滑的石头",
		DEADLYFEAST = "兔男不知道这个",
		DEERCLOPS = "好恐怖！！！",
		DEERCLOPS_EYEBALL = "现在我觉得安全了",
		EYEBRELLAHAT =	"兔男不相信这个",
		DEPLETED_GRASS =
		{
			GENERIC = "草?",
		},
		DEVTOOL = "它闻起来有培根味！",
		DEVTOOL_NODEV = "我没有强大到足以用它",
		DIRTPILE = "奇怪的泥土，我要调查！",
		DIVININGROD =
		{
			COLD = "冷",
			GENERIC = "它发现了... 一些东西",
			HOT = "兔男太靠近了！",
			WARM = "兔男太靠近了！！",
			WARMER = "兔男太靠近了！！",
		},
		DIVININGRODBASE =
		{
			GENERIC = "兔男从来都不知道那是什么",
			READY = "也许树枝杆感觉有什么东西",
			UNLOCKED = "兔男干了什么？",
		},
		DIVININGRODSTART = "那棒子看起来很有用！",
		DRAGONFLY = "这意味着东西会燃烧！",
		ARMORDRAGONFLY = "兔男很热的证据",
		DRAGON_SCALES = "蠕虫皮",
		DRAGONFLYCHEST = "最好的箱子",
		LAVASPIT =
		{
			HOT = "那看起来太烫了",
			COOL = "我不会碰它",
		},

		LAVAE = "发烫宝宝",
		LAVAE_PET =
		{
			STARVING = "这听起来很饿！",
			HUNGRY = "你饿了吗？",
			CONTENT = "很自豪的家长",
			GENERIC = "你好，发烫宝宝！",
		},
		LAVAE_EGG =
		{
			GENERIC = "不知道是什么在里面",
		},
		LAVAE_EGG_CRACKED =
		{
			COLD = "需要温暖！",
			COMFY = "兔男必须是好家长",
		},
		LAVAE_TOOTH = "那颗牙齿从哪里来的？",

		DRAGONFRUIT = "我们在王聚会吃这些",
		DRAGONFRUIT_COOKED = "他们不会持续很久",
		DRAGONFRUIT_SEEDS = "种子",
		DRAGONPIE = "我敢打赌王会喜欢这个",
		DRUMSTICK = "凶手",
		DRUMSTICK_COOKED = "凶手！",
		DUG_BERRYBUSH = "它属于地面",
		DUG_GRASS = "它属于地面",
		DUG_MARSH_BUSH = "它属于地面",
		DUG_SAPLING = "它属于地面",
		DURIAN = "真恶心",
		DURIAN_COOKED = "更加恶心了",
		DURIAN_SEEDS = "恶心的种子",
		EARMUFFSHAT = "我的耳朵暖和了",
		EGGPLANT = "怪异的植物",
		EGGPLANT_COOKED = "现在更加怪异了",
		EGGPLANT_SEEDS = "种子",
		DECIDUOUSTREE =
		{
			BURNING = "漂亮的树太烫了！",
			BURNT = "漂亮的树不漂亮了吗？",
			CHOPPED = "它倒下了",
			POISON = "你讨厌我吃坚果吗？",
			GENERIC = "它是一棵漂亮的树",
		},
		ACORN = "树的蛋",
		ACORN_SAPLING = "你好，宝宝树",
		ACORN_COOKED = "树蛋很好吃",
		BIRCHNUTDRAKE = "坏树蛋！",
		EVERGREEN =
		{
			BURNING = "兔男应该做些什么？",
			BURNT = "兔男去帮助已经太晚了",
			CHOPPED = "木材",
			GENERIC = "你好，树！",
		},
		EVERGREEN_SPARSE =
		{
			BURNING = "兔男应该做些什么？",
			BURNT = "兔男去帮助已经太晚了",
			CHOPPED = "木材",
			GENERIC = "这棵树没有孩子。",
		},
		EYEPLANT = "你好，奇怪的花",
		FARMPLOT =
		{
			GENERIC = "现在兔男是农民",
			GROWING = "兔男是好农民",
			NEEDSFERTILIZER = "需要便便",
			BURNT = "现在它烧了",
		},
		FEATHERHAT = "我的鸟",
		FEATHER_CROW = "它是黑色的柔软的",
		FEATHER_ROBIN = "红色的柔软的",
		FEATHER_ROBIN_WINTER = "它是白色的柔软的",
		FEM_PUPPET = "你还好吗？",
		FIREFLIES =
		{
			GENERIC = "你好，发光的虫子！",
			HELD = "我喜欢他们燃烧！",
		},
		FIREHOUND = "这东西会放火",
		FIREPIT =
		{
			EMBERS = "它几乎没有了",
			GENERIC = "燃烧吧！",
			HIGH = "热但安全！",
			LOW = "它不多啦！",
			NORMAL = "冷的",
			OUT = "兔男可以以后再启动它",
		},
		COLDFIREPIT =
		{
			EMBERS = "它几乎没有了",
			GENERIC = "燃烧吧！",
			HIGH = "冷啊",
			LOW = "它不多啦！",
			NORMAL = "这还不错",
			OUT = "兔男可以以后再启动它",
		},
		FIRESTAFF = "兔男有毁灭一切的冲动！",
		FIRESUPPRESSOR =
		{
			ON = "防止火灾！",
			OFF = "是个英雄！",
			LOWFUEL = "需要更多的木头！",
		},

		FISH = "兔男不喜欢你，但喜欢你的美味！",
		FISHINGROD = "兔男不能用这个",
		FISHSTICKS = "美味的鱼是美味的",
		FISHTACOS = "美味的鱼是美味的",
		FISH_COOKED = "美味的鱼在我的肚子里",
		FLINT = "成为王的一步",
		FLOWER = "漂亮！",
		FLOWER_WITHERED = "你还好吗？",
		FLOWERHAT = "兔男很漂亮！",
		FLOWER_EVIL = "你是丑陋的",
		FOLIAGE = "绿色",
		FOOTBALLHAT = "兔男会记得你",
		FROG =
		{
			DEAD = "好",
			GENERIC = "反派",
			SLEEPING = "毛骨悚然",
		},
		FROGGLEBUNWICH = "混蛋三明治",
		FROGLEGS = "我应该把混蛋煮了",
		FROGLEGS_COOKED = "现在你的很美味",
		FRUITMEDLEY = "美味！",
		FURTUFT = "暴徒的毛",
		GEARS = "兔男想用这个做东西",
		GHOST = "幽灵！",
		GOLDENAXE = "适合王",
		GOLDENPICKAXE = "希望它不会轻易打破",
		GOLDENPITCHFORK = "如此闪亮！",
		GOLDENSHOVEL = "挖啊挖",
		GOLDNUGGET = "我们用胡萝卜交换。",
		GRASS =
		{
			BARREN = "为什么你没有长大！",
			WITHERED = "讨厌炎热！",
			BURNING = "太热了！",
			GENERIC = "草",
			PICKED = "谢谢草",
		},
		GREEN_CAP = "丑陋的食物",
		GREEN_CAP_COOKED = "还是丑陋？",
		GREEN_MUSHROOM =
		{
			GENERIC = "丑陋的食物",
			INGROUND = "或许再等等！",
			PICKED = "稍后回来",
		},
		GUNPOWDER = "兔男现在变得越来越危险了",
		HAMBAT = "兔男现在感到内疚...",
		HAMMER = "现在兔男可以摧毁一切！",
		HEALINGSALVE = "它造成疼痛但是会使兔男更好",
		HEATROCK =
		{
			FROZEN = "好冷！",
			COLD = "石头是冷的！",
			GENERIC = "特殊的石头可以变热！",
			WARM = "温暖的石头",
			HOT = "非常热！",
		},
		HOME = "你好?",
		HOMESIGN =
		{
			GENERIC = "它说'你在这儿'",
			UNWRITTEN = "空的",
			BURNT = "\"不要玩火柴\"",
		},
		ARROWSIGN_POST =
		{
			GENERIC = "它说'这条路'",
			UNWRITTEN = "空的。",
			BURNT = "\"不要玩火柴\"",
		},
		ARROWSIGN_PANEL =
		{
			GENERIC = "它说'这条路'",
			UNWRITTEN = "空的。",
			BURNT = "\"不要玩火柴\"",
		},
		HONEY = "！",
		HONEYCOMB = "好吃的糖浆做的！",
		HONEYHAM = "好吃的糖浆的家！",
		HONEYNUGGETS = "我的最爱",
		HORN = "兔男现在粗野吗？",
		HOUND = "猎犬！",
		HOUNDBONE = "或许该走远点...",
		HOUNDMOUND = "猎犬住在那儿",
		ICEBOX = "它保持食物新鲜",
		ICEHAT = "兔男被冻僵了！",
		ICEHOUND = "冰猎犬！",
		INSANITYROCK =
		{
			ACTIVE = "高大的石头！",
			INACTIVE = "弱小的石头！",
		},
		JAMMYPRESERVES = "美味的红色液体",
		KABOBS = "好吃",
		KILLERBEE =
		{
			GENERIC = "那只蜜蜂很讨厌！",
			HELD = "不要伤害我",
		},
		KNIGHT = "兔男想有一天可以制造你",
		KOALEFANT_SUMMER = "你好，奇怪的长毛兽",
		KOALEFANT_WINTER = "奇怪的长毛兽看起来温暖！",
		KRAMPUS = "走开！ 我的东西！",
		KRAMPUS_SACK = "我拿了你的东西！",
		LEIF = "树很生气！",
		LEIF_SPARSE = "树很生气！",
		LIGHTNING_ROD =
		{
			CHARGED = "兔男感觉像是在笑",
			GENERIC = "兔男从天空带来的光！",
		},
		LIGHTNINGGOAT =
		{
			GENERIC = "你好",
			CHARGED = "你为什么生气？",
		},
		LIGHTNINGGOATHORN = "小小的棍子",
		GOATMILK = "好吃！",
		LITTLE_WALRUS = "你爸爸很讨厌！",
		LIVINGLOG = "它不喜欢火！",
		LOG =
		{
			BURNING = "燃烧的木头",
			GENERIC = "兔男会用这个做东西",
		},
		LUREPLANT = "你好？",
		LUREPLANTBULB = "我也许可以使用你",
		MALE_PUPPET = "你还好吗？",

		MANDRAKE_ACTIVE = "我的时间！",
		MANDRAKE_PLANTED = "兔男没有看到很多",
		MANDRAKE = "奇怪的植物是神奇的",

		MANDRAKESOUP = "好吃",
		MANDRAKE_COOKED = "再见，奇怪的植物",
		MARBLE = "特殊的石头！",
		MARBLEPILLAR = "看起来不错！",
		MARBLETREE = "坚硬的树",
		MARSH_BUSH =
		{
			BURNING = "再见！",
			GENERIC = "也许兔男没有触摸过",
			PICKED = "哼哼！",
		},
		BURNT_MARSH_BUSH = "它死了",
		MARSH_PLANT = "一个植物",
		MARSH_TREE =
		{
			BURNING = "烧吧！",
			BURNT = "再见！",
			CHOPPED = "现在它只是个树！",
			GENERIC = "恐怖的树！",
		},
		MAXWELL = "讨厌的人",
		MAXWELLHEAD = "可怕又讨厌的人",
		MAXWELLLIGHT = "你有魔力吗？",
		MAXWELLLOCK = "看起来像个洞",
		MAXWELLTHRONE = "兔男并不喜欢它",
		MEAT = "肉是原凶！",
		MEATBALLS = "糟糕啊！",
		MEATRACK =
		{
			DONE = "肉干！",
			DRYING = "肉在变成肉干",
			DRYINGINRAIN = "雨没有帮助！",
			GENERIC = "肉到这里来",
			BURNT = "现在是垃圾了",
		},
		MEAT_DRIED = "肉干",
		MERM = "兔男讨厌他！",
		MERMHEAD =
		{
			GENERIC = "兔男的战利品",
			BURNT = "兔男可以做新的",
		},
		MERMHOUSE =
		{
			GENERIC = "我们把它做得更好",
			BURNT = "没有太大的不同",
		},
		MINERHAT = "燃烧吧，脑袋！",
		MONKEY = "你好，猴子！.",
		MONKEYBARREL = "你好？",
		MONSTERLASAGNA = "恶心",
		FLOWERSALAD = "恶心但是漂亮",
		ICECREAM = "我喜欢这个",
		WATERMELONICLE = "讨厌",
		TRAILMIX = "美味的糖果",
		HOTCHILI = "兔男的嘴巴很热",
		GUACAMOLE = "讨厌",
		MONSTERMEAT = "兔男讨厌肉",
		MONSTERMEAT_DRIED = "这肉一直不好吃",
		MOOSE = "春天的鸟？",
		MOOSEEGG = "好大的蛋！",
		MOSSLING = "兔男打赌你很软，兔男会拥抱你！",
		FEATHERFAN = "把我的温度降下来",
		MINIFAN = "不知怎的微风从背后吹来。",
		GOOSE_FEATHER = "很软！",
		STAFF_TORNADO = "圆圆的自旋",
		MOSQUITO =
		{
			GENERIC = "吸血鬼",
			HELD = "他喜欢兔男血！",
		},
		MOSQUITOSACK = "闻起来像兔男血",
		MOUND =
		{
			DUG = "再见了，小偷洞！",
			GENERIC = "好奇藏了什么？",
		},
		NIGHTLIGHT = "幽灵般的火！",
		NIGHTMAREFUEL = "噩梦！",
		NIGHTSWORD = "亦真亦幻",
		NITRE = "奇怪的石头是什么",
		ONEMANBAND = "制作音乐！！！",
		PANDORASCHEST = "旧箱子但是有好吃的东西！",
		PANFLUTE = "音乐会使世界和平",
		PAPYRUS = "希望有一天会写点什么",
		PENGUIN = "奇怪的小鸟很吵",
		PERD = "坏鸟！我的食物！",
		PEROGIES = "好吃",
		PETALS = "美丽的花",
		PETALS_EVIL = "讨厌这些",
		PHLEGM = "无用的液体！",
		PICKAXE = "兔男用大脚代替！",
		PIGGYBACK = "猪总算有点用了！",
		PIGHEAD =
		{
			GENERIC = "这是谁做的？",
			BURNT = "安息安息。",
		},
		PIGHOUSE =
		{
			FULL = "你好，朋友！",
			GENERIC = "朋友的房子！",
			LIGHTSOUT = "兔男从来就不那么喜欢",
			BURNT = "小兔男感觉不好",
		},
		PIGKING = "最丑的王！",
		PIGMAN =
		{
			DEAD = "噢",
			FOLLOWER = "也许猪不是太坏",
			GENERIC = "他们恨我们，我们恨他们",
			GUARD = "他们是最坏的",
			WEREPIG = "他们像兔男一样被诅咒！？",
		},
		PIGSKIN = "这个谋杀案可以很好地加以利用",
		PIGTENT = "猪舍！臭！",
		PIGTORCH = "为什么那么重要？",
		PINECONE = "树的蛋！",
		PINECONE_SAPLING = "宝宝树将变成大树！",
		LUMPY_SAPLING = "宝宝树你怎么会在这儿的？",
		PITCHFORK = "基地的设计师！",
		PLANTMEAT = "恶心的肉",
		PLANTMEAT_COOKED = "额，吃得太糟了",
		PLANT_NORMAL =
		{
			GENERIC = "绿叶！",
			GROWING = "长的真慢！",
			READY = "嗯~. 准备收获！",
			WITHERED = "热死的。",
		},
		POMEGRANATE = "好的食物！",
		POMEGRANATE_COOKED = "好吃！",
		POMEGRANATE_SEEDS = "种子",
		POND = "鱼和青蛙在那里！",
		POOP = "闻起来恶心！",
		FERTILIZER = "便便桶",
		PUMPKIN = "兔男会喜欢这个",
		PUMPKINCOOKIE = "饼干！",
		PUMPKIN_COOKED = "南瓜，好吃！",
		PUMPKIN_LANTERN = "幽灵般的光！",
		PUMPKIN_SEEDS = "种子",
		PURPLEAMULET = "兔男讨厌声音。",
		PURPLEGEM = "闪亮的邪恶石头！",
		RABBIT =
		{
			GENERIC = "小的兔人",
			HELD = "兔男很软！兔男喜欢小兔男！",
		},
		RABBITHOLE =
		{
			GENERIC = "希望兔人呆在那里",
			SPRING = "今天至少没有兔人！",
		},
		RAINOMETER =
		{
			GENERIC = "兔男很无聊",
			BURNT = "兔男不在乎",
		},
		RAINCOAT = "防雨",
		RAINHAT = "干燥是幸福的",
		RATATOUILLE = "一种美味",
		RAZOR = "兔男希望有头发",
		REDGEM = "闪亮的红色石头！",
		RED_CAP = "丑陋的食物",
		RED_CAP_COOKED = "依旧恶心",
		RED_MUSHROOM =
		{
			GENERIC = "地上的食物。",
			INGROUND = "夜晚",
			PICKED = "回头见！",
		},
		REEDS =
		{
			BURNING = "这可不好！",
			GENERIC = "沼泽的草！",
			PICKED = "没有更多的了！",
		},
		RELIC =
		{
			GENERIC = "古代的家居用品",
			BROKEN = "没什么可以在这里干的了。",
		},
		RUINS_RUBBLE = "可以修好它！",
		RUBBLE = "以前是某个东西",
		RESEARCHLAB =
		{
			GENERIC = "兔男现在聪明的兔男",
			BURNT = "不！不能没有它！",
		},
		RESEARCHLAB2 =
		{
			GENERIC = "兔男将成为天才！",
			BURNT = "不！不是天才了！",
		},
		RESEARCHLAB3 =
		{
			GENERIC = "兔男现在变成邪恶的魔法师",
			BURNT = "或许很好",
		},
		RESEARCHLAB4 =
		{
			GENERIC = "兔男是很好的魔法师",
			BURNT = "兔男很伤心",
		},
		RESURRECTIONSTATUE =
		{
			GENERIC = "看起来像人",
			BURNT = "没多大用处了。",
		},
		RESURRECTIONSTONE = "有用的石头",
		ROBIN =
		{
			GENERIC = "兔男喜欢红色的鸟",
			HELD = "你好，红鸟。",
		},
		ROBIN_WINTER =
		{
			GENERIC = "你好，漂亮的小鸟！",
			HELD = "漂亮的小鸟很软！",
		},
		ROBOT_PUPPET = "你还好吗？",
		ROCK_LIGHT =
		{
			GENERIC = "一个陈旧的熔岩坑。",
			OUT = "看起来很脆弱。",
			LOW = "岩浆正在冷却。",
			NORMAL = "好看又舒服",
		},
		ROCK = "不知道兔男能否打碎这个",
		ROCK_ICE =
		{
			GENERIC = "冰冷的石头！",
			MELTED = "冰冷的石头在哪里？",
		},
		ROCK_ICE_MELTED = "冰冷的石头在哪里？",
		ICE = "你好，冰",
		ROCKS = "用来制造东西",
		ROOK = "看起来很讨厌",
		ROPE = "兔男会做很多东西",
		ROTTENEGG = "臭",
		SADDLE_BASIC = "兔男不能使用这个",
		SADDLE_WAR = "这是什么？",
		SANITYROCK =
		{
			ACTIVE = "它太大了",
			INACTIVE = "它太小了",
		},
		SAPLING =
		{
			BURNING = "燃烧吧",
			WITHERED = "太阳的树枝",
			GENERIC = "你好，树枝。",
			PICKED = "树枝不见了",
		},
		SEEDS = "它会长成什么",
		SEEDS_COOKED = "美味的种子",
		SEWING_KIT = "兔男用这个修补衣服",
		SHOVEL = "兔男要挖",
		SILK = "蜘蛛的粪便",
		SKELETON = "死人",
		SCORCHED_SKELETON = "熟的人很臭",
		SKULLCHEST = "我不知道我是否想打开它。",
		SMALLBIRD =
		{
			GENERIC = "这是一个相当小的鸟。",
			HUNGRY = "它看起来很饿。",
			STARVING = "它一定很饿。",
		},
		SMALLMEAT = "死的动物一小块",
		SMALLMEAT_DRIED = "肉干。",
		SPAT = "让人恶心",
		SPEAR = "兔男不需要这个",
		SPIDER =
		{
			DEAD = "兔男赢了！",
			GENERIC = "蜘蛛是很丑的毛茸茸的混蛋！",
			SLEEPING = "兔男睡得更好！",
		},
		SPIDERDEN = "蜘蛛巢让人恶心",
		SPIDEREGGSACK = "兔男已经控制！",
		SPIDERGLAND = "蜘蛛内脏",
		SPIDERHAT = "蜘蛛的战利品",
		SPIDERQUEEN = "王比你更好",
		SPIDER_WARRIOR =
		{
			DEAD = "兔男赢了！",
			GENERIC = "蜘蛛守卫！",
			SLEEPING = "兔男会杀了你！",
		},
		SPOILED_FOOD = "现在食物不好了",
		STATUEHARP = "去哪儿呢？",
		STATUEMAXWELL = "卑鄙的人假装是上帝",
		STEELWOOL = "这个真让人恶心",
		STINGER = "蜜蜂带刺",
		STRAWHAT = "兔男喜欢帽子",
		STUFFEDEGGPLANT = "不太好吃！",
		SUNKBOAT = "浮木？",
		SWEATERVEST = "兔男喜欢这个！",
		REFLECTIVEVEST = "现在兔男发光了",
		HAWAIIANSHIRT = "休假的兔男",
		TAFFY = "好吃",
		TALLBIRD = "你好眼睛鸟！",
		TALLBIRDEGG = "你好眼睛鸟蛋！",
		TALLBIRDEGG_COOKED = "你好眼睛鸟蛋食品",
		TALLBIRDEGG_CRACKED =
		{
			COLD = "你冷吗？",
			GENERIC = "眼睛鸟宝宝？",
			HOT = "也许太热了",
			LONG = "兔男是好家长",
			SHORT = "兔男很兴奋！",
		},
		TALLBIRDNEST =
		{
			GENERIC = "你好眼睛鸟蛋！",
			PICKED = "今天没有眼睛鸟蛋！",
		},
		TEENBIRD =
		{
			GENERIC = "兔男可能艰难的时间",
			HUNGRY = "兔男会喂你！",
			STARVING = "并不开心",
		},
		TELEBASE =
		{
			VALID = "准备",
			GEMS = "需要邪恶的石头",
		},
		GEMSOCKET =
		{
			VALID = "邪恶的石头制造邪恶的声音",
			GEMS = "它需要邪恶的石头！",
		},
		TELEPORTATO_BASE =
		{
			ACTIVE = "兔男将进入新世界！",
			GENERIC = "兔男很奇怪",
			LOCKED = "需要一些东西",
			PARTIAL = "很快",
		},
		TELEPORTATO_BOX = "盒子可能是有用的",
		TELEPORTATO_CRANK = "兔男可以用这个",
		TELEPORTATO_POTATO = "兔男从这感觉到一些东西",
		TELEPORTATO_RING = "有这个的兔男很有想法",
		TELESTAFF = "兔男很感兴趣",
		TENT =
		{
			GENERIC = "兔男喜欢的房子",
			BURNT = "今晚不能睡觉",
		},
		SIESTAHUT =
		{
			GENERIC = "兔男躲避阳光",
			BURNT = "现在它不能拯救兔男！",
		},
		TENTACLE = "那东西真的很不友好",
		TENTACLESPIKE = "兔男不需要这个！",
		TENTACLESPOTS = "兔男认为这让人恶心",
		TENTACLE_PILLAR = "巨大的地下",
		TENTACLE_PILLAR_HOLE = "兔男很好奇...",
		TENTACLE_PILLAR_ARM = "孩子的东西",
		TENTACLE_GARDEN = "粘糊糊的",
		TOPHAT = "很好的帽子",
		TORCH = "光在动",
		TRANSISTOR = "它嗡嗡叫",
		TRAP = "我捕捉食物",
		TRAP_TEETH = "兔男是聪明的",
		TRAP_TEETH_MAXWELL = "不友好的人讨厌兔男！",
		TREASURECHEST =
		{
			GENERIC = "这是箱子",
			BURNT = "再见，箱子",
		},
		TREASURECHEST_TRAP = "兔男被愚弄了吗？",
		TREECLUMP = "这好像有人试图阻止我去某个地方。",

		TRINKET_1 = "臭臭的家伙会喜欢这个",
		TRINKET_2 = "臭臭的家伙会喜欢这个",
		TRINKET_3 = "臭臭的家伙会喜欢这个",
		TRINKET_4 = "臭臭的家伙会喜欢这个",
		TRINKET_5 = "臭臭的家伙会喜欢这个",
		TRINKET_6 = "臭臭的家伙会喜欢这个",
		TRINKET_7 = "臭臭的家伙会喜欢这个",
		TRINKET_8 = "臭臭的家伙会喜欢这个",
		TRINKET_9 = "臭臭的家伙会喜欢这个",
		TRINKET_10 = "臭臭的家伙会喜欢这个",
		TRINKET_11 = "臭臭的家伙会喜欢这个",
		TRINKET_12 = "臭臭的家伙会喜欢这个",
		TRINKET_13 = "臭臭的家伙会喜欢这个",
		TRINKET_14 = "臭臭的家伙会喜欢这个",
		TRINKET_15 = "臭臭的家伙会喜欢这个",
		TRINKET_16 = "臭臭的家伙会喜欢这个",
		TRINKET_17 = "臭臭的家伙会喜欢这个",
		TRINKET_18 = "臭臭的家伙会喜欢这个",
		TRINKET_19 = "臭臭的家伙会喜欢这个",
		TRINKET_20 = "臭臭的家伙会喜欢这个",
		TRINKET_21 = "臭臭的家伙会喜欢这个.",
		TRINKET_22 = "臭臭的家伙会喜欢这个.",
		TRINKET_23 = "臭臭的家伙会喜欢这个.",
		TRINKET_24 = "臭臭的家伙会喜欢这个",
		TRINKET_25 = "臭臭的家伙会喜欢这个.",
		TRINKET_26 = "臭臭的家伙会喜欢这个.",
		TRINKET_27 = "臭臭的家伙会喜欢这个.",

		TRUNKVEST_SUMMER = "这让兔男温暖。",
		TRUNKVEST_WINTER = "这让兔男温暖。",
		TRUNK_COOKED = "谁会吃这个？",
		TRUNK_SUMMER = "野兽的鼻子。",
		TRUNK_WINTER = "模糊的野兽的鼻子",
		TUMBLEWEED = "草的礼物！",
		TURF_CARPETFLOOR = "兔男可以睡在这。",
		TURF_CHECKERFLOOR = "记得不友好的人的兔男",
		TURF_DIRT = "脏。",
		TURF_FOREST = "草。",
		TURF_GRASS = "草。",
		TURF_MARSH = "兔男要在这滚来滚去。",
		TURF_ROAD = "兔男越来越花哨。",
		TURF_ROCKY = "岩石的。",
		TURF_SAVANNA = "草。",
		TURF_WOODFLOOR = "木头的",
		TURKEYDINNER = "*流口水*",
		TWIGS = "一个不那么好吃的树枝",
		UMBRELLA = "雨啊雨快走开",
		GRASS_UMBRELLA = "雨啊雨快走开",
		UNIMPLEMENTED = "兔男不知道这是什么",
		WAFFLES = "兔男喜欢华夫饼",
		WALL_HAY =
		{
			GENERIC = "他们不会持有太多.",
			BURNT = "无用的墙！",
		},
		WALL_HAY_ITEM = "脆弱的墙。",
		WALL_STONE = "优于木头",
		WALL_STONE_ITEM = "优于木头",
		WALL_RUINS = "老旧但闪亮的墙壁。",
		WALL_RUINS_ITEM = "闪亮的硬墙",
		WALL_WOOD =
		{
			GENERIC = "木制",
			BURNT = "哎呀~",
		},
		WALL_WOOD_ITEM = "保卫者使用这些墙壁",
		WALL_MOONROCK = "兔男利用月亮！",
		WALL_MOONROCK_ITEM = "太空的兔男",
		WALRUS = "冬季兔男很坏",
		WALRUSHAT = "冬季兔男是兔男吗？",
		WALRUS_CAMP =
		{
			EMPTY = "冬兔男在这里",
			GENERIC = "可能看起来很暖和，但还是兔男房比较好！",
		},
		WALRUS_TUSK = "兔男用这个会做好东西",
		WARDROBE =
		{
			GENERIC = "现在兔男看起来很酷！",
			BURNING = "不！",
			BURNT = "但是兔男喜欢衣柜...",
		},
		WARG = "兔男应该离开...",
		WASPHIVE = "蜜蜂的巢穴",
		WATERMELON = "美味的粘粘的食物",
		WATERMELON_COOKED = "美味果汁食品",
		WATERMELONHAT = "希望不要让兔男头粘粘的...",
		WETGOOP = "兔男做的恶心食物",
		WHIP = "兔男是长毛兽驯兽师",
		WINTERHAT = "兔男喜欢温暖而蓬松的帽子",
		WINTEROMETER =
		{
			GENERIC = "兔男不需要这个，但是...",
			BURNT = "兔男用这个很好",
		},
		WORMHOLE =
		{
			GENERIC = "你好，洞！",
			OPEN = "兔男很好奇它通向哪里?",
		},
		WORMHOLE_LIMITED = "你还好吗？洞。",
		ACCOMPLISHMENT_SHRINE = "我想使用它，我想让世界知道我做了什么。",
		LIVINGTREE = "你好，木头！",
		ICESTAFF = "兔男是很酷的",
		REVIVER = "兔男将复活人类来帮助兔男",
		LIFEINJECTOR = "兔男喜欢药物",
		SKELETON_PLAYER =
		{
			MALE = "%s好笨。",
			FEMALE = "%s好笨。",
			ROBOT = "%s好笨。",
			DEFAULT = "%s好笨。",
		},
		HUMANMEAT = "人类的肉看起来有点笨",
		HUMANMEAT_COOKED = "还是恶心",
		HUMANMEAT_DRIED = "额额~",
		MOONROCKNUGGET = "空间石头",
	},
	DESCRIBE_GENERIC = "玩意儿。",
	DESCRIBE_TOODARK = "太黑了！",
	DESCRIBE_SMOLDERING = "越来越热！",
	EAT_FOOD =
	{
		TALLBIRDEGG_CRACKED = "有眼睛的鸟好可怜啊！",
	},
}
