-- The character select screen lines
STRINGS.CHARACTER_TITLES.haruz = "胆小的狗"
STRINGS.CHARACTER_NAMES.haruz = "Haruz"
STRINGS.CHARACTER_DESCRIPTIONS.haruz = "*吃肉升级 (最大30级)\n*Haruz 灯笼（可以用木头，草补充燃料）。\n*可以恢复健康\n"
STRINGS.CHARACTER_QUOTES.haruz = "\"我唯一不害怕的..只有 Sollyz\""

-- Custom speech strings
--STRINGS.CHARACTERS.haruz = require(chinesefolder.."/Haruz/speech_haruz")
STRINGS.CHARACTERS.haruz = nil

-- The character's name as appears in-game
STRINGS.NAMES.haruz = "haruz"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.haruz =
{
  GENERIC = "这是 Haruz",
  ATTACKER = "Haruz 看起来很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "Haruz, 鬼魂朋友！",
  GHOST = "Haruz 可以使用一颗心。",
}

STRINGS.NAMES.BRASS_LANTERN = "Haruz的灯笼"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.BRASS_LANTERN = "Sollyz送的礼物。"
