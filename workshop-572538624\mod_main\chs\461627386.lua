STRINGS.NAMES.NIGHTMAREROCK = "灵魂宝石"
STRINGS.NAMES.ROSEBOW = "鹿目圆的玫瑰"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.NIGHTMAREROCK = {
  VOID = "看着它让我难受。",
  WEAK = "感觉很冷。",
  NORM = "它是静止的",
  GRAY = "它失去了一些能量。",
  FULL = "它在脉动。"
}

STRINGS.CHARACTERS.GENERIC.DESCRIBE.ROSEBOW = "由一个粉红色的小女孩使用的武器。"

-- The character select screen lines
STRINGS.CHARACTER_TITLES.madoka = "魔法少女小圆"
STRINGS.CHARACTER_NAMES.madoka = "鹿目圆"
STRINGS.CHARACTER_DESCRIPTIONS.madoka = "*一个乐观的女孩。\n*一个魔法少女。\n*受她的宝石限制。"
STRINGS.CHARACTER_QUOTES.madoka = "\"如果有人告诉我希望是错误的，我会告诉他们，他们一直是错的。\""

-- Custom speech strings
--STRINGS.CHARACTERS.MADOKA = require(chinesefolder.."/Madoka/speech_madoka")
STRINGS.CHARACTERS.MADOKA = nil

-- The character's name as appears in-game
STRINGS.NAMES.MADOKA = "鹿目圆"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.MADOKA =
{
  GENERIC = "这是鹿目圆",
  ATTACKER = "鹿目圆看起来很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "鹿目圆, 鬼魂朋友！",
  GHOST = "鹿目圆可以使用一颗心。",
}

