_G.ChinesePlus.RenameRecipe("TENTONHAMMER", "必须重拳出击!")
_G.ChinesePlus.RenameRecipe("SMALLROCKS", "他们是岩石，但有一口是大小！!")

STRINGS.NAMES.TENTONHAMMER = "十吨之锤"
STRINGS.NAMES.SMALLROCKS = "小石头"
STRINGS.NAMES.IMPACTDIAL = "冲击贝"
STRINGS.NAMES.KUROKABUTO = "黑兜"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.TENTONHAMMER = "怎么能有人提起这样的事呢？！"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SMALLROCKS = "没有多少关于小石头的说法…"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.IMPACTDIAL = "如果使用了，会是毁灭性的破坏力。"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.KUROKABUTO = "小玩意儿，需要极大的瞄准技巧。"

_G.ChinesePlus.RenameTab("Usopp", "乌索普")

-- The character select screen lines
STRINGS.CHARACTER_TITLES.usopp = "狙击之王"
STRINGS.CHARACTER_NAMES.usopp = "乌索普"
STRINGS.CHARACTER_DESCRIPTIONS.usopp = "*鹰的眼睛\n*从远处射击\n*玻璃大炮"
STRINGS.CHARACTER_QUOTES.usopp = "\"总有一天会像一个男人站起来去战斗\""

-- Custom speech strings
--STRINGS.CHARACTERS.USOPP = require(chinesefolder.."/Usopp/speech_usopp")
STRINGS.CHARACTERS.USOPP = nil

-- The character's name as appears in-game
STRINGS.NAMES.USOPP = "乌索普"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.USOPP =
{
  GENERIC = "这是乌索普！",
  ATTACKER = "乌索普看起来很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "乌索普, 鬼魂朋友！",
  GHOST = "乌索普可以使用一颗心。",
}

