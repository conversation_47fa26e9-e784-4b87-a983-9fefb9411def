-- The character select screen lines
STRINGS.CHARACTER_TITLES.windkind = "空想家"
STRINGS.CHARACTER_NAMES.windkind = "風之女"
STRINGS.CHARACTER_DESCRIPTIONS.windkind = "*沒有很多傷害\n*有很高的精神\n*不容易餓"
STRINGS.CHARACTER_QUOTES.windkind = "\"去風吹的地方\""

-- Custom speech strings
--STRINGS.CHARACTERS.WINDKIND = require(chinesefolder.."/Windkind/speech_windkind")
STRINGS.CHARACTERS.WINDKIND = nil

-- The character's name as appears in-game
STRINGS.NAMES.WINDKIND = "風之女"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.WINDKIND =
{
  GENERIC = "這是風之女",
  ATTACKER = "風之女看起來很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "風之女, 鬼魂朋友！",
  GHOST = "風之女可以使用一顆心。",
}

