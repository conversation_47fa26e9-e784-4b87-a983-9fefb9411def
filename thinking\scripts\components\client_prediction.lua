-- 客户端预测辅助组件
-- 用于提供即时的视觉和音效反馈，减少网络延迟感

local _G = GLOBAL
local Class = _G.Class
local TheWorld = _G.TheWorld

local ClientPrediction = Class(function(self, inst)
    self.inst = inst
    self.predicted_effects = {}
    self.cleanup_tasks = {}
end)

-- 预测季节切换效果
function ClientPrediction:PredictSeasonChange(season)
    if TheWorld.ismastersim then return end
    
    -- 错误处理
    if not self.inst or not self.inst:IsValid() then
        print("[SeasonWorkshop] Warning: Invalid instance for season prediction")
        return
    end
    
    if not season then
        print("[SeasonWorkshop] Warning: No season provided for prediction")
        return
    end
    
    -- 立即播放音效
    if self.inst.SoundEmitter then
        self.inst.SoundEmitter:PlaySound("dontstarve/common/staff_coldlight", nil, 0.5)
    end
    
    -- 立即更新角色颜色
    if self.inst.AnimState then
        local colors = {
            spring = {0.05, 0.1, 0.05, 0},
            summer = {0.1, 0.05, 0, 0},
            autumn = {0.08, 0.03, 0, 0},
            winter = {0, 0.05, 0.1, 0}
        }
        
        local color = colors[season]
        if color then
            self.inst.AnimState:SetAddColour(color[1], color[2], color[3], color[4])
        end
    end
    
    -- 播放特效
    local fx = SpawnPrefab("staff_castinglight")
    if fx then
        fx.Transform:SetPosition(self.inst.Transform:GetWorldPosition())
        
        -- 季节颜色
        local season_colors = {
            spring = {0.5, 1.0, 0.5, 0.8},
            summer = {1.0, 0.6, 0.2, 0.8},
            autumn = {0.8, 0.5, 0.2, 0.8},
            winter = {0.6, 0.8, 1.0, 0.8}
        }
        
        local fx_color = season_colors[season]
        if fx_color and fx.AnimState then
            fx.AnimState:SetMultColour(fx_color[1], fx_color[2], fx_color[3], fx_color[4])
        end
        
        fx:DoTaskInTime(1.0, function()
            if fx and fx:IsValid() then
                fx:Remove()
            end
        end)
    end
    
    print(string.format("[SeasonWorkshop] Predicted season change to %s", season))
end

-- 预测武器攻击效果
function ClientPrediction:PredictWeaponHit(target, season)
    if TheWorld.ismastersim then return end
    
    -- 错误处理
    if not self.inst or not self.inst:IsValid() or not target or not target:IsValid() then
        return
    end
    
    -- 立即播放轻量级音效
    if self.inst.SoundEmitter then
        self.inst.SoundEmitter:PlaySound("dontstarve/common/staff_spell", nil, 0.3)
    end
    
    -- 目标闪烁效果
    if target.AnimState then
        local colors = {
            spring = {0.5, 1.0, 0.5, 0.3},
            summer = {1.0, 0.6, 0.2, 0.3},
            autumn = {0.8, 0.5, 0.2, 0.3},
            winter = {0.6, 0.8, 1.0, 0.3}
        }
        
        local color = colors[season] or colors["autumn"]
        target.AnimState:SetAddColour(color[1], color[2], color[3], color[4])
        
        -- 清理任务
        local cleanup_task = target:DoTaskInTime(0.2, function()
            if target and target:IsValid() and target.AnimState then
                target.AnimState:SetAddColour(0, 0, 0, 0)
            end
        end)
        
        if cleanup_task then
            table.insert(self.cleanup_tasks, cleanup_task)
        end
    end
end

-- 预测爆发攻击效果
function ClientPrediction:PredictBurstAttack(pos, season)
    if TheWorld.ismastersim then return end
    
    -- 错误处理
    if not pos or not self.inst or not self.inst:IsValid() then
        return
    end
    
    -- 立即播放音效
    if self.inst.SoundEmitter then
        self.inst.SoundEmitter:PlaySound("dontstarve/common/staff_spell", nil, 0.6)
    end
    
    -- 播放特效
    local fx = SpawnPrefab("staff_castinglight")
    if fx then
        fx.Transform:SetPosition(pos.x or pos[1], 0, pos.z or pos[3])
        
        -- 季节颜色
        local colors = {
            spring = {0.5, 1.0, 0.5, 1.0},
            summer = {1.0, 0.6, 0.2, 1.0},
            autumn = {0.8, 0.5, 0.2, 1.0},
            winter = {0.6, 0.8, 1.0, 1.0}
        }
        
        local color = colors[season] or colors["autumn"]
        if fx.AnimState then
            fx.AnimState:SetMultColour(color[1], color[2], color[3], color[4])
        end
        
        fx:DoTaskInTime(0.5, function()
            if fx and fx:IsValid() then
                fx:Remove()
            end
        end)
    end
end

-- 预测护盾破除效果
function ClientPrediction:PredictShieldBreak()
    if TheWorld.ismastersim then return end
    
    -- 错误处理
    if not self.inst or not self.inst:IsValid() then
        return
    end
    
    -- 立即播放音效
    if self.inst.SoundEmitter then
        self.inst.SoundEmitter:PlaySound("dontstarve/common/ghost_spawn", nil, 0.8)
    end
    
    -- 立即更新视觉效果（移除护盾颜色）
    if self.inst.AnimState then
        self.inst.AnimState:SetAddColour(0, 0, 0, 0)
    end
    
    -- 播放破盾特效
    local fx = SpawnPrefab("staff_castinglight")
    if fx then
        fx.Transform:SetPosition(self.inst.Transform:GetWorldPosition())
        if fx.AnimState then
            fx.AnimState:SetMultColour(1.0, 1.0, 1.0, 1.0) -- 白色闪光
        end
        
        fx:DoTaskInTime(0.3, function()
            if fx and fx:IsValid() then
                fx:Remove()
            end
        end)
    end
    
    print("[SeasonWorkshop] Predicted shield break")
end

-- 清理所有预测效果
function ClientPrediction:ClearPredictions()
    -- 清理所有清理任务
    for _, task in ipairs(self.cleanup_tasks) do
        if task and task.Cancel then
            task:Cancel()
        end
    end
    self.cleanup_tasks = {}
    
    -- 清理预测效果记录
    self.predicted_effects = {}
    
    print("[SeasonWorkshop] Client predictions cleared")
end

-- 组件移除时的清理
function ClientPrediction:OnRemoveFromEntity()
    self:ClearPredictions()
end

return ClientPrediction
