-- The character select screen lines
STRINGS.CHARACTER_TITLES.haruz = "膽小的狗"
STRINGS.CHARACTER_NAMES.haruz = "Haruz"
STRINGS.CHARACTER_DESCRIPTIONS.haruz = "*吃肉升級 (最大30級)\n*Haruz 燈籠（可以用木頭，草補充燃料）。\n*可以恢復健康\n"
STRINGS.CHARACTER_QUOTES.haruz = "\"我唯一不害怕的..只有 Sollyz\""

-- Custom speech strings
--STRINGS.CHARACTERS.haruz = require(chinesefolder.."/Haruz/speech_haruz")
STRINGS.CHARACTERS.haruz = nil

-- The character's name as appears in-game
STRINGS.NAMES.haruz = "haruz"

-- The default responses of examining the character
STRINGS.CHARACTERS.GENERIC.DESCRIBE.haruz =
{
  GENERIC = "這是 Haruz",
  ATTACKER = "Haruz 看起來很狡猾...",
  MURDERER = "凶手！",
  REVIVER = "Haruz, 鬼魂朋友！",
  GHOST = "Haruz 可以使用一顆心。",
}

STRINGS.NAMES.BRASS_LANTERN = "Haruz的燈籠"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.BRASS_LANTERN = "Sollyz送的禮物。"
