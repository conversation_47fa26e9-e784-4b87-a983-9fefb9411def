STRINGS.NAMES.PICKLE = "腌制"

-- Pickle Barrel

STRINGS.NAMES.PICKLE_BARREL = "腌菜桶"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.PICKLE_BARREL_PICKLING = {
  "腌制这个需要一段时间！",
  "腌制这个肯定需要一段时间。",
  "我的食物完成腌制之前我很迷茫。",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.PICKLE_BARREL_GENERIC = {
  "腌制食物会用很长时间，对吧？",
  "嗯，优质的咸味.",
  "不要与匹克球混淆.",
  "供应三明治，对吧？",
}

STRINGS.CHARACTERS.GENERIC.DESCRIBE.PICKLE_BARREL = STRINGS.CHARACTERS.GENERIC.DESCRIBE.PICKLE_BARREL_GENERIC

_G.ChinesePlus.RenameRecipe("PICKLE_BARREL", "腌制你的食物，让它较长时间保持不坏！")

-- Pickle Sword

STRINGS.NAMES.PICKLE_SWORD = "黄瓜大保剑"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.PICKLE_SWORD = {
  "用你的黄瓜重击敌人的菊花！",
  "我的黄瓜比你的大！",
  "腌出来的惊喜！",
}

_G.ChinesePlus.RenameRecipe("PICKLE_SWORD", "用你的黄瓜重击敌人的菊花！")

-- Beet

STRINGS.NAMES.BEET = "甜菜"
STRINGS.NAMES.BEET_COOKED = "烤甜菜"
STRINGS.NAMES.BEET_SEEDS = "甜菜种子"
STRINGS.NAMES.BEET_PICKLED = "腌甜菜"
STRINGS.NAMES.BEET_PLANTED = "甜菜"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.BEET = {
  "一个事实：熊吃甜菜。熊吃甜菜，太空堡垒卡拉狄加。",
  "没有人喜欢甜菜？也许我应该用生长的糖果代替。",
  "让我们花园派对。\n莴苣萝卜甜菜。",
  "我可以腌制这个！",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.BEET_COOKED = {
  "烤甜菜有一个甜蜜的泥土味",
  "比未经焙烤的甜菜甜",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.BEET_SEEDS = {
  "这是一个种子。",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.BEET_PICKLED = {
  "我听到人们很喜欢腌甜菜。\n 可能我应该让他们尝试一下。",
  "他们实际上看起来还挺好吃",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.BEET_PLANTED = {
  "看起来像甜菜",
}

-- Berries

STRINGS.NAMES.BERRIES_PICKLED = "腌浆果"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.BERRIES_PICKLED = {
  "腌浆果只是香的轻触。",
  "与奶酪搭配口味更佳！",
}

-- Cabbage

STRINGS.NAMES.CABBAGE = "白菜"
STRINGS.NAMES.CABBAGE_COOKED = "烤白菜"
STRINGS.NAMES.CABBAGE_SEEDS = "白菜种子"
STRINGS.NAMES.CABBAGE_PICKLED = "酸菜"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.CABBAGE = {
  "一个叫白菜的人发明了电脑......\n无需等待，他叫巴贝奇",
  "作为一个人的头是又大又聪明",
  "我听说，孩子们开发了周围白菜的修补程序",
  "我可以腌制这个！",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CABBAGE_COOKED = {
  "香脆可口",
  "这个很容易制作，只需要切和煮",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CABBAGE_SEEDS = {
  "这是一个种子.",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CABBAGE_PICKLED = {
  "我爷爷把酸菜放在他的巧克力蛋糕里",
  "烘烤时尝试用椰子替换酸菜。",
  "也被称为人身自由的白菜",
}

-- Carrot

STRINGS.NAMES.CARROT_PICKLED = "腌胡萝卜"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.CARROT_PICKLED = {
  "坏掉的时间比正常的胡萝卜长",
  "有些人喜欢胡萝卜，而另一些人喜欢白菜。",
}

-- Corn

STRINGS.NAMES.CORN_PICKLED = "腌玉米"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.CORN_PICKLED = {
  "坏掉的时间比正常的玉米长",
  "玉米得到了表扬时说了什么？噢，扯淡！",
}

-- Cucumber

STRINGS.NAMES.CUCUMBER = "黄瓜"
STRINGS.NAMES.CUCUMBER_COOKED = "烤黄瓜"
STRINGS.NAMES.CUCUMBER_SEEDS = "黄瓜种子"
STRINGS.NAMES.CUCUMBER_PICKLED = "腌黄瓜"
STRINGS.NAMES.CUCUMBER_GOLDEN_PICKLED = "黄金腌黄瓜"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.CUCUMBER = {
  "看起来繁琐...有一些黄瓜",
  "我敢打赌，这将成为一个好腌菜",
  "像黄瓜一样酷",
  "我要叫他拉里",
  "我可以腌制这个！",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CUCUMBER_COOKED = {
  "它现在永远不会成为一个腌菜了",
  "尝起来像开水",
  "可怜的拉里。",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CUCUMBER_SEEDS = {
  "这是一个种子。",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CUCUMBER_PICKLED = {
  "这是一个相当好的腌菜",
  "为什么黄瓜要傻笑？他们捡东西国的！",
  "如果我有一个汉堡包把它放上去就好了！",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.CUCUMBER_GOLDEN_PICKLED = {
  "一个超级特别的腌菜",
  "比普通腌菜好很多！",
}

-- Egg

STRINGS.NAMES.EGG_PICKLED = "腌蛋"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.EGG_PICKLED = {
  "我真的要吃这个？",
  "谁确定这应该是可食用的？",
}

-- Eggplant

STRINGS.NAMES.EGGPLANT_PICKLED = "腌茄子"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.EGGPLANT_PICKLED = {
  "坏掉的时间比正常的茄子长",
  "你不要偷苦茄子",
}

-- Fish

STRINGS.NAMES.FISH_PICKLED = "腌鲱鱼"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.FISH_PICKLED = {
  "哇，真刺鼻！",
  "抓住一个腌鲱鱼，把它放在你的口袋里",
  "至少它不是带泥的鱼",
}

-- Mush

STRINGS.NAMES.MUSH_PICKLED = "腌菜浓汤"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.MUSH_PICKLED = {
  "最好不要吃这个",
  "我猜是腌完了被人忘了",
}

-- Mushroom

STRINGS.NAMES.MUSHROOM_PICKLED = "腌蘑菇"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.MUSHROOM_PICKLED = {
  "菌类为什么离开派对？这儿没有蘑菇。",
  "人们为什么喜欢蘑菇先生？\n因为他是一个菌类！",
}

-- Onion

STRINGS.NAMES.ONION = "洋葱"

STRINGS.NAMES.ONION_COOKED = "洋葱圈"

STRINGS.NAMES.ONION_SEEDS = "洋葱种子"
STRINGS.NAMES.ONION_PICKLED = "腌洋葱"
STRINGS.NAMES.ONION_PLANTED = "洋葱"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.ONION = {
  "”抽泣中“ 谁在切洋葱？",
  "妖怪就像洋葱一样",
  "洋葱总是让我哭",
  "我可以腌制这个！",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.ONION_COOKED = {
  "如果你喜欢的话，你应该把洋葱圈放在上面。",
  "如果你听到一个洋葱响了，回答它。",
  "我想这使我成为洋葱圈的上帝。",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.ONION_SEEDS = {
  "这是一个种子。",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.ONION_PICKLED = {
  "什么是圆的白色的笑声？一个搔你痒的洋葱！",
  "美丽而灿烂...味道好极了！",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.ONION_PLANTED = {
  "你看，一个野生的洋葱！",
}

-- Potato

STRINGS.NAMES.POTATO = "土豆"
STRINGS.NAMES.POTATO_COOKED = "烤土豆"
STRINGS.NAMES.POTATO_PLANTED = "土豆"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.POTATO = {
  "煮熟他们，捣烂他们，把脚伸进罐子里",
  "我思故我土豆",
  "土豆就是土豆",
  "马铃薯是什么东西？",
  "什么有眼睛却看不见？\n土豆！",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.POTATO_COOKED = {
  "简单，快速，美味的土豆",
  "美味的炸土豆",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.POTATO_PLANTED = {
  "如果我要那个土豆，我需要一把铲子",
}

-- Pigs Foot

STRINGS.NAMES.PIGS_FOOT = "猪脚"
STRINGS.NAMES.PIGS_FOOT_COOKED = "猪肉皮"
STRINGS.NAMES.PIGS_FOOT_PICKLED = "腌猪脚"
STRINGS.NAMES.PIGS_FOOT_DRIED = "干猪脚"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.PIGS_FOOT = {
  "可怜的威尔伯…",
  "这只小猪不会再去市场了",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.PIGS_FOOT_COOKED = {
  "最好吃边看足球",
  "一个肉制成的松脆的点心！",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.PIGS_FOOT_PICKLED = {
  "谁认为这是一个好主意？",
  "我不认为一个饥饿的猛禽会吃这个",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.PIGS_FOOT_DRIED = {
  "好吃的猪脚干…",
}

-- Pumpkin

STRINGS.NAMES.PUMPKIN_PICKLED = "腌南瓜"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.PUMPKIN_PICKLED = {
  "彼得-辣椒腌了一大堆腌南瓜… 呃，辣椒",
  "南瓜最喜欢的运动是什么？\n壁球（南瓜）",
}

-- Radish

STRINGS.NAMES.RADISH = "萝卜"
STRINGS.NAMES.RADISH_COOKED = "烤萝卜"
STRINGS.NAMES.RADISH_SEEDS = "萝卜种子"
STRINGS.NAMES.RADISH_PICKLED = "腌萝卜"
STRINGS.NAMES.RADISH_PLANTED = "萝卜"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.RADISH = {
  "什么是小的，红色的耳语？嘶哑的萝卜！",
  "这个素食者是那么像轮子",
  "我可以腌制这个！",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.RADISH_COOKED = {
  "美味加健康",
  "比生萝卜更甜美柔软",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.RADISH_SEEDS = {
  "这是一个种子。",
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.RADISH_PICKLED = {
  "甜的，浓烈的，粉红色的",
  "这将是伟大的装饰"
}
STRINGS.CHARACTERS.GENERIC.DESCRIBE.RADISH_PLANTED = {
  "这是一个小小的野生萝卜",
}

-- Watermelon

STRINGS.NAMES.WATERMELON_PICKLED = "腌西瓜皮"

STRINGS.CHARACTERS.GENERIC.DESCRIBE.WATERMELON_PICKLED = {
  "谁想到的腌制西瓜皮？",
  "与奶酪和饼干搭配会很好吃。",
}

_G.ChinesePlus.RenameAction("PICKLEIT","腌制")
