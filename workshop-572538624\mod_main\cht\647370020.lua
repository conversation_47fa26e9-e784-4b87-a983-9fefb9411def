_G.ChinesePlus.RenameTab("<PERSON><PERSON>'s Tab","蛛侍")

STRINGS.NAMES.KATANAS = "蛛侍的刀"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.KATANAS = "一套鋒利的刀"

STRINGS.NAMES.SHIELD = "美國隊長盾牌"
STRINGS.CHARACTERS.GENERIC.DESCRIBE.SHIELD = "自動收的迴旋鏢"

_G.ChinesePlus.RenameRecipe("KATANAS","一套鋒利的刀")
_G.ChinesePlus.RenameRecipe("SILK","蛛侍產好絲。")

-- The character select screen lines
STRINGS.CHARACTER_TITLES.spidey = "蛛侍"
STRINGS.CHARACTER_NAMES.spidey = "蛛侍"
STRINGS.CHARACTER_DESCRIPTIONS.spidey = "*生命自動恢復\n*快速 (移動, 收穫, 砍樹)\n*夜視能力"
STRINGS.CHARACTER_QUOTES.spidey = "\"嘿！大家好！\""

-- Custom speech strings
--STRINGS.CHARACTERS.SPIDEY = require(chinesefolder.."/Spidey/speech_spidey")
STRINGS.CHARACTERS.SPIDEY = nil

-- The character's name as appears in-game
STRINGS.NAMES.SPIDEY = "蛛侍"

